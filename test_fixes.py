#!/usr/bin/env python3
"""
Test script to verify all the fixes are working correctly
"""

import requests
import time

def test_routes():
    """Test that all routes are accessible"""
    base_url = "http://localhost:5000"
    
    routes_to_test = [
        "/",
        "/story-generator",
        "/search",
        "/auth/login",
        "/auth/register"
    ]
    
    print("=== TESTING ROUTE ACCESSIBILITY ===")
    for route in routes_to_test:
        try:
            response = requests.get(f"{base_url}{route}", timeout=5)
            status = "✅ OK" if response.status_code == 200 else f"❌ {response.status_code}"
            print(f"{route:<20} {status}")
        except Exception as e:
            print(f"{route:<20} ❌ ERROR: {e}")
    
    print("\n=== TESTING SPECIFIC FIXES ===")
    
    # Test 1: Story generator should return a page (not None)
    try:
        response = requests.get(f"{base_url}/story-generator", timeout=5)
        if response.status_code == 200 and "Story Generator" in response.text:
            print("✅ Story generator page loads correctly")
        else:
            print(f"❌ Story generator issue: {response.status_code}")
    except Exception as e:
        print(f"❌ Story generator error: {e}")
    
    # Test 2: Homepage should load without errors
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ Homepage loads correctly")
            if "recommended" in response.text.lower() or "words" in response.text.lower():
                print("✅ Homepage contains word content")
            else:
                print("⚠️  Homepage may not show recommended words")
        else:
            print(f"❌ Homepage issue: {response.status_code}")
    except Exception as e:
        print(f"❌ Homepage error: {e}")
    
    # Test 3: Google search route should exist
    try:
        # This should redirect to Google
        response = requests.get(f"{base_url}/google-search/test", timeout=5, allow_redirects=False)
        if response.status_code in [301, 302]:
            print("✅ Google search route works (redirects)")
        else:
            print(f"❌ Google search route issue: {response.status_code}")
    except Exception as e:
        print(f"❌ Google search route error: {e}")

def test_flask_routes():
    """Test Flask route registration"""
    try:
        from app import app
        
        print("\n=== TESTING FLASK ROUTE REGISTRATION ===")
        
        required_routes = [
            'story_generator',
            'delete_library', 
            'google_search',
            'play_pronunciation'
        ]
        
        registered_endpoints = [rule.endpoint for rule in app.url_map.iter_rules()]
        
        for route in required_routes:
            if route in registered_endpoints:
                print(f"✅ {route} - REGISTERED")
            else:
                print(f"❌ {route} - MISSING")
                
        print(f"\nTotal registered routes: {len(registered_endpoints)}")
        
    except Exception as e:
        print(f"❌ Error testing Flask routes: {e}")

if __name__ == "__main__":
    print("Testing GRE Vocab Learner fixes...")
    print("Make sure the application is running on http://localhost:5000")
    print()
    
    # Wait a moment for server to be ready
    time.sleep(1)
    
    test_flask_routes()
    test_routes()
    
    print("\n=== TEST SUMMARY ===")
    print("If all tests show ✅, the fixes are working correctly!")
    print("If you see ❌, there may still be issues to address.")
