from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from models import db, User, UserLibrary, LearningSession, UserWord, SyncLog
from forms import UserForm, LibraryForm
from functools import wraps
import json
from datetime import datetime, timedelta

admin = Blueprint('admin', __name__, url_prefix='/admin')

# Admin required decorator
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin():
            flash('You need admin privileges to access this page', 'danger')
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    return decorated_function

@admin.route('/')
@login_required
@admin_required
def index():
    # Get basic stats for dashboard
    user_count = User.query.count()
    active_users = User.query.filter_by(is_active=True).count()
    
    # Get recent users
    recent_users = User.query.order_by(User.created_at.desc()).limit(5).all()
    
    # Get recent learning sessions
    recent_sessions = LearningSession.query.order_by(LearningSession.start_time.desc()).limit(5).all()
    
    # Calculate usage statistics
    now = datetime.utcnow()
    last_week = now - timedelta(days=7)
    sessions_last_week = LearningSession.query.filter(LearningSession.start_time >= last_week).count()
    
    # Get Google Drive sync stats
    sync_success = SyncLog.query.filter_by(status='success').count()
    sync_failed = SyncLog.query.filter_by(status='failed').count()
    
    return render_template(
        'admin/index.html',
        user_count=user_count,
        active_users=active_users,
        recent_users=recent_users,
        recent_sessions=recent_sessions,
        sessions_last_week=sessions_last_week,
        sync_success=sync_success,
        sync_failed=sync_failed
    )

@admin.route('/users')
@login_required
@admin_required
def users():
    users = User.query.all()
    return render_template('admin/users.html', users=users)

@admin.route('/user/<int:user_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_user(user_id):
    user = User.query.get_or_404(user_id)
    form = UserForm(obj=user)
    
    if form.validate_on_submit():
        user.username = form.username.data
        user.email = form.email.data
        user.first_name = form.first_name.data
        user.last_name = form.last_name.data
        user.role = form.role.data
        user.is_active = form.is_active.data
        
        if form.password.data:
            user.set_password(form.password.data)
        
        db.session.commit()
        flash('User updated successfully', 'success')
        return redirect(url_for('admin.users'))
    
    return render_template('admin/edit_user.html', form=form, user=user)

@admin.route('/user/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_user():
    form = UserForm()
    
    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=form.email.data,
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            role=form.role.data,
            is_active=form.is_active.data
        )
        
        if form.password.data:
            user.set_password(form.password.data)
        else:
            user.set_password('changeme')  # Default password
        
        db.session.add(user)
        db.session.commit()
        flash('User created successfully', 'success')
        return redirect(url_for('admin.users'))
    
    return render_template('admin/create_user.html', form=form)

@admin.route('/user/delete/<int:user_id>', methods=['POST'])
@login_required
@admin_required
def delete_user(user_id):
    user = User.query.get_or_404(user_id)
    
    if user.id == current_user.id:
        flash('You