{% extends "base.html" %}

{% block title %}Change Password - GRE Vocabulary Learner{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">Change Password</h3>
                </div>
                <div class="card-body">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category }}">{{ message }}</div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    
                    <form method="post" action="{{ url_for('auth.change_password') }}">
                        <div class="mb-3">
                            <label for="current_password" class="form-label">Current Password</label>
                            <input type="password" class="form-control {% if errors.get('current_password') %}is-invalid{% endif %}" 
                                   id="current_password" name="current_password" required>
                            {% if errors.get('current_password') %}
                                <div class="invalid-feedback">{{ errors.get('current_password') }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="new_password" class="form-label">New Password</label>
                            <input type="password" class="form-control {% if errors.get('new_password') %}is-invalid{% endif %}" 
                                   id="new_password" name="new_password" required>
                            {% if errors.get('new_password') %}
                                <div class="invalid-feedback">{{ errors.get('new_password') }}</div>
                            {% endif %}
                            <div class="form-text">Minimum 6 characters</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirm New Password</label>
                            <input type="password" class="form-control {% if errors.get('confirm_password') %}is-invalid{% endif %}" 
                                   id="confirm_password" name="confirm_password" required>
                            {% if errors.get('confirm_password') %}
                                <div class="invalid-feedback">{{ errors.get('confirm_password') }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Change Password</button>
                        </div>
                    </form>
                    
                    <div class="mt-3 text-center">
                        <a href="{{ url_for('auth.profile') }}" class="btn btn-link">Back to Profile</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
