"""
This script patches Werkzeug to provide compatibility functions that have been removed in newer versions.
Run this script before importing Flask or any Flask extensions.
"""
import sys
import types
import secrets
import string

# Create a mock module for werkzeug.security with missing functions
class MockSecurity:
    def safe_str_cmp(a, b):
        """
        Replacement for werkzeug.security.safe_str_cmp which was removed in newer versions.
        This function performs a constant-time comparison of two strings.
        """
        if len(a) != len(b):
            return False
        
        result = 0
        for x, y in zip(a, b):
            result |= ord(x) ^ ord(y) if isinstance(x, str) else x ^ y
        
        return result == 0
    
    def gen_salt(length):
        """
        Replacement for werkzeug.security.gen_salt which was removed in newer versions.
        Generate a random string of ALPHANUMERICS of length 'length'.
        """
        alphabet = string.ascii_letters + string.digits
        return ''.join(secrets.choice(alphabet) for _ in range(length))

# Create the module
mock_security = types.ModuleType('werkzeug.security')
mock_security.safe_str_cmp = MockSecurity.safe_str_cmp
mock_security.gen_salt = MockSecurity.gen_salt

# Add other functions from werkzeug.security to our mock
try:
    from werkzeug.security import generate_password_hash, check_password_hash
    mock_security.generate_password_hash = generate_password_hash
    mock_security.check_password_hash = check_password_hash
except ImportError:
    # Provide fallback implementations if needed
    def generate_password_hash(password, method='pbkdf2:sha256', salt_length=16):
        """Hash a password with the given method and salt length."""
        import hashlib
        import base64
        salt = MockSecurity.gen_salt(salt_length)
        hash_val = hashlib.pbkdf2_hmac(
            'sha256', 
            password.encode('utf-8'), 
            salt.encode('utf-8'), 
            100000
        )
        return f"pbkdf2:sha256:{salt_length}${salt}${base64.b64encode(hash_val).decode('ascii')}"
    
    def check_password_hash(pwhash, password):
        """Check a password against a given hash value."""
        import hashlib
        import base64
        if pwhash.count('$') < 2:
            return False
        method, salt, hashval = pwhash.split('$', 2)
        if method.startswith('pbkdf2:sha256:'):
            import hashlib
            hash_val = hashlib.pbkdf2_hmac(
                'sha256', 
                password.encode('utf-8'), 
                salt.encode('utf-8'), 
                100000
            )
            return base64.b64encode(hash_val).decode('ascii') == hashval
        return False
    
    mock_security.generate_password_hash = generate_password_hash
    mock_security.check_password_hash = check_password_hash

# Patch sys.modules
sys.modules['werkzeug.security'] = mock_security

# Create a mock debug module
debug_module = types.ModuleType('werkzeug.debug')
debug_module.gen_salt = MockSecurity.gen_salt

# Add a mock DebuggedApplication class
class MockDebuggedApplication:
    def __init__(self, app, *args, **kwargs):
        self.app = app
    
    def __call__(self, environ, start_response):
        return self.app(environ, start_response)

debug_module.DebuggedApplication = MockDebuggedApplication
sys.modules['werkzeug.debug'] = debug_module

# Also patch werkzeug.debug.__init__ module
debug_init_module = types.ModuleType('werkzeug.debug.__init__')
debug_init_module.gen_salt = MockSecurity.gen_salt
debug_init_module.DebuggedApplication = MockDebuggedApplication
sys.modules['werkzeug.debug.__init__'] = debug_init_module

print("Patched werkzeug.security and werkzeug.debug with compatibility functions")
