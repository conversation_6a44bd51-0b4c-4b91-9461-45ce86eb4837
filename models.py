from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash, check_password_hash
import secrets

# Optional imports for 2FA functionality
try:
    import pyotp
    import qrcode
    import io
    import base64
    TOTP_AVAILABLE = True
except ImportError:
    print("Warning: pyotp and qrcode not available. Two-factor authentication will be disabled.")
    TOTP_AVAILABLE = False

# Initialize SQLAlchemy
db = SQLAlchemy()

class User(db.Model):
    __tablename__ = 'user'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(20), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    first_name = db.Column(db.String(50))
    last_name = db.Column(db.String(50))
    profile_picture = db.Column(db.String(200))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    role = db.Column(db.String(20), default='student')  # Options: admin, teacher, student, guest

    # Security enhancements
    is_active = db.Column(db.Boolean, default=True)
    email_verified = db.Column(db.Boolean, default=False)
    failed_login_attempts = db.Column(db.Integer, default=0)
    locked_until = db.Column(db.DateTime)

    # Two-Factor Authentication
    totp_secret = db.Column(db.String(32))
    two_factor_enabled = db.Column(db.Boolean, default=False)
    backup_codes = db.Column(db.Text)  # JSON string of backup codes

    # Session management
    session_token = db.Column(db.String(255))
    session_expires = db.Column(db.DateTime)

    def get_full_name(self):
        """Return the user's full name or username if not available"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        else:
            return self.username

    def is_admin(self):
        """Check if the user has admin role"""
        return self.role == 'admin'

    def is_teacher(self):
        """Check if the user has teacher role"""
        return self.role == 'teacher' or self.role == 'admin'

    # Password management
    def set_password(self, password):
        """Set password hash"""
        self.password_hash = generate_password_hash(password, method='pbkdf2:sha256', salt_length=16)

    def check_password(self, password):
        """Check password against hash"""
        return check_password_hash(self.password_hash, password)

    # Account security
    def is_account_locked(self):
        """Check if account is locked due to failed login attempts"""
        if self.locked_until and self.locked_until > datetime.utcnow():
            return True
        return False

    def lock_account(self, duration_minutes=30):
        """Lock account for specified duration"""
        self.locked_until = datetime.utcnow() + timedelta(minutes=duration_minutes)
        db.session.commit()

    def unlock_account(self):
        """Unlock account and reset failed attempts"""
        self.failed_login_attempts = 0
        self.locked_until = None
        db.session.commit()

    def increment_failed_login(self):
        """Increment failed login attempts and lock if threshold reached"""
        self.failed_login_attempts += 1
        if self.failed_login_attempts >= 5:  # Lock after 5 failed attempts
            self.lock_account()
        db.session.commit()

    # Two-Factor Authentication
    def generate_totp_secret(self):
        """Generate TOTP secret for 2FA"""
        if not TOTP_AVAILABLE:
            return None
        if not self.totp_secret:
            self.totp_secret = pyotp.random_base32()
            db.session.commit()
        return self.totp_secret

    def get_totp_uri(self, app_name="GRE Vocabulary App"):
        """Get TOTP URI for QR code generation"""
        if not TOTP_AVAILABLE:
            return None
        if not self.totp_secret:
            self.generate_totp_secret()
        return pyotp.totp.TOTP(self.totp_secret).provisioning_uri(
            name=self.email,
            issuer_name=app_name
        )

    def verify_totp(self, token):
        """Verify TOTP token"""
        if not TOTP_AVAILABLE or not self.totp_secret:
            return False
        totp = pyotp.TOTP(self.totp_secret)
        return totp.verify(token, valid_window=1)

    def generate_backup_codes(self):
        """Generate backup codes for 2FA"""
        import json
        codes = [secrets.token_hex(4).upper() for _ in range(10)]
        self.backup_codes = json.dumps(codes)
        db.session.commit()
        return codes

    def verify_backup_code(self, code):
        """Verify and consume backup code"""
        if not self.backup_codes:
            return False

        import json
        codes = json.loads(self.backup_codes)
        if code.upper() in codes:
            codes.remove(code.upper())
            self.backup_codes = json.dumps(codes)
            db.session.commit()
            return True
        return False

    # Session management
    def generate_session_token(self):
        """Generate secure session token"""
        self.session_token = secrets.token_urlsafe(32)
        self.session_expires = datetime.utcnow() + timedelta(hours=24)
        db.session.commit()
        return self.session_token

    def is_session_valid(self, token):
        """Check if session token is valid"""
        return (self.session_token == token and
                self.session_expires and
                self.session_expires > datetime.utcnow())

    def __repr__(self):
        return f'<User {self.username}>'

# Add more models as needed for your application
class UserProgress(db.Model):
    __tablename__ = 'user_progress'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    word = db.Column(db.String(100), nullable=False)
    status = db.Column(db.String(20), default='learning')  # Options: learning, learned, difficult
    last_reviewed = db.Column(db.DateTime, default=datetime.now)
    review_count = db.Column(db.Integer, default=0)

    # Define relationship
    user = db.relationship('User', backref=db.backref('progress', lazy=True))

    def __repr__(self):
        return f'<UserProgress {self.user.username} - {self.word}>'

class LearningSession(db.Model):
    __tablename__ = 'learning_sessions'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    start_time = db.Column(db.DateTime, default=datetime.utcnow)
    end_time = db.Column(db.DateTime)
    words_reviewed = db.Column(db.Integer, default=0)
    words_learned = db.Column(db.Integer, default=0)
    session_type = db.Column(db.String(50), default='vocabulary')  # vocabulary, study, timer

    # Define relationship
    user = db.relationship('User', backref=db.backref('sessions', lazy=True))

    def __repr__(self):
        return f'<LearningSession {self.user.username} - {self.start_time}>'

# Study Room and Timer Models
class StudySession(db.Model):
    __tablename__ = 'study_sessions'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    planned_duration = db.Column(db.Integer)  # in minutes
    actual_duration = db.Column(db.Integer)  # in minutes
    start_time = db.Column(db.DateTime, default=datetime.utcnow)
    end_time = db.Column(db.DateTime)
    status = db.Column(db.String(20), default='active')  # active, paused, completed, cancelled
    background_audio = db.Column(db.String(500))  # YouTube URL or audio file
    background_theme = db.Column(db.String(100), default='default')

    # Define relationship
    user = db.relationship('User', backref=db.backref('study_sessions', lazy=True))

    def __repr__(self):
        return f'<StudySession {self.title} - {self.user.username}>'

class Task(db.Model):
    __tablename__ = 'tasks'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    study_session_id = db.Column(db.Integer, db.ForeignKey('study_sessions.id'))
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    estimated_time = db.Column(db.Integer)  # in minutes
    actual_time = db.Column(db.Integer)  # in minutes
    priority = db.Column(db.String(20), default='medium')  # low, medium, high
    status = db.Column(db.String(20), default='pending')  # pending, in_progress, completed, cancelled
    due_date = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime)

    # Define relationships
    user = db.relationship('User', backref=db.backref('tasks', lazy=True))
    study_session = db.relationship('StudySession', backref=db.backref('tasks', lazy=True))

    def __repr__(self):
        return f'<Task {self.title} - {self.user.username}>'

# Library and Vocabulary Models
class Library(db.Model):
    __tablename__ = 'libraries'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    is_public = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Define relationship
    user = db.relationship('User', backref=db.backref('libraries', lazy=True))

    def __repr__(self):
        return f'<Library {self.name} - {self.user.username}>'

class Word(db.Model):
    __tablename__ = 'words'

    id = db.Column(db.Integer, primary_key=True)
    word = db.Column(db.String(100), unique=True, nullable=False, index=True)
    definition = db.Column(db.Text, nullable=False)
    pronunciation = db.Column(db.String(200))
    part_of_speech = db.Column(db.String(50))
    difficulty_level = db.Column(db.Integer, default=1)  # 1-5 scale
    frequency_rank = db.Column(db.Integer)  # GRE frequency ranking
    source = db.Column(db.String(100))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Word {self.word}>'

class WordExample(db.Model):
    __tablename__ = 'word_examples'

    id = db.Column(db.Integer, primary_key=True)
    word_id = db.Column(db.Integer, db.ForeignKey('words.id'), nullable=False)
    example_sentence = db.Column(db.Text, nullable=False)
    source = db.Column(db.String(200))

    # Define relationship
    word = db.relationship('Word', backref=db.backref('examples', lazy=True))

    def __repr__(self):
        return f'<WordExample for {self.word.word}>'




