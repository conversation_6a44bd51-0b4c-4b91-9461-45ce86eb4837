#!/usr/bin/env python3
"""
Database migration script for GRE Vocabulary App enhancements
This script safely migrates the existing database to support new features.
"""

import os
import sys
import sqlite3
import shutil
from datetime import datetime
from werkzeug.security import generate_password_hash

def backup_database(db_path):
    """Create a backup of the existing database"""
    if not os.path.exists(db_path):
        print(f"Database file {db_path} not found. Creating new database.")
        return False
    
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(db_path, backup_path)
    print(f"Database backed up to: {backup_path}")
    return True

def migrate_users_table(cursor):
    """Migrate users table to add new security fields"""
    print("Migrating users table...")
    
    # Check if new columns already exist
    cursor.execute("PRAGMA table_info(users)")
    columns = [column[1] for column in cursor.fetchall()]
    
    migrations = [
        ("is_active", "ALTER TABLE users ADD COLUMN is_active BOOLEAN DEFAULT 1"),
        ("email_verified", "ALTER TABLE users ADD COLUMN email_verified BOOLEAN DEFAULT 0"),
        ("failed_login_attempts", "ALTER TABLE users ADD COLUMN failed_login_attempts INTEGER DEFAULT 0"),
        ("locked_until", "ALTER TABLE users ADD COLUMN locked_until DATETIME"),
        ("totp_secret", "ALTER TABLE users ADD COLUMN totp_secret VARCHAR(32)"),
        ("two_factor_enabled", "ALTER TABLE users ADD COLUMN two_factor_enabled BOOLEAN DEFAULT 0"),
        ("backup_codes", "ALTER TABLE users ADD COLUMN backup_codes TEXT"),
        ("session_token", "ALTER TABLE users ADD COLUMN session_token VARCHAR(255)"),
        ("session_expires", "ALTER TABLE users ADD COLUMN session_expires DATETIME"),
        ("password_hash", "ALTER TABLE users ADD COLUMN password_hash VARCHAR(255)")
    ]
    
    for column_name, sql in migrations:
        if column_name not in columns:
            try:
                cursor.execute(sql)
                print(f"  ✓ Added column: {column_name}")
            except sqlite3.Error as e:
                print(f"  ✗ Error adding column {column_name}: {e}")
        else:
            print(f"  - Column {column_name} already exists")
    
    # Migrate password field to password_hash if needed
    if "password" in columns and "password_hash" in columns:
        cursor.execute("SELECT id, password FROM users WHERE password_hash IS NULL")
        users_to_migrate = cursor.fetchall()
        
        for user_id, old_password in users_to_migrate:
            # If the password is already hashed (starts with pbkdf2), copy it
            # Otherwise, hash it properly
            if old_password and old_password.startswith('pbkdf2'):
                cursor.execute("UPDATE users SET password_hash = ? WHERE id = ?", 
                             (old_password, user_id))
            elif old_password:
                # This is a plain text password, hash it
                new_hash = generate_password_hash(old_password)
                cursor.execute("UPDATE users SET password_hash = ? WHERE id = ?", 
                             (new_hash, user_id))
            
        print(f"  ✓ Migrated passwords for {len(users_to_migrate)} users")

def create_new_tables(cursor):
    """Create new tables for enhanced functionality"""
    print("Creating new tables...")
    
    # Study Sessions table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS study_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            title VARCHAR(200) NOT NULL,
            description TEXT,
            planned_duration INTEGER,
            actual_duration INTEGER,
            start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
            end_time DATETIME,
            status VARCHAR(20) DEFAULT 'active',
            background_audio VARCHAR(500),
            background_theme VARCHAR(100) DEFAULT 'default',
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    """)
    print("  ✓ Created study_sessions table")
    
    # Tasks table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS tasks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            study_session_id INTEGER,
            title VARCHAR(200) NOT NULL,
            description TEXT,
            estimated_time INTEGER,
            actual_time INTEGER,
            priority VARCHAR(20) DEFAULT 'medium',
            status VARCHAR(20) DEFAULT 'pending',
            due_date DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            completed_at DATETIME,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (study_session_id) REFERENCES study_sessions (id)
        )
    """)
    print("  ✓ Created tasks table")
    
    # Libraries table (if not exists)
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS libraries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(200) NOT NULL,
            description TEXT,
            user_id INTEGER NOT NULL,
            is_public BOOLEAN DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    """)
    print("  ✓ Created/verified libraries table")
    
    # Words table (enhanced)
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS words (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            word VARCHAR(100) UNIQUE NOT NULL,
            definition TEXT NOT NULL,
            pronunciation VARCHAR(200),
            part_of_speech VARCHAR(50),
            difficulty_level INTEGER DEFAULT 1,
            frequency_rank INTEGER,
            source VARCHAR(100),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    """)
    print("  ✓ Created/verified words table")
    
    # Word Examples table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS word_examples (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            word_id INTEGER NOT NULL,
            example_sentence TEXT NOT NULL,
            source VARCHAR(200),
            FOREIGN KEY (word_id) REFERENCES words (id)
        )
    """)
    print("  ✓ Created word_examples table")

def create_indexes(cursor):
    """Create database indexes for better performance"""
    print("Creating database indexes...")
    
    indexes = [
        ("idx_users_username", "CREATE INDEX IF NOT EXISTS idx_users_username ON users (username)"),
        ("idx_users_email", "CREATE INDEX IF NOT EXISTS idx_users_email ON users (email)"),
        ("idx_users_session_token", "CREATE INDEX IF NOT EXISTS idx_users_session_token ON users (session_token)"),
        ("idx_study_sessions_user", "CREATE INDEX IF NOT EXISTS idx_study_sessions_user ON study_sessions (user_id)"),
        ("idx_tasks_user", "CREATE INDEX IF NOT EXISTS idx_tasks_user ON tasks (user_id)"),
        ("idx_tasks_session", "CREATE INDEX IF NOT EXISTS idx_tasks_session ON tasks (study_session_id)"),
        ("idx_words_word", "CREATE INDEX IF NOT EXISTS idx_words_word ON words (word)"),
        ("idx_word_examples_word", "CREATE INDEX IF NOT EXISTS idx_word_examples_word ON word_examples (word_id)")
    ]
    
    for index_name, sql in indexes:
        try:
            cursor.execute(sql)
            print(f"  ✓ Created index: {index_name}")
        except sqlite3.Error as e:
            print(f"  ✗ Error creating index {index_name}: {e}")

def update_learning_sessions(cursor):
    """Update learning_sessions table with new fields"""
    print("Updating learning_sessions table...")
    
    cursor.execute("PRAGMA table_info(learning_sessions)")
    columns = [column[1] for column in cursor.fetchall()]
    
    if "words_learned" not in columns:
        try:
            cursor.execute("ALTER TABLE learning_sessions ADD COLUMN words_learned INTEGER DEFAULT 0")
            print("  ✓ Added words_learned column")
        except sqlite3.Error as e:
            print(f"  ✗ Error adding words_learned column: {e}")
    
    if "session_type" not in columns:
        try:
            cursor.execute("ALTER TABLE learning_sessions ADD COLUMN session_type VARCHAR(50) DEFAULT 'vocabulary'")
            print("  ✓ Added session_type column")
        except sqlite3.Error as e:
            print(f"  ✗ Error adding session_type column: {e}")

def verify_migration(cursor):
    """Verify that the migration was successful"""
    print("Verifying migration...")
    
    # Check that all expected tables exist
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [table[0] for table in cursor.fetchall()]
    
    expected_tables = [
        'users', 'learning_sessions', 'study_sessions', 'tasks', 
        'libraries', 'words', 'word_examples'
    ]
    
    missing_tables = [table for table in expected_tables if table not in tables]
    if missing_tables:
        print(f"  ✗ Missing tables: {missing_tables}")
        return False
    else:
        print("  ✓ All expected tables exist")
    
    # Check users table has new columns
    cursor.execute("PRAGMA table_info(users)")
    user_columns = [column[1] for column in cursor.fetchall()]
    
    expected_user_columns = [
        'is_active', 'email_verified', 'failed_login_attempts', 
        'totp_secret', 'two_factor_enabled', 'password_hash'
    ]
    
    missing_columns = [col for col in expected_user_columns if col not in user_columns]
    if missing_columns:
        print(f"  ✗ Missing user columns: {missing_columns}")
        return False
    else:
        print("  ✓ All expected user columns exist")
    
    return True

def main():
    """Main migration function"""
    print("GRE Vocabulary App Database Migration")
    print("=" * 40)
    
    # Determine database path
    db_path = os.environ.get('DATABASE_URL', 'sqlite:///instance/users.db')
    if db_path.startswith('sqlite:///'):
        db_path = db_path[10:]  # Remove 'sqlite:///' prefix
    
    # Ensure directory exists
    os.makedirs(os.path.dirname(db_path) if os.path.dirname(db_path) else '.', exist_ok=True)
    
    # Backup existing database
    backup_created = backup_database(db_path)
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Enable foreign keys
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # Perform migrations
        migrate_users_table(cursor)
        create_new_tables(cursor)
        update_learning_sessions(cursor)
        create_indexes(cursor)
        
        # Commit changes
        conn.commit()
        
        # Verify migration
        if verify_migration(cursor):
            print("\n✓ Migration completed successfully!")
            print("Your database has been updated with all new features.")
            if backup_created:
                print(f"Original database backed up for safety.")
        else:
            print("\n✗ Migration verification failed!")
            print("Please check the errors above and try again.")
            return 1
            
    except Exception as e:
        print(f"\n✗ Migration failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        if 'conn' in locals():
            conn.close()
    
    print("\nNext steps:")
    print("1. Install new dependencies: pip install -r requirements.txt")
    print("2. Set environment variables (see IMPROVEMENTS.md)")
    print("3. Restart your application")
    print("4. Test the new features!")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
