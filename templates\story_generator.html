{% extends 'base.html' %}

{% block title %}Story Generator{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h1 class="mb-0">
                <i class="bi bi-book"></i> Vocabulary Story Generator
            </h1>
        </div>
        <div class="card-body">
            <p class="lead">
                Generate a story that incorporates vocabulary words to help you learn them in context.
            </p>

            <form method="post" action="{{ url_for('story_generator') }}">
                <div class="mb-4">
                    <h4>1. Select Words</h4>
                    <div class="card">
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Word Source</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="word_source" id="sourceRandom" value="random">
                                    <label class="form-check-label" for="sourceRandom">
                                        Random words
                                    </label>
                                </div>

                                {% if libraries %}
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="word_source" id="sourceLibrary" value="library" {% if library_id %}checked{% endif %}>
                                        <label class="form-check-label" for="sourceLibrary">
                                            Words from a library
                                        </label>
                                    </div>

                                    <div class="ms-4 mb-3" id="librarySelectContainer" style="display: {% if library_id %}block{% else %}none{% endif %};">
                                        <select class="form-select" name="library_id" id="librarySelect">
                                            <option value="" selected disabled>Select a library</option>
                                            {% for library in libraries %}
                                                <option value="{{ library.id }}" {% if library.id == library_id|int %}selected{% endif %}>
                                                    {{ library.name }} ({{ library.word_count }} words)
                                                </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                {% endif %}

                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="word_source" id="sourceCustom" value="custom" {% if not library_id %}checked{% endif %}>
                                    <label class="form-check-label" for="sourceCustom">
                                        Specific words
                                    </label>
                                </div>

                                <div class="ms-4 mb-3" id="customWordsContainer" style="display: {% if not library_id %}block{% else %}none{% endif %};">
                                    <textarea class="form-control" name="word_list" id="customWordList" rows="3" placeholder="Enter words separated by commas"></textarea>
                                    <div class="form-text">Enter words separated by commas</div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="wordCount" class="form-label">Number of Words to Include</label>
                                <input type="range" class="form-range" min="3" max="10" value="5" id="wordCount" name="word_count" oninput="updateWordCountLabel()">
                                <div class="text-center" id="wordCountLabel">5 words</div>
                                <div class="form-text">More words will create a longer, more complex story</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <h4>2. Story Settings</h4>
                    <div class="card">
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="theme" class="form-label">Theme</label>
                                <select class="form-select" id="theme" name="theme">
                                    <option value="adventure">Adventure</option>
                                    <option value="mystery">Mystery</option>
                                    <option value="fantasy">Fantasy</option>
                                    <option value="science fiction">Science Fiction</option>
                                    <option value="historical">Historical</option>
                                    <option value="comedy">Comedy</option>
                                    <option value="romance">Romance</option>
                                    <option value="horror">Horror</option>
                                    <option value="educational">Educational</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="characters" class="form-label">Characters (Optional)</label>
                                <input type="text" class="form-control" id="characters" name="characters" placeholder="e.g., a detective, a scientist, a talking cat">
                            </div>

                            <div class="mb-3">
                                <label for="scenario" class="form-label">Scenario (Optional)</label>
                                <input type="text" class="form-control" id="scenario" name="scenario" placeholder="e.g., a mysterious island, a haunted house, a space station">
                            </div>

                            <div class="mb-3">
                                <label for="modelPreference" class="form-label">AI Model</label>
                                <select class="form-select" id="modelPreference" name="model_preference">
                                    <option value="auto">Auto-select best available</option>
                                    <option value="gemini">Gemini (Google)</option>
                                    <option value="phi">Phi-3 Mini (Microsoft)</option>
                                    <option value="lmstudio">LM Studio (Local)</option>
                                </select>
                                <div class="form-text">Different models may produce different quality stories</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="bi bi-magic"></i> Generate Story
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Show/hide containers based on radio selection
    document.querySelectorAll('input[name="word_source"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const libraryContainer = document.getElementById('librarySelectContainer');
            const customContainer = document.getElementById('customWordsContainer');

            if (this.value === 'library') {
                libraryContainer.style.display = 'block';
                customContainer.style.display = 'none';
            } else if (this.value === 'custom') {
                libraryContainer.style.display = 'none';
                customContainer.style.display = 'block';
            } else {
                libraryContainer.style.display = 'none';
                customContainer.style.display = 'none';
            }
        });
    });

    // Update word count label
    function updateWordCountLabel() {
        const count = document.getElementById('wordCount').value;
        document.getElementById('wordCountLabel').textContent = count + ' words';
    }

    // Initialize with URL parameters if present
    document.addEventListener('DOMContentLoaded', function() {
        updateWordCountLabel();
    });
</script>
{% endblock %}
