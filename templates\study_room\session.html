{% extends 'base.html' %}

{% block title %}Study Session{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h2 class="mb-0">Study Session</h2>
                    <div class="session-controls">
                        <button id="pauseBtn" class="btn btn-warning btn-sm">
                            <i class="bi bi-pause"></i> Pause
                        </button>
                        <button id="resumeBtn" class="btn btn-success btn-sm" style="display: none;">
                            <i class="bi bi-play"></i> Resume
                        </button>
                        <button id="completeBtn" class="btn btn-danger btn-sm">
                            <i class="bi bi-stop"></i> Complete
                        </button>
                    </div>
                </div>

                <div class="card-body">
                    {% if session %}
                    <div class="session-info mb-4">
                        <h4>{{ session.title }}</h4>
                        {% if session.description %}
                        <p class="text-muted">{{ session.description }}</p>
                        {% endif %}

                        <div class="row">
                            <div class="col-md-6">
                                <strong>Duration:</strong> {{ session.planned_duration or 25 }} minutes
                            </div>
                            <div class="col-md-6">
                                <strong>Status:</strong>
                                <span class="badge bg-{{ 'success' if session.status == 'completed' else 'primary' }}">
                                    {{ session.status.title() }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Timer Display -->
                    <div class="timer-display text-center mb-4">
                        <div class="display-4" id="timerDisplay">{{ session.planned_duration or 25 }}:00</div>
                        <div class="progress mt-2">
                            <div class="progress-bar" id="timerProgress" role="progressbar" style="width: 0%;"
                                 aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                            </div>
                        </div>
                    </div>

                    <!-- Tasks Section -->
                    {% if tasks %}
                    <div class="tasks-section">
                        <h5>Session Tasks</h5>
                        <div class="list-group">
                            {% for task in tasks %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ task.title }}</h6>
                                    {% if task.description %}
                                    <p class="mb-1 text-muted">{{ task.description }}</p>
                                    {% endif %}
                                    {% if task.estimated_time %}
                                    <small class="text-muted">Estimated: {{ task.estimated_time }} minutes</small>
                                    {% endif %}
                                </div>
                                <div>
                                    <span class="badge bg-{{ 'success' if task.status == 'completed' else 'secondary' }}">
                                        {{ task.status.title() }}
                                    </span>
                                    {% if task.status != 'completed' %}
                                    <button class="btn btn-sm btn-outline-success ms-2"
                                            onclick="completeTask({{ task.id }})">
                                        <i class="bi bi-check"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Background Audio -->
                    {% if youtube_embed_url %}
                    <div class="audio-section mt-4">
                        <h5>Background Audio</h5>
                        <div class="ratio ratio-16x9">
                            <iframe src="{{ youtube_embed_url }}"
                                    title="Background Audio"
                                    allowfullscreen>
                            </iframe>
                        </div>
                    </div>
                    {% endif %}

                    {% else %}
                    <div class="alert alert-warning">
                        Session not found or access denied.
                    </div>
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('study_room.index') }}" class="btn btn-primary">
                            <i class="bi bi-arrow-left"></i> Back to Study Room
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let sessionId = {{ session.id if session else 'null' }};
    let timerInterval;
    let isPaused = false;
    let timeRemaining = {{ (session.planned_duration or 25) * 60 if session else 0 }}; // Convert to seconds
    let totalTime = {{ (session.planned_duration or 25) * 60 if session else 0 }};

    document.addEventListener('DOMContentLoaded', function() {
        if (sessionId) {
            startTimer();
        }

        // Pause button
        document.getElementById('pauseBtn').addEventListener('click', function() {
            pauseSession();
        });

        // Resume button
        document.getElementById('resumeBtn').addEventListener('click', function() {
            resumeSession();
        });

        // Complete button
        document.getElementById('completeBtn').addEventListener('click', function() {
            completeSession();
        });
    });

    function startTimer() {
        timerInterval = setInterval(function() {
            if (!isPaused && timeRemaining > 0) {
                timeRemaining--;
                updateTimerDisplay();
                updateProgress();

                if (timeRemaining <= 0) {
                    completeSession();
                }
            }
        }, 1000);
    }

    function updateTimerDisplay() {
        const minutes = Math.floor(timeRemaining / 60);
        const seconds = timeRemaining % 60;
        document.getElementById('timerDisplay').textContent =
            `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    function updateProgress() {
        const progress = ((totalTime - timeRemaining) / totalTime) * 100;
        document.getElementById('timerProgress').style.width = progress + '%';
        document.getElementById('timerProgress').setAttribute('aria-valuenow', progress);
    }

    function pauseSession() {
        isPaused = true;
        document.getElementById('pauseBtn').style.display = 'none';
        document.getElementById('resumeBtn').style.display = 'inline-block';

        fetch(`/study/session/${sessionId}/pause`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
    }

    function resumeSession() {
        isPaused = false;
        document.getElementById('pauseBtn').style.display = 'inline-block';
        document.getElementById('resumeBtn').style.display = 'none';

        fetch(`/study/session/${sessionId}/resume`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
    }

    function completeSession() {
        clearInterval(timerInterval);
        const actualDuration = Math.floor((totalTime - timeRemaining) / 60);

        fetch(`/study/session/${sessionId}/complete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                actual_duration: actualDuration
            })
        }).then(response => response.json())
          .then(data => {
              if (data.success) {
                  window.location.href = '/study/';
              }
          });
    }

    function completeTask(taskId) {
        fetch(`/study/tasks/${taskId}/update`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                status: 'completed'
            })
        }).then(response => response.json())
          .then(data => {
              if (data.success) {
                  location.reload();
              }
          });
    }
</script>
{% endblock %}
