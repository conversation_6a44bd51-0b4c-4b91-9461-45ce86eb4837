import os
import gc
import json
import time
import threading
import uvic<PERSON>
from typing import List, Dict, Optional
from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel
from concurrent.futures import ThreadPoolExecutor
import requests

# --- Configuration ---
# LM Studio API endpoint
LM_STUDIO_API_URL = "http://127.0.0.1:1234/v1"
NUM_THREADS = os.cpu_count() or 4
print(f"Using {NUM_THREADS} CPU threads for processing")

# --- Global Variables ---
executor = ThreadPoolExecutor(max_workers=2)  # For parallel processing
model_info = None

# --- FastAPI App Initialization ---
app = FastAPI(
    title="LM Studio API Server",
    description="A simple API to interact with LM Studio models with optimized performance.",
    version="0.1.0"
)

# --- Pydantic Models for Request Body ---
class PromptRequest(BaseModel):
    prompt: str
    max_new_tokens: int = 256  # Max tokens to generate
    temperature: float = 0.7
    top_p: float = 0.95
    top_k: int = 40
    do_sample: bool = True

class StoryRequest(BaseModel):
    words: List[str]  # List of words to include in the story
    theme: str = "adventure"  # Theme of the story
    characters: str = ""  # Optional characters to include
    scenario: str = ""  # Optional scenario to include
    max_new_tokens: int = 1024  # Stories need more tokens
    temperature: float = 0.8  # Slightly more creative for stories
    do_sample: bool = True

# --- Event Handlers for Model Loading/Unloading ---
@app.on_event("startup")
async def startup_event():
    global model_info
    print("Checking LM Studio API...")
    try:
        # Check if LM Studio is running and get model info
        response = requests.get(f"{LM_STUDIO_API_URL}/models")
        if response.status_code == 200:
            models_data = response.json()
            if models_data:
                model_info = models_data
                print(f"LM Studio is running with model: {models_data.get('id', 'Unknown')}")
            else:
                print("LM Studio is running but no model information is available")
        else:
            print(f"Error connecting to LM Studio API: {response.status_code}")
    except Exception as e:
        print(f"Error checking LM Studio API: {e}")
        print("Make sure LM Studio is running on http://127.0.0.1:1234")

@app.on_event("shutdown")
async def shutdown_event():
    global executor
    print("Releasing resources...")
    executor.shutdown(wait=True)
    gc.collect()
    print("Resources released.")

# --- Helper Functions ---
def generate_text_with_lmstudio(prompt, gen_config):
    """Helper function to generate text with LM Studio API"""
    try:
        # Prepare the request payload for LM Studio API
        payload = {
            "prompt": prompt,
            "temperature": gen_config.get("temperature", 0.7),
            "top_p": gen_config.get("top_p", 0.95),
            "top_k": gen_config.get("top_k", 40),
            "max_tokens": gen_config.get("max_new_tokens", 256),
            "stream": False
        }
        
        # Make the request to LM Studio API
        response = requests.post(
            f"{LM_STUDIO_API_URL}/completions", 
            json=payload,
            timeout=120  # Longer timeout for story generation
        )
        
        if response.status_code == 200:
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["text"], None
            else:
                return None, "No completion choices returned from LM Studio"
        else:
            return None, f"LM Studio API error: {response.status_code} - {response.text}"
    except Exception as e:
        return None, str(e)

# --- API Endpoint ---
@app.post("/generate")
async def generate_text(request: PromptRequest):
    if not model_info:
        raise HTTPException(status_code=503, detail="LM Studio API is not available or no model is loaded")

    # Configure generation parameters
    gen_config = {
        "temperature": request.temperature,
        "top_p": request.top_p,
        "top_k": request.top_k,
        "max_new_tokens": request.max_new_tokens,
    }

    print(f"Generating response for prompt: '{request.prompt[:50]}...' with temperature: {request.temperature}")

    try:
        # Submit the generation task to the thread pool
        future = executor.submit(generate_text_with_lmstudio, request.prompt, gen_config)
        response_text, error = future.result()
        
        if error:
            raise Exception(error)

        if not response_text:
            response_text = "Could not generate response."

        print(f"Generated response: '{response_text[:100]}...'")
        return {"prompt": request.prompt, "response": response_text.strip()}

    except Exception as e:
        print(f"Error during generation: {e}")
        raise HTTPException(status_code=500, detail=f"Error during text generation: {str(e)}")

# --- API Endpoint for Story Generation ---
@app.post("/generate-story")
async def generate_story(request: StoryRequest):
    if not model_info:
        raise HTTPException(status_code=503, detail="LM Studio API is not available or no model is loaded")

    # Format the words
    words_text = ", ".join([f"'{word}'" for word in request.words])
    
    # Create a prompt for the story generation
    story_prompt = f"""Create an engaging short story that effectively teaches the following vocabulary words: {words_text}.

Theme: {request.theme}
{"Characters: " + request.characters if request.characters else ""}
{"Scenario: " + request.scenario if request.scenario else ""}

Guidelines:
1. Use each vocabulary word at least once in a way that clearly demonstrates its meaning
2. Bold each vocabulary word when first used
3. After the story, include a brief section explaining each word's meaning as used in the story
4. Keep the story engaging and appropriate for vocabulary learning
5. The story should be 300-500 words long

Story:"""

    # Configure generation parameters
    gen_config = {
        "temperature": request.temperature,
        "top_p": 0.95,
        "top_k": 40,
        "max_new_tokens": request.max_new_tokens,
    }

    print(f"Generating story with theme: '{request.theme}' for words: {request.words}")

    try:
        # Submit the generation task to the thread pool
        future = executor.submit(generate_text_with_lmstudio, story_prompt, gen_config)
        story_text, error = future.result()
        
        if error:
            raise Exception(error)

        if not story_text:
            story_text = "Could not generate a story with the given words."

        return {
            "words": request.words,
            "theme": request.theme,
            "story": story_text.strip()
        }

    except Exception as e:
        print(f"Error during story generation: {e}")
        raise HTTPException(status_code=500, detail=f"Error during story generation: {str(e)}")

# --- Health Check Endpoint ---
@app.get("/health")
async def health_check():
    try:
        # Check if LM Studio is running
        response = requests.get(f"{LM_STUDIO_API_URL}/models", timeout=3)
        lm_studio_running = response.status_code == 200
        
        # Get model information if available
        model_name = "Unknown"
        if lm_studio_running and model_info:
            model_name = model_info.get("id", "Unknown")
        
        return {
            "status": "ok" if lm_studio_running else "error",
            "model_ready": lm_studio_running and model_info is not None,
            "model_name": model_name,
            "lm_studio_url": LM_STUDIO_API_URL,
            "cpu_threads": NUM_THREADS
        }
    except Exception as e:
        return {
            "status": "error",
            "model_ready": False,
            "error": str(e),
            "lm_studio_url": LM_STUDIO_API_URL
        }

# --- Main Block to Run Uvicorn ---
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=9002)