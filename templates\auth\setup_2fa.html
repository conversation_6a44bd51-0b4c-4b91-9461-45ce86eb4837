{% extends "base.html" %}

{% block title %}Two-Factor Authentication Setup - GRE Vocabulary Learner{% endblock %}

{% block extra_css %}
<style>
    .security-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 15px;
        color: white;
    }
    
    .qr-code-container {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        text-align: center;
        margin: 2rem 0;
    }
    
    .step-indicator {
        display: flex;
        justify-content: center;
        margin-bottom: 2rem;
    }
    
    .step {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 1rem;
        font-weight: bold;
    }
    
    .step.active {
        background: var(--primary-color);
    }
    
    .step.completed {
        background: #28a745;
    }
    
    .security-warning {
        background: rgba(255, 193, 7, 0.1);
        border: 1px solid rgba(255, 193, 7, 0.3);
        border-radius: 10px;
        padding: 1rem;
        margin: 1rem 0;
    }
    
    .app-recommendation {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        padding: 1rem;
        margin: 1rem 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card security-card">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <h2><i class="bi bi-shield-check"></i> Two-Factor Authentication</h2>
                        <p class="mb-0">Add an extra layer of security to your account</p>
                    </div>
                    
                    {% if user.two_factor_enabled %}
                    <!-- 2FA Already Enabled -->
                    <div class="text-center">
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle"></i>
                            Two-Factor Authentication is currently <strong>enabled</strong> for your account.
                        </div>
                        
                        <div class="d-flex justify-content-center gap-3 mt-4">
                            <a href="{{ url_for('auth.backup_codes') }}" class="btn btn-warning">
                                <i class="bi bi-key"></i> View Backup Codes
                            </a>
                            <button class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#disable2faModal">
                                <i class="bi bi-shield-x"></i> Disable 2FA
                            </button>
                        </div>
                    </div>
                    
                    {% else %}
                    <!-- Setup 2FA -->
                    <div class="step-indicator">
                        <div class="step active">1</div>
                        <div class="step">2</div>
                        <div class="step">3</div>
                    </div>
                    
                    <div class="security-warning">
                        <h6><i class="bi bi-exclamation-triangle"></i> Important Security Information</h6>
                        <ul class="mb-0">
                            <li>Two-Factor Authentication adds an extra layer of security to your account</li>
                            <li>You'll need an authenticator app on your phone to generate codes</li>
                            <li>Keep your backup codes in a safe place - you'll need them if you lose your phone</li>
                        </ul>
                    </div>
                    
                    <div class="app-recommendation">
                        <h6><i class="bi bi-phone"></i> Recommended Authenticator Apps</h6>
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <i class="bi bi-google" style="font-size: 2rem;"></i>
                                <p class="mb-0"><strong>Google Authenticator</strong></p>
                                <small>Free • iOS & Android</small>
                            </div>
                            <div class="col-md-4 text-center">
                                <i class="bi bi-microsoft" style="font-size: 2rem;"></i>
                                <p class="mb-0"><strong>Microsoft Authenticator</strong></p>
                                <small>Free • iOS & Android</small>
                            </div>
                            <div class="col-md-4 text-center">
                                <i class="bi bi-shield-check" style="font-size: 2rem;"></i>
                                <p class="mb-0"><strong>Authy</strong></p>
                                <small>Free • iOS & Android</small>
                            </div>
                        </div>
                    </div>
                    
                    {% if qr_code_url %}
                    <div class="qr-code-container">
                        <h5 class="text-dark mb-3">Scan this QR Code</h5>
                        <img src="{{ qr_code_url }}" alt="2FA QR Code" class="img-fluid" style="max-width: 200px;">
                        <p class="text-muted mt-3 mb-0">
                            Open your authenticator app and scan this QR code to add your account.
                        </p>
                    </div>
                    {% endif %}
                    
                    <form action="{{ url_for('auth.setup_2fa') }}" method="POST">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <input type="hidden" name="action" value="enable">
                        
                        <div class="mb-4">
                            <label for="token" class="form-label">
                                <i class="bi bi-key"></i> Enter Verification Code
                            </label>
                            <input type="text" class="form-control form-control-lg text-center" 
                                   id="token" name="token" placeholder="000000" 
                                   maxlength="6" pattern="[0-9]{6}" required
                                   style="font-size: 1.5rem; letter-spacing: 0.5rem;">
                            <div class="form-text text-light">
                                Enter the 6-digit code from your authenticator app
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('auth.profile') }}" class="btn btn-outline-light">
                                <i class="bi bi-arrow-left"></i> Back to Profile
                            </a>
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="bi bi-shield-check"></i> Enable 2FA
                            </button>
                        </div>
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Disable 2FA Modal -->
{% if user.two_factor_enabled %}
<div class="modal fade" id="disable2faModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-shield-x"></i> Disable Two-Factor Authentication
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ url_for('auth.setup_2fa') }}" method="POST">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <input type="hidden" name="action" value="disable">
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>Warning:</strong> Disabling 2FA will make your account less secure.
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">Confirm with your password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Disable 2FA</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
// Auto-format verification code input
document.getElementById('token').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, ''); // Remove non-digits
    if (value.length > 6) {
        value = value.substring(0, 6);
    }
    e.target.value = value;
});

// Auto-submit when 6 digits are entered
document.getElementById('token').addEventListener('input', function(e) {
    if (e.target.value.length === 6) {
        // Optional: Auto-submit after a short delay
        setTimeout(() => {
            if (e.target.value.length === 6) {
                e.target.form.submit();
            }
        }, 500);
    }
});

// Focus on token input when page loads
document.addEventListener('DOMContentLoaded', function() {
    const tokenInput = document.getElementById('token');
    if (tokenInput) {
        tokenInput.focus();
    }
});
</script>
{% endblock %}
