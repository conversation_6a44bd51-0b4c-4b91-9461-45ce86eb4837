import os
import secrets
import sqlite3
import uuid
import random
from datetime import datetime
from contextlib import contextmanager
from functools import wraps

# Remove LearningSession from the import
from search import SearchEngine
# Flask imports
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session, g, make_response
from werkzeug.utils import secure_filename

from auth import auth, login_required
from models import db, User
from security import SecurityManager, security_manager
from csv_processor import csv_processor
from study_room import study_room

# Configuration Constants
class Config:
    # File upload settings
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    ALLOWED_EXTENSIONS = {'.csv', '.txt'}

    # Learning session defaults
    DEFAULT_WORD_COUNT = 10
    DEFAULT_RECOMMENDATION_COUNT = 5

    # Session timeouts
    SESSION_TIMEOUT = 3600  # 1 hour

    # Database settings
    DEFAULT_DB_NAME = "gre_words.db"

    # UI settings
    WORDS_PER_PAGE = 20

# Utility Functions
def validate_user():
    """Validate that user is logged in and return user object"""
    if not hasattr(g, 'user') or not g.user:
        return None
    return g.user

def validate_learning_session():
    """Validate that a learning session is active"""
    return ('learning_words' in session and
            'current_index' in session and
            'learning_session_id' in session)

def get_user_or_redirect():
    """Get current user or redirect to login"""
    user = validate_user()
    if not user:
        flash('You must be logged in to access this page', 'warning')
        return None
    return user

@contextmanager
def safe_file_operation(filepath):
    """Context manager for safe file operations with cleanup"""
    try:
        yield filepath
    finally:
        if os.path.exists(filepath):
            try:
                os.remove(filepath)
            except OSError:
                pass  # File might already be deleted

def require_learning_session(f):
    """Decorator to ensure learning session is active"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not validate_learning_session():
            flash('No active learning session. Please start a new session.', 'warning')
            return redirect(url_for('learning_session'))
        return f(*args, **kwargs)
    return decorated_function

def handle_csv_processing(file, library_id=None):
    """Handle CSV file processing with proper error handling"""
    _ = library_id  # Reserved for future use
    if not file or file.filename == '':
        return None, ["No file selected"]

    if not file.filename.lower().endswith(tuple(Config.ALLOWED_EXTENSIONS)):
        return None, ["Please upload a valid CSV or TXT file"]

    filename = secure_filename(file.filename)
    filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)

    try:
        # Ensure upload directory exists
        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
        file.save(filepath)

        # Process CSV
        word_entries, processing_errors = csv_processor.process_csv(filepath)

        if processing_errors:
            return None, processing_errors

        return word_entries, []

    except Exception as e:
        return None, [f"Error processing file: {str(e)}"]
    finally:
        # Clean up file
        if os.path.exists(filepath):
            try:
                os.remove(filepath)
            except OSError:
                pass

def get_words_for_session(word_source, word_count, user_id, form_data):
    """Get words for learning session based on source type"""
    words = []

    if word_source == 'random':
        words = search_engine.get_random_words(word_count)

    elif word_source == 'library':
        library_id = form_data.get('library_id')
        if not library_id:
            return None, "Please select a library."

        unlearned_only = form_data.get('unlearned_only') == '1'

        if unlearned_only:
            word_objects = search_engine.get_unlearned_words_from_library(user_id, library_id)
        else:
            word_objects = search_engine.get_words_in_library(library_id)

        # Limit to requested count and randomize if needed
        if len(word_objects) > word_count:
            random_order = form_data.get('random_order') == '1'
            if random_order:
                word_objects = random.sample(word_objects, word_count)
            else:
                word_objects = word_objects[:word_count]

        # Convert to session format
        words = [{'word': w['word'], 'definition': w['definition'], 'example': w['example']}
                for w in word_objects]

    elif word_source == 'custom':
        word_list = form_data.get('word_list', '').strip()
        if not word_list:
            return None, "Please enter at least one word."

        word_texts = [w.strip() for w in word_list.split(',') if w.strip()]

        for word_text in word_texts:
            word = search_engine.get_word(word_text)
            if word:
                words.append({
                    'word': word['word'],
                    'definition': word['definition'],
                    'example': word['example']
                })
            else:
                words.append({
                    'word': word_text,
                    'definition': "No definition available",
                    'example': ""
                })

    if not words:
        return None, "No words available for learning. Please try a different selection."

    # Randomize order if requested
    if form_data.get('random_order') == '1':
        random.shuffle(words)

    return words, None

def clear_learning_session():
    """Clear all learning session data from Flask session"""
    session_keys = [
        'learning_words', 'current_index', 'learning_session_id',
        'words_learned', 'session_start_time', 'show_definition', 'show_examples'
    ]
    for key in session_keys:
        session.pop(key, None)

# Define a simple placeholder RecommendationEngine since the module is not available
class RecommendationEngine:
    def __init__(self, db_path):
        self.db_path = db_path
        print(f"Warning: Using placeholder RecommendationEngine. Recommendations will be random words.")

    def close(self):
        pass

    def get_word_of_the_day(self):
        # Return a random word as word of the day
        try:
            random_words = search_engine.get_random_words(1)
            if random_words:
                return random_words[0]
        except:
            pass
        return None

    def get_recommended_words(self, count=5):
        """Get recommended words - fallback to random words"""
        try:
            # Get random words as recommendations
            random_words = search_engine.get_random_words(count)
            if random_words:
                # random_words is already a list of dictionaries
                recommendations = []
                for word_dict in random_words:
                    if isinstance(word_dict, dict):
                        recommendations.append({
                            'word': word_dict.get('word', ''),
                            'definition': word_dict.get('definition', ''),
                            'source': word_dict.get('source', 'Database'),
                            'learned': bool(word_dict.get('learned', False))
                        })
                    else:
                        # Handle tuple format as fallback
                        if len(word_dict) >= 4:
                            recommendations.append({
                                'word': word_dict[0],
                                'definition': word_dict[1],
                                'source': word_dict[2] if word_dict[2] else 'Database',
                                'learned': bool(word_dict[3])
                            })
                return recommendations
        except Exception as e:
            print(f"Error getting recommended words: {e}")
        return []

# Create Flask application
app = Flask(__name__)

# Disable template caching for debugging
app.config['TEMPLATES_AUTO_RELOAD'] = True
app.jinja_env.auto_reload = True

# Check Werkzeug version and import appropriate modules
import werkzeug
import importlib.util

# Try to import SharedDataMiddleware from the correct location based on Werkzeug version
shared_data_middleware = None
try:
    # First try the new location (Werkzeug 2.0+)
    if importlib.util.find_spec('werkzeug.middleware.shared_data'):
        from werkzeug.middleware.shared_data import SharedDataMiddleware
        shared_data_middleware = SharedDataMiddleware
    # Then try the old location
    elif importlib.util.find_spec('werkzeug.wsgi') and hasattr(werkzeug.wsgi, 'SharedDataMiddleware'):
        from werkzeug.wsgi import SharedDataMiddleware
        shared_data_middleware = SharedDataMiddleware
except ImportError:
    print("Warning: SharedDataMiddleware not found. Static file serving may be affected.")

# Generate a secure secret key if not provided
def generate_secret_key():
    """Generate a secure secret key for the application"""
    return secrets.token_urlsafe(32)

# Configure application
app.config.update(
    SECRET_KEY=os.environ.get('SECRET_KEY', generate_secret_key()),
    UPLOAD_FOLDER=os.path.join(os.getcwd(), 'uploads'),
    MAX_CONTENT_LENGTH=Config.MAX_CONTENT_LENGTH,
    SQLALCHEMY_DATABASE_URI=os.environ.get('DATABASE_URL', 'sqlite:///users.db'),
    SQLALCHEMY_TRACK_MODIFICATIONS=False,
    # Security configurations
    SESSION_COOKIE_SECURE=os.environ.get('FLASK_ENV') == 'production',
    SESSION_COOKIE_HTTPONLY=True,
    SESSION_COOKIE_SAMESITE='Lax',
    PERMANENT_SESSION_LIFETIME=Config.SESSION_TIMEOUT,
    # CSRF Protection - Temporarily disabled to fix login issues
    WTF_CSRF_ENABLED=False,
    WTF_CSRF_TIME_LIMIT=None,
    # Set debug mode to False to avoid Werkzeug debug issues
    DEBUG=os.environ.get('FLASK_ENV') != 'production'
)

# Create upload directory if it doesn't exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs('instance', exist_ok=True)

# Initialize security features - CSRF disabled temporarily
csrf = None
print("Warning: CSRF protection is disabled to fix login issues.")
security_manager.init_app(app)

# Initialize database
db.init_app(app)

# Create database tables if they don't exist
with app.app_context():
    try:
        db.create_all()
        print("Database tables created successfully")
    except Exception as e:
        print(f"Error creating database tables: {e}")

# Register blueprints
app.register_blueprint(auth, url_prefix='/auth')
app.register_blueprint(study_room)

# Initialize the search engine
search_engine = SearchEngine(Config.DEFAULT_DB_NAME)

# Initialize database tables
try:
    search_engine.ensure_tables_exist()
except Exception as e:
    print(f"Error initializing database: {e}")
    import traceback
    traceback.print_exc()

# Database connection helper
def get_db():
    """Get a database connection"""
    if not hasattr(g, 'sqlite_db'):
        try:
            # Use the same database file as SQLAlchemy
            g.sqlite_db = sqlite3.connect('users.db')
            g.sqlite_db.row_factory = sqlite3.Row
        except Exception as e:
            print(f"Error connecting to database: {e}")
            # Return a dummy connection that won't crash when methods are called on it
            class DummyConnection:
                def cursor(self):
                    class DummyCursor:
                        def execute(self, *args, **kwargs):
                            _ = args, kwargs  # Suppress unused parameter warnings
                            pass
                        def fetchone(self): return None
                        def fetchall(self): return []
                    return DummyCursor()
                def commit(self): pass
                def close(self): pass
            return DummyConnection()
    return g.sqlite_db

# Close database connection when request is done
@app.teardown_appcontext
def close_db(error):
    _ = error  # Suppress unused parameter warning
    if hasattr(g, 'sqlite_db'):
        g.sqlite_db.close()

# Before request handler to load user from session
@app.before_request
def load_user():
    """Load user before each request"""
    user_id = session.get('user_id')
    if user_id:
        try:
            # Load user from database
            user = db.session.get(User, user_id)
            g.user = user

            # Initialize user libraries if needed (with error handling)
            if user and not hasattr(g, 'libraries_initialized'):
                try:
                    search_engine.initialize_user_libraries(user.id)
                    g.libraries_initialized = True
                except Exception as lib_error:
                    # Don't fail user loading if library initialization fails
                    print(f"Warning: Could not initialize user libraries: {lib_error}")
                    g.libraries_initialized = False
        except Exception as e:
            app.logger.error(f"Error loading user: {e}")
            g.user = None
    else:
        g.user = None

# Add a context processor to make current_year and libraries available to all templates
@app.context_processor
def inject_context():
    context = {
        'current_year': datetime.now().year,
        'libraries': [],
        'current_library': None,
        'current_library_id': None,
        'csrf_token': SecurityManager.generate_csrf_token
    }

    # Get current user ID if available
    user_id = None
    if hasattr(g, 'user') and g.user is not None:
        user_id = g.user.id

    try:
        # Ensure tables exist before querying
        search_engine.ensure_tables_exist()

        # Get libraries for the current user
        libraries = search_engine.get_libraries(user_id=user_id)
        context['libraries'] = libraries or []

        # Get current library
        current_library_id = search_engine.get_current_library(user_id)
        context['current_library_id'] = current_library_id

        if current_library_id:
            # Find the current library in the list
            for lib in libraries:
                if lib[0] == current_library_id:
                    context['current_library'] = lib
                    break

            # If not found in the list, get it directly
            if not context['current_library']:
                current_library = search_engine.get_library_details(current_library_id)
                context['current_library'] = current_library
    except Exception as e:
        app.logger.error(f"Error getting libraries: {e}")
        import traceback
        app.logger.error(traceback.format_exc())

    # Create a current_user proxy for templates
    class CurrentUser:
        @property
        def is_authenticated(self):
            return hasattr(g, 'user') and g.user is not None

        @property
        def is_anonymous(self):
            return not hasattr(g, 'user') or g.user is None

        def __getattr__(self, name):
            if not hasattr(g, 'user') or g.user is None:
                return None
            return getattr(g.user, name)

    context['current_user'] = CurrentUser()
    return context

# Add these helper functions for templates
@app.context_processor
def utility_processor():
    def get_learned_words_from_library(library_id):
        if hasattr(g, 'user') and g.user:
            return search_engine.get_library_learned_words(library_id, g.user.id)
        return []

    def get_unlearned_words_from_library(library_id):
        if hasattr(g, 'user') and g.user:
            return search_engine.get_library_unlearned_words(library_id, g.user.id)
        return []

    def get_library_stats(library_id):
        try:
            if hasattr(g, 'user') and g.user:
                # Call the SearchEngine method with user_id
                return search_engine.get_library_stats(library_id, g.user.id)
            else:
                # Call the SearchEngine method without user_id
                return search_engine.get_library_stats(library_id)
        except Exception as e:
            print(f"Error in get_library_stats: {e}")
            # Return default stats if there's an error
            return {'total': 0, 'learned': 0, 'remaining': 0, 'percent_complete': 0}

    def is_word_learned(word):
        """Check if a word is learned by the current user"""
        if not hasattr(g, 'user') or g.user is None:
            return False

        word_data = search_engine.get_word(word)
        if not word_data:
            return False

        # Use the existing is_word_learned_by_user method
        return search_engine.is_word_learned_by_user(word_data['id'], g.user.id)

    return dict(
        get_learned_words_from_library=get_learned_words_from_library,
        get_unlearned_words_from_library=get_unlearned_words_from_library,
        get_library_stats=get_library_stats,
        is_word_learned=is_word_learned
    )

# Add a filter for formatting dates
@app.template_filter('datetime')
def format_datetime(value, format='%B %d, %Y at %I:%M %p'):
    """Format a datetime object to a string"""
    if value is None:
        return ""
    if isinstance(value, str):
        try:
            from datetime import datetime
            value = datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
        except:
            return value
    return value.strftime(format)

# Home page route
@app.route('/')
def index():
    try:
        # Get some statistics for the dashboard
        total_words = search_engine.get_total_word_count()
        learned_words = search_engine.get_learned_word_count()
        remaining_words = total_words - learned_words

        # Get recommended words for the home page instead of random words
        recommended_words = []
        try:
            # Try to use the recommendation engine first
            if 'RecommendationEngine' in globals():
                recommendation_engine = RecommendationEngine(search_engine.db_name)
                recommended_words = recommendation_engine.get_recommended_words(Config.DEFAULT_RECOMMENDATION_COUNT)
                recommendation_engine.close()

            # Fall back to random words if no recommendations
            if not recommended_words:
                random_words = search_engine.get_random_words(Config.DEFAULT_RECOMMENDATION_COUNT)
                # Convert to the expected format for the template
                recommended_words = []
                for word_dict in random_words:
                    if isinstance(word_dict, dict):
                        recommended_words.append((
                            word_dict.get('word', ''),
                            word_dict.get('definition', ''),
                            word_dict.get('source', None),
                            word_dict.get('learned', False)
                        ))
        except Exception as e:
            app.logger.error(f"Error getting recommended words: {e}")
            # Fall back to empty list if there's an error
            recommended_words = []

        return render_template(
            'index.html',
            total_words=total_words,
            learned_words=learned_words,
            remaining_words=remaining_words,
            recommended_words=recommended_words
        )
    except Exception as e:
        app.logger.error(f"Error rendering index page: {e}")
        import traceback
        app.logger.error(traceback.format_exc())
        return render_template('500.html'), 500

@app.route('/search', methods=['GET', 'POST'])
def search():
    word = request.args.get('word', '') or request.form.get('word', '')
    result = None
    examples = []
    phonetics = []

    if word:
        # Trim whitespace and convert to lowercase for search
        word = word.strip().lower()
        result = search_engine.search_word(word)
        if result:
            examples = search_engine.get_word_examples(word)
            phonetics = search_engine.get_word_phonetics(word)

    return render_template('search.html', word=word, result=result, examples=examples, phonetics=phonetics)

@app.route('/add-word', methods=['GET', 'POST'])
@login_required
def add_word():
    if request.method == 'POST':
        word = request.form.get('word', '').strip()
        definition = request.form.get('definition', '').strip()
        source = request.form.get('source', '').strip() or None

        if word and definition:
            search_engine.insert_word(word, definition, source)
            flash(f'Word "{word}" added successfully!', 'success')
            return redirect(url_for('search', word=word))
        else:
            flash('Word and definition are required!', 'danger')

    return render_template('add_word.html')

@app.route('/mark-word', methods=['POST'])
@login_required
def mark_word():
    word = request.form.get('word')
    action = request.form.get('action')
    redirect_url = request.form.get('redirect_url') or request.referrer or url_for('index')

    if not word:
        flash('No word specified!', 'danger')
        return redirect(redirect_url)

    # Handle multiple words (from checkboxes)
    if isinstance(word, list):
        words = word
    else:
        words = [word]

    for w in words:
        if action == 'learned':
            success = search_engine.mark_word_as_learned(w, g.user.id)
            if success and len(words) == 1:
                flash(f'Word "{w}" marked as learned!', 'success')
        elif action == 'unlearned':
            success = search_engine.mark_word_as_unlearned(w, g.user.id)
            if success and len(words) == 1:
                flash(f'Word "{w}" marked as not learned!', 'warning')

    return redirect(redirect_url)

@app.route('/learning-session', methods=['GET', 'POST'])
@login_required
def learning_session():
    """Start or continue a learning session"""
    if request.method == 'POST':
        # Get form data
        word_source = request.form.get('word_source', 'random')
        word_count = int(request.form.get('word_count', Config.DEFAULT_WORD_COUNT))
        show_definition = request.form.get('show_definition') == '1'
        show_examples = request.form.get('show_examples') == '1'

        # Get words using helper function
        words, error_message = get_words_for_session(word_source, word_count, g.user.id, request.form)

        if error_message:
            flash(error_message, "warning")
            return redirect(url_for('learning_session'))

        # Start a new learning session
        session_id = str(uuid.uuid4())
        session['learning_session_id'] = session_id
        session['learning_words'] = words
        session['current_index'] = 0
        session['words_learned'] = 0
        session['session_start_time'] = datetime.now().isoformat()
        session['show_definition'] = show_definition
        session['show_examples'] = show_examples

        # Record session start in database
        search_engine.start_learning_session(g.user.id, session_id)

        return redirect(url_for('show_word'))

    # GET request - show form to start session
    user_libraries = search_engine.get_libraries_for_user(g.user.id)
    library_id = request.args.get('library_id')
    word = request.args.get('word')

    return render_template('start_session.html', libraries=user_libraries, library_id=library_id, word=word)

@app.route('/show-word')
@login_required
@require_learning_session
def show_word():
    """Show the current word in the learning session"""
    # Get current word
    current_index = session.get('current_index', 0)
    words = session.get('learning_words', [])

    if current_index >= len(words):
        # End of session
        return redirect(url_for('end_session'))

    current_word = words[current_index]

    # Record this word in the session
    session_id = session.get('learning_session_id')
    if session_id:
        word_data = search_engine.get_word(current_word['word'])
        if word_data:
            search_engine.record_session_word(session_id, word_data['id'])

    return render_template('show_word.html', word=current_word, index=current_index, total=len(words))

@app.route('/next-word', methods=['POST'])
@login_required
@require_learning_session
def next_word():
    """Move to the next word in the learning session"""
    # Get current word
    current_index = session.get('current_index', 0)
    words = session.get('learning_words', [])

    if current_index >= len(words):
        # End of session
        return redirect(url_for('end_session'))

    current_word = words[current_index]

    # Check if the user marked the word as learned
    learned = request.form.get('learned') == '1'

    if learned:
        # Mark word as learned
        search_engine.mark_word_as_learned(current_word['word'], g.user.id)
        session['words_learned'] = session.get('words_learned', 0) + 1

        # Update session word record
        session_id = session.get('learning_session_id')
        if session_id:
            word_data = search_engine.get_word(current_word['word'])
            if word_data:
                search_engine.record_session_word(session_id, word_data['id'], 1)

    # Move to next word
    session['current_index'] = current_index + 1

    return redirect(url_for('show_word'))

@app.route('/end-session')
@login_required
@require_learning_session
def end_session():
    """End the current learning session"""

    # Get session data
    words = session.get('learning_words', [])
    current_index = session.get('current_index', 0)
    words_learned = session.get('words_learned', 0)
    session_id = session.get('learning_session_id')

    # End the session in the database
    if session_id:
        search_engine.end_learning_session(session_id, current_index, words_learned)

    # Calculate session stats
    stats = {
        'words_reviewed': current_index,
        'total_words': len(words),
        'words_learned': words_learned,
        'completion_rate': round(current_index / len(words) * 100 if len(words) > 0 else 0, 1),
        'learning_rate': round(words_learned / current_index * 100 if current_index > 0 else 0, 1)
    }

    # Clear session data using helper function
    clear_learning_session()

    return render_template('end_session.html', stats=stats)

@app.route('/learned-words')
@login_required
def learned_words():
    words = search_engine.get_learned_words_for_user(g.user.id)
    return render_template('word_list.html', title='My Learned Words', words=words, list_type='learned')

@app.route('/remaining-words')
def remaining_words():
    words = search_engine.get_remaining_words()
    return render_template('word_list.html', title='Words to Learn', words=words, list_type='remaining')

@app.route('/dashboard')
@login_required
def dashboard():
    """User dashboard with overview"""
    try:
        # Get user statistics
        user_libraries = search_engine.get_libraries_for_user(g.user.id)
        total_words = search_engine.get_total_word_count()

        # Get recent activity (placeholder for now)
        recent_activity = []

        return render_template('dashboard.html',
                             libraries=user_libraries,
                             total_words=total_words,
                             recent_activity=recent_activity)
    except Exception as e:
        app.logger.error(f"Error loading dashboard: {e}")
        flash("Error loading dashboard. Please try again.", "danger")
        return redirect(url_for('index'))

@app.route('/libraries')
@login_required
def libraries():
    """Show user's word libraries"""
    user_libraries = search_engine.get_libraries_for_user(g.user.id)
    return render_template('libraries.html', libraries=user_libraries)

@app.route('/library/create', methods=['GET', 'POST'])
@login_required
def create_library():
    """Create a new word library"""
    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        description = request.form.get('description', '').strip()

        if not name:
            flash("Library name is required.", "danger")
            return render_template('library_form.html')

        library_id = search_engine.create_library(g.user.id, name, description)

        if library_id:
            flash(f"Library '{name}' created successfully!", "success")
            return redirect(url_for('view_library', library_id=library_id))
        else:
            flash("Failed to create library. Please try again.", "danger")
            return render_template('library_form.html')

    # GET request - show form
    return render_template('library_form.html')

@app.route('/library/<int:library_id>')
def view_library(library_id):
    """View a library - main route"""
    return view_library_alt(library_id)

@app.route('/library/import', methods=['GET', 'POST'])
@login_required
def import_library():
    """Create a new library by importing from CSV"""
    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        description = request.form.get('description', '').strip()

        if not name:
            flash("Library name is required.", "danger")
            return render_template('import_library.html')

        # Handle file upload
        if 'file' not in request.files:
            flash("No file selected.", "danger")
            return render_template('import_library.html')

        file = request.files['file']
        if file.filename == '':
            flash("No file selected.", "danger")
            return render_template('import_library.html')

        # Process CSV file
        word_entries, processing_errors = handle_csv_processing(file)

        if processing_errors:
            flash(f"Error processing CSV: {'; '.join(processing_errors)}", "danger")
            return render_template('import_library.html')

        # Create library and import words
        library_id = search_engine.create_library(g.user.id, name, description)

        if library_id:
            added_count = 0
            for entry in word_entries:
                # Add word to database if it doesn't exist
                word_id = search_engine.insert_word(entry.word, entry.definition, entry.source or "Imported from CSV")

                # Add word to library
                if word_id and search_engine.add_word_to_library(library_id, word_id):
                    added_count += 1

            flash(f"Library '{name}' created successfully with {added_count} words!", "success")
            return redirect(url_for('view_library', library_id=library_id))
        else:
            flash("Failed to create library. Please try again.", "danger")

    # GET request - show form
    return render_template('import_library.html')

@app.route('/set-current-library/<int:library_id>', methods=['POST'])
@login_required
def set_current_library(library_id):
    """Set a library as the current library for the user"""
    try:
        success = search_engine.set_current_library(library_id, g.user.id)
        if success:
            flash('Current library updated successfully', 'success')
        else:
            flash('Failed to update current library', 'danger')
    except Exception as e:
        app.logger.error(f"Error setting current library: {e}")
        flash('An error occurred while setting the current library', 'danger')

    return redirect(url_for('libraries'))

@app.route('/set-current-library', methods=['POST'])
@login_required
def set_current_library_form():
    """Set a library as the current library for the user (form submission)"""
    library_id = request.form.get('library_id')
    if not library_id:
        flash('No library specified', 'danger')
        return redirect(url_for('libraries'))

    return set_current_library(int(library_id))

@app.route('/library/<int:library_id>/add-words', methods=['GET', 'POST'])
@login_required
def add_words_to_library(library_id):
    """Add words to a library"""
    return add_multiple_words(library_id)

@app.route('/library/<int:library_id>/add-to-library', methods=['GET', 'POST'])
@login_required
def add_to_library(library_id):
    """Add words to a library via CSV or from another library"""
    if not hasattr(g, 'user') or not g.user:
        flash('You must be logged in to add words to a library', 'warning')
        return redirect(url_for('auth.login'))

    # Get library details
    library = search_engine.get_library_details(library_id)
    if not library:
        flash('Library not found', 'danger')
        return redirect(url_for('libraries'))

    # Handle both tuple and dict formats
    if isinstance(library, tuple):
        library_name = library[1]
    else:
        library_name = library.get('name', 'Unknown Library')

    if request.method == 'POST':
        # Check if it's a CSV upload or library copy
        if 'file' in request.files and request.files['file'].filename:
            # Handle CSV upload using helper function
            file = request.files['file']
            word_entries, processing_errors = handle_csv_processing(file)

            if processing_errors:
                flash(f"Error processing CSV: {'; '.join(processing_errors)}", "danger")
                return redirect(url_for('add_to_library', library_id=library_id))

            added_count = 0
            for entry in word_entries:
                # Add word to database if it doesn't exist
                word_id = search_engine.insert_word(entry.word, entry.definition, entry.source or "Imported from CSV")

                # Add word to library
                if word_id and search_engine.add_word_to_library(library_id, word_id):
                    added_count += 1

            flash(f"Added {added_count} words to library '{library_name}'!", "success")
            return redirect(url_for('view_library', library_id=library_id))

        elif 'source_library_id' in request.form:
            # Handle library copy
            source_library_id = request.form.get('source_library_id')
            if source_library_id:
                try:
                    # Get words from source library
                    source_words = search_engine.get_words_in_library(int(source_library_id))
                    added_count = 0

                    for word in source_words:
                        word_id = word.get('id') if isinstance(word, dict) else word[0]
                        if search_engine.add_word_to_library(library_id, word_id):
                            added_count += 1

                    flash(f"Copied {added_count} words to library '{library_name}'!", "success")
                    return redirect(url_for('view_library', library_id=library_id))

                except Exception as e:
                    app.logger.error(f"Error copying library: {e}")
                    flash(f"Error copying words: {str(e)}", "danger")
            else:
                flash("Please select a source library.", "danger")

    # GET request - show form
    # Get other libraries for copying
    all_libraries = search_engine.get_libraries_for_user(g.user.id)
    other_libraries = [lib for lib in all_libraries if lib.get('id', lib[0] if isinstance(lib, tuple) else 0) != library_id]

    return render_template('add_to_library.html',
                         library_id=library_id,
                         library_name=library_name,
                         libraries=other_libraries)

@app.route('/edit-library/<int:library_id>', methods=['GET', 'POST'])
def edit_library_standalone(library_id):
    """Edit a library's name and description"""
    return edit_library(library_id)

@app.route('/library/<int:library_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_library(library_id):
    """Edit a library's name and description"""
    try:
        # Get library details
        library = search_engine.get_library_details(library_id)

        if not library:
            flash('Library not found!', 'danger')
            return redirect(url_for('libraries'))

        library_name = library[1]
        library_description = library[2]

        if request.method == 'POST':
            name = request.form.get('name', '').strip()
            description = request.form.get('description', '').strip() or None

            if not name:
                flash('Library name is required!', 'danger')
                return render_template('edit_library.html',
                                      library_id=library_id,
                                      library_name=library_name,
                                      library_description=library_description)

            # Update the library
            result = search_engine.update_library(library_id, name, description)
            if result is True:
                flash(f'Library "{name}" updated successfully!', 'success')
                return redirect(url_for('view_library', library_id=library_id))
            else:
                # If result is a string, it's an error message
                error_msg = result if isinstance(result, str) else 'Failed to update library. A library with this name may already exist.'
                flash(error_msg, 'danger')

        return render_template('edit_library.html',
                              library_id=library_id,
                              library_name=library_name,
                              library_description=library_description)
    except Exception as e:
        app.logger.error(f"Error editing library: {e}")
        import traceback
        app.logger.error(traceback.format_exc())
        flash(f'An error occurred while editing the library: {str(e)}', 'danger')
        return redirect(url_for('libraries'))

@app.route('/debug/library-stats/<int:library_id>')
def debug_library_stats(library_id):
    """Debug endpoint to check library stats"""
    if not app.debug:
        return "Debug mode is not enabled", 403

    stats = search_engine.get_library_stats(library_id)
    words = search_engine.get_library_words(library_id)

    return {
        'library_id': library_id,
        'stats': stats,
        'word_count': len(words),
        'words': [word[0] for word in words[:10]]  # Show first 10 words
    }

@app.route('/debug/populate-library/<int:library_id>')
def populate_test_library(library_id):
    """Debug endpoint to populate a library with test words"""
    if not app.debug:
        return "Debug mode is not enabled", 403

    # Get some words from the main database
    all_words = search_engine.get_all_words()
    if not all_words:
        return "No words found in database", 400

    # Add first 10 words to the library
    added_count = 0
    for word, _, _, _ in all_words[:10]:
        if search_engine.add_word_to_library(word, library_id):
            added_count += 1

    return f"Added {added_count} words to library {library_id}", 200

@app.route('/debug/database-structure')
def debug_db_structure():
    """Debug endpoint to check database structure"""
    if not app.debug:
        return "Debug mode is not enabled", 403

    return search_engine.debug_database_structure()

@app.route('/import-all-to-library/<int:library_id>')
def import_all_to_library_route(library_id):
    """Import all words from the main database into a library"""
    # Check if library exists
    libraries = search_engine.get_libraries()
    library_exists = False
    library_name = None

    for lib_id, name, _, _ in libraries:
        if lib_id == library_id:
            library_exists = True
            library_name = name
            break

    if not library_exists:
        flash('Library not found!', 'danger')
        return redirect(url_for('libraries'))

    # Import all words
    count = search_engine.import_all_words_to_library(library_id)

    if count > 0:
        flash(f'Imported {count} words to library "{library_name}"!', 'success')
    else:
        flash('No new words were imported to the library!', 'warning')

    return redirect(url_for('view_library', library_id=library_id))

@app.route('/debug/refresh-libraries')
def debug_refresh_libraries():
    """Debug endpoint to refresh all libraries"""
    if not app.debug:
        return "Debug mode is not enabled", 403

    # Ensure library tables exist
    search_engine.ensure_library_tables_exist()

    # Ensure default libraries exist and are populated
    search_engine.ensure_default_libraries_exist()

    # Make sure Mongoose library has words
    mongoose_id = search_engine.get_or_create_default_library()
    if mongoose_id:
        imported_count = search_engine.import_all_words_to_library(mongoose_id)
        if imported_count > 0:
            flash(f'Imported {imported_count} words to Mongoose library', 'success')

    return redirect(url_for('libraries'))

@app.route('/debug/library/<int:library_id>')
@login_required
def debug_library(library_id):
    """Debug route to show raw library data"""
    if not hasattr(g, 'user') or not g.user or not getattr(g.user, 'is_admin', False):
        flash('Access denied.', 'danger')
        return redirect(url_for('index'))

    try:
        # Get library details
        library = search_engine.get_library_details(library_id)

        if not library:
            flash('Library not found!', 'danger')
            return redirect(url_for('libraries'))

        # Get words in the library
        words = search_engine.get_library_words(library_id)

        # Get learned words
        learned_words = search_engine.get_library_learned_words(library_id)

        # Get unlearned words
        unlearned_words = search_engine.get_library_unlearned_words(library_id)

        # Get library stats
        stats = get_library_stats(library_id)

        debug_info = {
            'library': library,
            'words_count': len(words),
            'learned_count': len(learned_words),
            'unlearned_count': len(unlearned_words),
            'stats': stats,
            'words': words,
            'learned_words': learned_words,
            'unlearned_words': unlearned_words
        }

        return render_template('debug.html', debug_info=debug_info)
    except Exception as e:
        app.logger.error(f"Error debugging library: {e}")
        import traceback
        app.logger.error(traceback.format_exc())
        flash('An error occurred while debugging the library.', 'danger')
        return redirect(url_for('libraries'))

@app.route('/debug/search')
def debug_search():
    """Debug route to test search functionality"""
    word = request.args.get('word', '')
    result = None

    if word:
        result = search_engine.search_word(word)

    return jsonify({
        'word': word,
        'result': result,
        'request_args': dict(request.args),
        'request_form': dict(request.form)
    })

@app.route('/debug/routes')
def debug_routes():
    """Debug route to show all registered routes"""
    if not app.debug:
        return "Debug mode is not enabled", 403

    routes = []
    for rule in app.url_map.iter_rules():
        routes.append(f"{rule.endpoint} -> {rule.rule} [{', '.join(rule.methods)}]")
    return "<br>".join(sorted(routes))

@app.route('/view-word/<string:word>')
def view_word(word):
    """View details for a specific word"""
    try:
        # Get word details
        word_data = search_engine.get_word(word)

        if not word_data:
            flash(f'Word "{word}" not found!', 'danger')
            return redirect(url_for('index'))

        # Check if the word is learned by the current user
        user_id = g.user.id if hasattr(g, 'user') and g.user else None
        is_learned = search_engine.is_word_learned_by_user(word_data['id'], user_id) if user_id else False

        # Get examples for the word
        examples = []  # You would implement a method to get examples

        # Get phonetics for the word
        phonetics = []  # You would implement a method to get phonetics

        return render_template(
            'view_word.html',
            word=word_data['word'],
            definition=word_data['definition'],
            source=word_data['source'],
            is_learned=is_learned,
            examples=examples,
            phonetics=phonetics
        )
    except Exception as e:
        app.logger.error(f"Error viewing word: {e}")
        import traceback
        app.logger.error(traceback.format_exc())
        flash(f"Error viewing word: {str(e)}", 'danger')
        return redirect(url_for('index'))

@app.route('/word/<word>')
def word_detail(word):
    """Show details for a specific word"""
    word_data = search_engine.get_word(word)
    if not word_data:
        flash(f"Word '{word}' not found.", "danger")
        return redirect(url_for('search'))

    # Get user progress for this word if logged in
    progress = None
    libraries = []
    if hasattr(g, 'user') and g.user is not None:
        # Check if word is learned by user
        is_learned = search_engine.is_word_learned_by_user(word_data['id'], g.user.id)
        progress = {'status': 'learned' if is_learned else 'learning'}
        libraries = search_engine.get_libraries_for_user(g.user.id)

    return render_template('word_detail.html', word=word_data, progress=progress, libraries=libraries)

@app.route('/libraries/<int:library_id>')
def view_library_alt(library_id):
    """View a library"""
    try:
        user_id = g.user.id if hasattr(g, 'user') and g.user else None

        # Get library details
        library = search_engine._execute_query(
            'SELECT id, name, description, created_date FROM libraries WHERE id = ?',
            (library_id,),
            fetch_one=True
        )

        if not library:
            flash('Library not found!', 'danger')
            return redirect(url_for('libraries'))

        # Get all words in this library with learned status
        if user_id is not None:
            words = search_engine._execute_query('''
                SELECT w.word, w.definition, w.source,
                       CASE WHEN lh.id IS NOT NULL THEN 1 ELSE 0 END as learned
                FROM words w
                JOIN library_words lw ON w.id = lw.word_id
                LEFT JOIN learned_history lh ON w.id = lh.word_id AND lh.user_id = ?
                WHERE lw.library_id = ?
                ORDER BY w.word
            ''', (user_id, library_id))
        else:
            words = search_engine._execute_query('''
                SELECT w.word, w.definition, w.source, w.learned
                FROM words w
                JOIN library_words lw ON w.id = lw.word_id
                WHERE lw.library_id = ?
                ORDER BY w.word
            ''', (library_id,))

        # Get current library
        current_library_id = search_engine.get_current_library(user_id)

        return render_template(
            'view_library.html',
            library=dict(library),
            words=words,
            current_library_id=current_library_id
        )
    except Exception as e:
        app.logger.error(f"Error viewing library: {e}")
        import traceback
        app.logger.error(traceback.format_exc())
        flash(f"Error viewing library: {str(e)}", 'danger')
        return redirect(url_for('libraries'))

@app.route('/library/<int:library_id>/add-multiple-words', methods=['GET', 'POST'])
@login_required
def add_multiple_words(library_id):
    """Add words to a library"""
    if not hasattr(g, 'user') or not g.user:
        flash('You must be logged in to add words to a library', 'warning')
        return redirect(url_for('auth.login'))

    # Get library details
    conn = search_engine._get_connection()
    try:
        cursor = conn.cursor()
        cursor.execute('SELECT id, name, description FROM libraries WHERE id = ?', (library_id,))
        library = cursor.fetchone()

        if not library:
            flash('Library not found', 'danger')
            return redirect(url_for('libraries'))

        if request.method == 'POST':
            words_text = request.form.get('words', '')
            words_list = [w.strip() for w in words_text.split('\n') if w.strip()]

            added_count = 0
            for word in words_list:
                # Check if word exists in database
                cursor.execute('SELECT id FROM words WHERE word = ?', (word,))
                result = cursor.fetchone()

                if result:
                    word_id = result[0]
                else:
                    # Add word to database
                    cursor.execute(
                        'INSERT INTO words (word, definition, source) VALUES (?, ?, ?)',
                        (word, f"Definition for {word}", "User added")
                    )
                    word_id = cursor.lastrowid

                # Add word to library
                try:
                    cursor.execute(
                        'INSERT INTO library_words (library_id, word_id) VALUES (?, ?)',
                        (library_id, word_id)
                    )
                    added_count += 1
                except sqlite3.IntegrityError:
                    # Word already in library
                    pass

            conn.commit()

            if added_count > 0:
                flash(f'Added {added_count} words to library', 'success')
            else:
                flash('No new words were added to the library', 'info')

            return redirect(url_for('view_library', library_id=library_id))

        return render_template('add_words.html', library=library)
    finally:
        search_engine._release_connection(conn)

@app.route('/library/<int:library_id>/remove_word/<string:word>', methods=['POST'])
@login_required
def remove_word_from_library(library_id, word):
    """Remove a word from a library"""
    _ = word  # Suppress unused parameter warning
    flash('Word removal functionality is temporarily disabled', 'info')
    return redirect(url_for('view_library', library_id=library_id))

@app.route('/export-words')
@login_required
def export_words():
    """Export words to a file"""
    format = request.args.get('format', 'csv')
    list_type = request.args.get('list_type', 'all')
    library_id = request.args.get('library_id')

    # Get words based on list type
    if list_type == 'learned':
        words = search_engine.get_learned_words_for_user(g.user.id)
        filename = f"learned_words.{format}"
    elif list_type == 'library' and library_id:
        words = search_engine.get_words_in_library(library_id)
        library = search_engine.get_library(library_id)
        library_name = library['name'] if library else 'library'
        filename = f"{library_name.lower().replace(' ', '_')}.{format}"
    else:
        words = search_engine.get_all_words()
        filename = f"all_words.{format}"

    # Export words to the requested format
    content = search_engine.export_words(words, format)

    # Create response
    response = make_response(content)
    response.headers["Content-Disposition"] = f"attachment; filename={filename}"

    # Set content type based on format
    if format == 'csv':
        response.headers["Content-Type"] = "text/csv"
    elif format == 'txt':
        response.headers["Content-Type"] = "text/plain"
    elif format == 'json':
        response.headers["Content-Type"] = "application/json"

    return response

@app.route('/library/<int:library_id>/import-all', methods=['GET'])
@login_required
def import_all_to_library(library_id):
    """Import all words from the database to a library"""
    library = search_engine.get_library(library_id)

    # Check if library exists and belongs to the user
    if not library or library['user_id'] != g.user.id:
        flash("Library not found or you don't have permission to modify it.", "danger")
        return redirect(url_for('libraries'))

    # Get all words from the database
    all_words = search_engine.get_all_words()

    # Add each word to the library
    added_count = 0
    for word in all_words:
        if search_engine.add_word_to_library(library_id, word['id']):
            added_count += 1

    if added_count > 0:
        flash(f"Imported {added_count} word(s) to the library successfully!", "success")
    else:
        flash("No new words were added to the library. All words may already be in the library.", "info")

    return redirect(url_for('view_library', library_id=library_id))

# Helper functions for library statistics
def get_library_stats(library_id):
    """Get statistics for a library"""
    if not hasattr(g, 'user') or g.user is None:
        return {'learned': 0, 'remaining': 0, 'total': 0, 'percent': 0}

    user_id = g.user.id

    # Get learned words
    learned_words = search_engine.get_learned_words_from_library(user_id, library_id)
    learned_count = len(learned_words)

    # Get unlearned words
    unlearned_words = search_engine.get_unlearned_words_from_library(user_id, library_id)
    unlearned_count = len(unlearned_words)

    # Calculate total and percentage
    total = learned_count + unlearned_count
    percent = round((learned_count / total) * 100) if total > 0 else 0

    return {
        'learned': learned_count,
        'remaining': unlearned_count,
        'total': total,
        'percent': percent
    }

def is_word_learned(word):
    """Check if a word is learned by the current user"""
    if not hasattr(g, 'user') or g.user is None:
        return False

    user_id = g.user.id
    return search_engine.is_word_learned(user_id, word)

def get_learned_words_from_library(library_id):
    """Get learned words from a library for the current user"""
    # Get user ID if available
    user_id = g.user.id if hasattr(g, 'user') and g.user else None

    conn = search_engine._get_connection()
    try:
        cursor = conn.cursor()

        if user_id is not None:
            # Get words learned by this user
            cursor.execute('''
                SELECT w.word, w.definition, w.source
                FROM words w
                JOIN library_words lw ON w.id = lw.word_id
                JOIN learned_history lh ON w.id = lh.word_id
                WHERE lw.library_id = ? AND lh.user_id = ?
                ORDER BY w.word
            ''', (library_id, user_id))
        else:
            # Get globally learned words
            cursor.execute('''
                SELECT w.word, w.definition, w.source
                FROM words w
                JOIN library_words lw ON w.id = lw.word_id
                WHERE lw.library_id = ? AND w.learned = 1
                ORDER BY w.word
            ''', (library_id,))

        return cursor.fetchall()
    finally:
        search_engine._release_connection(conn)

def get_unlearned_words_from_library(library_id):
    """Get unlearned words from a library for the current user"""
    # Get user ID if available
    user_id = g.user.id if hasattr(g, 'user') and g.user else None

    conn = search_engine._get_connection()
    try:
        cursor = conn.cursor()

        if user_id is not None:
            # Get words not learned by this user
            cursor.execute('''
                SELECT w.word, w.definition, w.source
                FROM words w
                JOIN library_words lw ON w.id = lw.word_id
                LEFT JOIN learned_history lh ON w.id = lh.word_id AND lh.user_id = ?
                WHERE lw.library_id = ? AND lh.id IS NULL
                ORDER BY w.word
            ''', (user_id, library_id))
        else:
            # Get globally unlearned words
            cursor.execute('''
                SELECT w.word, w.definition, w.source
                FROM words w
                JOIN library_words lw ON w.id = lw.word_id
                WHERE lw.library_id = ? AND w.learned = 0
                ORDER BY w.word
            ''', (library_id,))

        return cursor.fetchall()
    finally:
        search_engine._release_connection(conn)

# Story Generator Routes
@app.route('/story-generator', methods=['GET', 'POST'])
def story_generator():
    """Generate stories using vocabulary words"""
    if request.method == 'POST':
        try:
            # Get form data
            word_source = request.form.get('word_source', 'random')
            word_count = int(request.form.get('word_count', 5))
            theme = request.form.get('theme', 'adventure')
            model_preference = request.form.get('model_preference', 'auto')

            # Get words based on source
            words = []
            if word_source == 'random':
                # Get random words
                random_words = search_engine.get_random_words(word_count)
                words = [word[0] for word in random_words] if random_words else []
            elif word_source == 'library':
                # Get words from a library
                library_id = request.form.get('library_id')
                if not library_id:
                    flash("Please select a library.", "warning")
                    return redirect(url_for('story_generator'))

                # Get words from the library
                library_words = search_engine.get_words_in_library(library_id)
                if library_words:
                    import random
                    selected_words = random.sample(library_words, min(word_count, len(library_words)))
                    words = [word['word'] for word in selected_words]
                else:
                    words = []
            elif word_source == 'custom':
                # Get custom words
                word_list = request.form.get('word_list', '').strip()
                if not word_list:
                    flash("Please enter at least one word.", "warning")
                    return redirect(url_for('story_generator'))

                # Split the text into individual words
                words = [w.strip() for w in word_list.split(',') if w.strip()]

            # Check if we have any words
            if not words:
                flash("No words available for story generation. Please try a different selection.", "warning")
                return redirect(url_for('story_generator'))

            # Generate story using the search engine's story generation capability
            try:
                story = search_engine.generate_story(words, theme, model_preference)
                if not story:
                    story = search_engine.generate_simple_story(words, theme)
            except Exception as e:
                app.logger.error(f"Error generating story: {e}")
                # Fallback to a simple story
                story = f"Once upon a time, there was a story that included these words: {', '.join(words)}. The theme was {theme}."

            # Return the story result page
            return render_template('story_result.html',
                                 story=story,
                                 words=words,
                                 theme=theme)

        except Exception as e:
            app.logger.error(f"Error in story generator: {e}")
            flash("An error occurred while generating the story. Please try again.", "danger")
            return redirect(url_for('story_generator'))

    # GET request - show the story generator form
    user_libraries = []
    if hasattr(g, 'user') and g.user:
        user_libraries = search_engine.get_libraries_for_user(g.user.id)

    return render_template('story_generator.html', libraries=user_libraries)

# Delete Library Route
@app.route('/library/<int:library_id>/delete', methods=['POST'])
@login_required
def delete_library(library_id):
    """Delete a library"""
    try:
        # Get library details to check ownership
        library = search_engine.get_library_details(library_id)
        if not library:
            flash('Library not found!', 'danger')
            return redirect(url_for('libraries'))

        # Delete the library using search engine
        result = search_engine.delete_library(library_id)

        if result is True:
            flash(f'Library "{library[1]}" deleted successfully!', 'success')
        else:
            # If result is a string, it's an error message
            error_msg = result if isinstance(result, str) else 'Failed to delete library.'
            flash(error_msg, 'danger')

    except Exception as e:
        app.logger.error(f"Error deleting library: {e}")
        flash('An error occurred while deleting the library.', 'danger')

    return redirect(url_for('libraries'))

# Google Search Route
@app.route('/google-search/<word>')
def google_search(word):
    """Redirect to Google search for a word"""
    import urllib.parse
    encoded_word = urllib.parse.quote(f"define {word}")
    google_url = f"https://www.google.com/search?q={encoded_word}"
    return redirect(google_url)

# Play Pronunciation Route
@app.route('/play-pronunciation/<word>')
def play_pronunciation(word):
    """Return pronunciation data for a word"""
    try:
        # This is a placeholder - you could integrate with a pronunciation API
        # For now, just return that browser speech synthesis should be used
        _ = word  # Suppress unused parameter warning
        return jsonify({
            'success': False,
            'message': 'Pronunciation service not available. Using browser speech synthesis.'
        })
    except Exception as e:
        app.logger.error(f"Error getting pronunciation: {e}")
        return jsonify({
            'success': False,
            'message': 'Error getting pronunciation'
        })

if __name__ == '__main__':
    print("Starting GRE Vocab Learner application...")
    print("Access the web interface at: http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=True)