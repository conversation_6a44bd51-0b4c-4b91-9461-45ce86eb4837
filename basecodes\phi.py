import torch
from transformers import AutoModelForCausalLM, AutoTokenizer, pipeline
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Optional
import uvicorn
import gc # Garbage collector
import os
import threading
from concurrent.futures import ThreadPoolExecutor

# --- Configuration ---
MODEL_NAME = "microsoft/Phi-3-mini-4k-instruct"
# Check for CUDA availability and set device accordingly
CUDA_AVAILABLE = torch.cuda.is_available()
DEVICE = "cuda" if CUDA_AVAILABLE else "cpu"
# Set number of CPU threads for PyTorch
NUM_THREADS = os.cpu_count() or 4
torch.set_num_threads(NUM_THREADS)
print(f"Using device: {DEVICE} with {NUM_THREADS} CPU threads")

# --- Global Variables for Model and Tokenizer ---
model = None
tokenizer = None
pipe = None
compiled_model = None  # For torch.compile
executor = ThreadPoolExecutor(max_workers=2)  # For parallel processing

# --- FastAPI App Initialization ---
app = FastAPI(
    title="Phi-3 Mini API",
    description="A simple API to interact with the Phi-3 Mini Instruct model with GPU+CPU optimization.",
    version="0.1.0"
)

# --- Pydantic Models for Request Body ---
class PromptRequest(BaseModel):
    prompt: str
    max_new_tokens: int = 256 # Max tokens to generate
    temperature: float = 0.7
    do_sample: bool = True

class StoryRequest(BaseModel):
    words: List[str]  # List of words to include in the story
    theme: str = "adventure"  # Theme of the story
    characters: str = ""  # Optional characters to include
    scenario: str = ""  # Optional scenario to include
    max_new_tokens: int = 1024  # Stories need more tokens
    temperature: float = 0.8  # Slightly more creative for stories
    do_sample: bool = True

# --- Event Handlers for Model Loading/Unloading ---
@app.on_event("startup")
async def startup_event():
    global model, tokenizer, pipe, compiled_model
    print("Loading model and tokenizer...")
    try:
        tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME, trust_remote_code=True)
        
        # Load model with optimized settings
        if CUDA_AVAILABLE:
            # For GPU: Use mixed precision and optimize memory usage
            model = AutoModelForCausalLM.from_pretrained(
                MODEL_NAME,
                torch_dtype=torch.bfloat16,  # Use bfloat16 for better performance/memory tradeoff
                device_map="auto",  # Let accelerate decide optimal device mapping
                trust_remote_code=True,
                # Use low_cpu_mem_usage for more efficient loading
                low_cpu_mem_usage=True,
            )
            
            # Apply torch.compile for faster inference if using PyTorch 2.0+
            if hasattr(torch, 'compile'):
                print("Applying torch.compile for optimized execution...")
                try:
                    # Use inductor backend with CUDA graphs for optimal performance
                    compiled_model = torch.compile(
                        model, 
                        backend="inductor",
                        mode="reduce-overhead",
                        options={"triton.cudagraphs": True} if CUDA_AVAILABLE else {}
                    )
                    print("Model successfully compiled")
                except Exception as e:
                    print(f"Warning: torch.compile failed: {e}. Falling back to standard model.")
                    compiled_model = model
            else:
                compiled_model = model
                print("torch.compile not available in this PyTorch version")
        else:
            # For CPU-only: Optimize for CPU inference
            model = AutoModelForCausalLM.from_pretrained(
                MODEL_NAME,
                device_map="cpu",
                trust_remote_code=True,
                # Use int8 quantization for CPU if bitsandbytes is available
                # load_in_8bit=True,
            )
            compiled_model = model
        
        print("Model and tokenizer loaded successfully.")

        # Create a Hugging Face pipeline for easier text generation
        pipe = pipeline(
            "text-generation",
            model=compiled_model,  # Use the compiled model if available
            tokenizer=tokenizer,
        )
        print("Text generation pipeline created.")

    except Exception as e:
        print(f"Error loading model: {e}")
        # You might want to raise an exception here or handle it gracefully
        # For this example, we'll let the server start but endpoints will fail.
        # raise HTTPException(status_code=500, detail=f"Model loading failed: {e}")


@app.on_event("shutdown")
async def shutdown_event():
    global model, tokenizer, pipe, compiled_model, executor
    print("Releasing model and tokenizer...")
    executor.shutdown(wait=True)
    del pipe
    del compiled_model
    del model
    del tokenizer
    gc.collect()
    if CUDA_AVAILABLE:
        torch.cuda.empty_cache()
    print("Resources released.")

# --- Helper Functions ---
def generate_text_with_model(messages, generation_args):
    """Helper function to generate text with the model"""
    try:
        output = pipe(messages, **generation_args)
        return output, None
    except Exception as e:
        return None, str(e)

# --- API Endpoint ---
@app.post("/generate")
async def generate_text(request: PromptRequest):
    if not model or not tokenizer or not pipe:
        raise HTTPException(status_code=503, detail="Model is not loaded or not ready.")

    # Phi-3 instruct format
    messages = [
        {"role": "user", "content": request.prompt},
    ]

    generation_args = {
        "max_new_tokens": request.max_new_tokens,
        "temperature": request.temperature,
        "do_sample": request.do_sample,
        "eos_token_id": tokenizer.eos_token_id,
    }
    if tokenizer.pad_token_id is None:
        generation_args["pad_token_id"] = tokenizer.eos_token_id

    print(f"Generating response for prompt: '{request.prompt[:50]}...' with args: {generation_args}")

    try:
        # Submit the generation task to the thread pool
        future = executor.submit(generate_text_with_model, messages, generation_args)
        output, error = future.result()
        
        if error:
            raise Exception(error)

        if output and isinstance(output, list) and output[0] and "generated_text" in output[0]:
            full_chat = output[0]["generated_text"]
            if isinstance(full_chat, list) and len(full_chat) > 0 and full_chat[-1]["role"] == "assistant":
                response_text = full_chat[-1]["content"]
            elif isinstance(full_chat, str): # If pipeline returns string
                 # Try to extract assistant part if the full string is returned
                assistant_prompt = tokenizer.apply_chat_template([{"role": "assistant", "content": ""}], tokenize=False, add_generation_prompt=False)
                if assistant_prompt in full_chat: # Basic check
                    response_text = full_chat.split(assistant_prompt)[-1].strip()
                else: # Fallback to just taking the last part if no explicit assistant role found in string.
                    input_len = len(tokenizer.apply_chat_template(messages, tokenize=False, add_generation_prompt=True))
                    response_text = full_chat[input_len:].strip() # Crude way, might need refinement
            else:
                response_text = "Could not parse assistant response from pipeline output."
        else:
            response_text = "Pipeline did not return expected output."

        print(f"Generated response: '{response_text[:100]}...'")
        return {"prompt": request.prompt, "response": response_text.strip()}

    except Exception as e:
        print(f"Error during generation: {e}")
        raise HTTPException(status_code=500, detail=f"Error during text generation: {str(e)}")

# --- API Endpoint for Story Generation ---
@app.post("/generate-story")
async def generate_story(request: StoryRequest):
    if not model or not tokenizer or not pipe:
        raise HTTPException(status_code=503, detail="Model is not loaded or not ready.")

    # Format the words with their definitions if available
    words_with_definitions = []
    for word in request.words:
        words_with_definitions.append(f"'{word}'")
    
    words_text = ", ".join(words_with_definitions)
    
    # Create a prompt for the story generation
    story_prompt = f"""Create an engaging short story that effectively teaches the following vocabulary words: {words_text}.

Theme: {request.theme}
{"Characters: " + request.characters if request.characters else ""}
{"Scenario: " + request.scenario if request.scenario else ""}

Guidelines:
1. Use each vocabulary word at least once in a way that clearly demonstrates its meaning
2. Bold each vocabulary word when first used
3. After the story, include a brief section explaining each word's meaning as used in the story
4. Keep the story engaging and appropriate for vocabulary learning
5. The story should be 300-500 words long

Story:"""

    messages = [
        {"role": "user", "content": story_prompt},
    ]

    generation_args = {
        "max_new_tokens": request.max_new_tokens,
        "temperature": request.temperature,
        "do_sample": request.do_sample,
        "eos_token_id": tokenizer.eos_token_id,
    }
    if tokenizer.pad_token_id is None:
        generation_args["pad_token_id"] = tokenizer.eos_token_id

    print(f"Generating story with theme: '{request.theme}' for words: {request.words}")

    try:
        # Submit the generation task to the thread pool
        future = executor.submit(generate_text_with_model, messages, generation_args)
        output, error = future.result()
        
        if error:
            raise Exception(error)

        if output and isinstance(output, list) and output[0] and "generated_text" in output[0]:
            full_chat = output[0]["generated_text"]
            if isinstance(full_chat, list) and len(full_chat) > 0 and full_chat[-1]["role"] == "assistant":
                story_text = full_chat[-1]["content"]
            elif isinstance(full_chat, str):
                assistant_prompt = tokenizer.apply_chat_template([{"role": "assistant", "content": ""}], tokenize=False, add_generation_prompt=False)
                if assistant_prompt in full_chat:
                    story_text = full_chat.split(assistant_prompt)[-1].strip()
                else:
                    input_len = len(tokenizer.apply_chat_template(messages, tokenize=False, add_generation_prompt=True))
                    story_text = full_chat[input_len:].strip()
            else:
                story_text = "Could not generate a story with the given words."
        else:
            story_text = "Story generation failed."

        return {
            "words": request.words,
            "theme": request.theme,
            "story": story_text.strip()
        }

    except Exception as e:
        print(f"Error during story generation: {e}")
        raise HTTPException(status_code=500, detail=f"Error during story generation: {str(e)}")

# --- Health Check Endpoint ---
@app.get("/health")
async def health_check():
    return {
        "status": "ok", 
        "model_ready": model is not None and tokenizer is not None and pipe is not None,
        "device": DEVICE,
        "cpu_threads": NUM_THREADS,
        "compiled_model": compiled_model is not None
    }

# --- Main Block to Run Uvicorn ---
if __name__ == "__main__":
    # If you have a multi-GPU setup and want to specify which GPU:
    # import os
    # os.environ["CUDA_VISIBLE_DEVICES"] = "0" # Use GPU 0

    uvicorn.run(app, host="0.0.0.0", port=8000)
