{% extends 'base.html' %}

{% block title %}Learning Dashboard{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1 class="mb-4">
        <i class="bi bi-graph-up"></i> Your Learning Dashboard
    </h1>
    
    <div class="row">
        <!-- Summary Cards -->
        <div class="col-md-3 mb-4">
            <div class="card bg-primary text-white h-100">
                <div class="card-body text-center">
                    <h1 class="display-4">{{ stats.total_words_learned }}</h1>
                    <h5>Words Learned</h5>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card bg-success text-white h-100">
                <div class="card-body text-center">
                    <h1 class="display-4">{{ stats.total_sessions }}</h1>
                    <h5>Learning Sessions</h5>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card bg-info text-white h-100">
                <div class="card-body text-center">
                    <h1 class="display-4">{{ stats.total_words_reviewed }}</h1>
                    <h5>Words Reviewed</h5>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card bg-warning text-dark h-100">
                <div class="card-body text-center">
                    <h1 class="display-4">{{ stats.average_session_length|round|int }}</h1>
                    <h5>Avg. Minutes/Session</h5>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Learning Progress -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Learning Progress</h5>
                </div>
                <div class="card-body">
                    <div class="progress" style="height: 30px;">
                        <div class="progress-bar bg-success" role="progressbar" 
                             style="width: {{ (stats.total_words_learned / total_words * 100) if total_words > 0 else 0 }}%;" 
                             aria-valuenow="{{ stats.total_words_learned }}" 
                             aria-valuemin="0" 
                             aria-valuemax="{{ total_words }}">
                            {{ stats.total_words_learned }} / {{ total_words }} Words ({{ (stats.total_words_learned / total_words * 100)|round|int if total_words > 0 else 0 }}%)
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <p>
                            <strong>Last session:</strong> 
                            {% if stats.last_session_date %}
                                {{ stats.last_session_date|datetime }}
                            {% else %}
                                No sessions yet
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Activity and Library Stats -->
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">Recent Activity</h5>
                </div>
                <div class="card-body">
                    {% if recent_sessions %}
                        <ul class="list-group">
                            {% for session in recent_sessions %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>{{ session.start_time|datetime }}</strong>
                                        <br>
                                        <small>{{ session.words_reviewed }} words reviewed</small>
                                    </div>
                                    <span class="badge bg-success rounded-pill">{{ session.words_learned }} learned</span>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i> No recent activity. Start a learning session!
                        </div>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('learning_session') }}" class="btn btn-primary">
                        <i class="bi bi-play-circle"></i> Start New Session
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">Your Libraries</h5>
                </div>
                <div class="card-body">
                    {% if libraries %}
                        <ul class="list-group">
                            {% for library in libraries %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>{{ library.name }}</strong>
                                        <br>
                                        <small>{{ library.description|truncate(50) }}</small>
                                    </div>
                                    <div>
                                        <span class="badge bg-primary rounded-pill">{{ library.word_count }} words</span>
                                        <a href="{{ url_for('view_library', library_id=library.id) }}" class="btn btn-sm btn-outline-primary ms-2">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </div>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i> No libraries created yet.
                        </div>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('create_library') }}" class="btn btn-success">
                        <i class="bi bi-plus-circle"></i> Create Library
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

