{% extends 'base.html' %}

{% block title %}Vocabulary Story{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header bg-primary text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="mb-0">
                    <i class="bi bi-book"></i> Your Vocabulary Story
                </h1>
                <div>
                    <button class="btn btn-light" id="saveStory">
                        <i class="bi bi-download"></i> Save Story
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="mb-4">
                <h5>Theme: {{ theme|title }}</h5>
                <h5>Words Used:</h5>
                <div class="d-flex flex-wrap gap-2 mb-3">
                    {% for word in words %}
                        <span class="badge bg-primary">{{ word }}</span>
                    {% endfor %}
                </div>
            </div>
            
            <div class="card">
                <div class="card-body story-content">
                    {{ story|safe }}
                </div>
            </div>
        </div>
        <div class="card-footer">
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('story_generator') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Back to Generator
                </a>
                
                <a href="{{ url_for('story_generator') }}?words={{ words|join(',') }}" class="btn btn-primary">
                    <i class="bi bi-arrow-repeat"></i> Regenerate with Same Words
                </a>
            </div>
        </div>
    </div>
</div>

<script>
    // Save functionality
    document.getElementById('saveStory').addEventListener('click', function() {
        const storyContent = document.querySelector('.story-content').innerText;
        const words = [{% for word in words %}'{{ word }}',{% endfor %}].join(', ');
        const theme = '{{ theme }}';
        
        const content = `VOCABULARY STORY\n\nTheme: ${theme}\nWords: ${words}\n\n${storyContent}`;
        
        const blob = new Blob([content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = 'vocabulary_story.txt';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    });
</script>
{% endblock %}


