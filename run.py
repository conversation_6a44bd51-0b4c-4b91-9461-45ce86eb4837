"""
Production-ready script to run the Flask application.
This script sets up proper logging and error handling.
"""
import os
import sys
import logging
from logging.handlers import RotatingFileHandler
import traceback

# Set environment variables
os.environ['FLASK_ENV'] = os.environ.get('FLASK_ENV', 'production')

# Apply patches if needed
try:
    import patch_werkzeug
except ImportError:
    print("Warning: patch_werkzeug.py not found. Compatibility issues may occur.")

# Import the Flask application
try:
    from app import app
except ImportError as e:
    print(f"Error importing app: {e}")
    traceback.print_exc()
    sys.exit(1)

# Set up logging
if not os.path.exists('logs'):
    os.mkdir('logs')

file_handler = RotatingFileHandler('logs/app.log', maxBytes=10240, backupCount=10)
file_handler.setFormatter(logging.Formatter(
    '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
))
file_handler.setLevel(logging.INFO)
app.logger.addHandler(file_handler)

app.logger.setLevel(logging.INFO)
app.logger.info('Application startup')

if __name__ == '__main__':
    # Get port from environment variable or use default
    port = int(os.environ.get('PORT', 5000))
    
    # In production, use a proper WSGI server
    if os.environ.get('FLASK_ENV') == 'production':
        try:
            # Try to use waitress (a production WSGI server)
            from waitress import serve
            app.logger.info(f'Starting waitress server on port {port}')
            serve(app, host='0.0.0.0', port=port)
        except ImportError:
            # Fall back to Flask's built-in server with debug disabled
            app.logger.warning('Waitress not installed, using Flask built-in server (not recommended for production)')
            app.run(host='0.0.0.0', port=port, debug=False)
    else:
        # Development mode
        app.logger.info(f'Starting development server on port {port}')
        app.run(debug=True, port=port)