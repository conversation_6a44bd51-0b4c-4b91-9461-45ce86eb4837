
import os
import gc
import json
import time
import threading
import uvicorn
from typing import List, Dict, Optional
from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
from concurrent.futures import ThreadPoolExecutor
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold

# --- Configuration ---
# Get API key from environment variable for security or use hardcoded key as fallback
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "YOUR_API_KEY_HERE")  # Replace with your actual API key
# Alternative: Hardcode your API key directly if you're not sharing this code
# GEMINI_API_KEY = "AIzaSyAUqGYw081u88eAL-gc2BvT3J3mzRGBIP0"  # Replace with your actual API key

if not GEMINI_API_KEY or GEMINI_API_KEY == "AIzaSyAUqGYw081u88eAL-gc2BvT3J3mzRGBIP0":
    raise ValueError("GEMINI_API_KEY environment variable not set or default value not replaced")

# Gemini model configuration
MODEL_NAME = "gemini-1.5-pro"  # Can be changed to other Gemini models
NUM_THREADS = os.cpu_count() or 4
print(f"Using {NUM_THREADS} CPU threads for processing")

# Configure the Gemini API
genai.configure(api_key=GEMINI_API_KEY)

# --- Global Variables ---
model = None
generation_config = None
safety_settings = None
executor = ThreadPoolExecutor(max_workers=2)  # For parallel processing

# --- FastAPI App Initialization ---
app = FastAPI(
    title="Gemini API Server",
    description="A simple API to interact with Google's Gemini model with optimized performance.",
    version="0.1.0"
)

# --- Pydantic Models for Request Body ---
class PromptRequest(BaseModel):
    prompt: str
    max_new_tokens: int = 256  # Max tokens to generate
    temperature: float = 0.7
    top_p: float = 0.95
    top_k: int = 40
    do_sample: bool = True

class StoryRequest(BaseModel):
    words: List[str]  # List of words to include in the story
    theme: str = "adventure"  # Theme of the story
    characters: str = ""  # Optional characters to include
    scenario: str = ""  # Optional scenario to include
    max_new_tokens: int = 1024  # Stories need more tokens
    temperature: float = 0.8  # Slightly more creative for stories
    do_sample: bool = True

# --- Event Handlers for Model Loading/Unloading ---
@app.on_event("startup")
async def startup_event():
    global model, generation_config, safety_settings
    print("Initializing Gemini model...")
    try:
        # Initialize the model
        model = genai.GenerativeModel(MODEL_NAME)
        
        # Default generation config
        generation_config = genai.GenerationConfig(
            temperature=0.7,
            top_p=0.95,
            top_k=40,
            max_output_tokens=256,
        )
        
        # Configure safety settings - using more permissive settings for creative content
        safety_settings = {
            HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        }
        
        print("Gemini model initialized successfully.")
    except Exception as e:
        print(f"Error initializing Gemini model: {e}")
        # We'll let the server start but endpoints will fail

@app.on_event("shutdown")
async def shutdown_event():
    global model, generation_config, safety_settings, executor
    print("Releasing resources...")
    executor.shutdown(wait=True)
    model = None
    generation_config = None
    safety_settings = None
    gc.collect()
    print("Resources released.")

# --- Helper Functions ---
def generate_text_with_model(prompt, gen_config, safety_config):
    """Helper function to generate text with the Gemini model"""
    try:
        # Create a copy of the model with specific configuration
        configured_model = genai.GenerativeModel(
            MODEL_NAME,
            generation_config=genai.GenerationConfig(**gen_config),
            safety_settings=safety_config
        )
        
        # Generate content
        response = configured_model.generate_content(prompt)
        
        # Check if the response was blocked
        if hasattr(response, 'prompt_feedback') and response.prompt_feedback and response.prompt_feedback.block_reason:
            return None, f"Content blocked: {response.prompt_feedback.block_reason}"
            
        return response, None
    except Exception as e:
        return None, str(e)

# --- API Endpoint ---
@app.post("/generate")
async def generate_text(request: PromptRequest):
    if not model:
        raise HTTPException(status_code=503, detail="Model is not initialized or not ready.")

    # Configure generation parameters
    gen_config = {
        "temperature": request.temperature,
        "top_p": request.top_p,
        "top_k": request.top_k,
        "max_output_tokens": request.max_new_tokens,
    }

    print(f"Generating response for prompt: '{request.prompt[:50]}...' with temperature: {request.temperature}")

    try:
        # Submit the generation task to the thread pool
        future = executor.submit(generate_text_with_model, request.prompt, gen_config, safety_settings)
        response, error = future.result()
        
        if error:
            raise Exception(error)

        if response and hasattr(response, 'text'):
            response_text = response.text
        else:
            response_text = "Could not generate response."

        print(f"Generated response: '{response_text[:100]}...'")
        return {"prompt": request.prompt, "response": response_text.strip()}

    except Exception as e:
        print(f"Error during generation: {e}")
        raise HTTPException(status_code=500, detail=f"Error during text generation: {str(e)}")

# --- API Endpoint for Story Generation ---
@app.post("/generate-story")
async def generate_story(request: StoryRequest):
    if not model:
        raise HTTPException(status_code=503, detail="Model is not initialized or not ready.")

    # Format the words
    words_text = ", ".join([f"'{word}'" for word in request.words])
    
    # Create a prompt for the story generation
    story_prompt = f"""Create an engaging short story that effectively teaches the following vocabulary words: {words_text}.

Theme: {request.theme}
{"Characters: " + request.characters if request.characters else ""}
{"Scenario: " + request.scenario if request.scenario else ""}

Guidelines:
1. Use each vocabulary word at least once in a way that clearly demonstrates its meaning
2. Bold each vocabulary word when first used
3. After the story, include a brief section explaining each word's meaning as used in the story
4. Keep the story engaging and appropriate for vocabulary learning
5. The story should be 300-500 words long

Story:"""

    # Configure generation parameters
    gen_config = {
        "temperature": request.temperature,
        "top_p": 0.95,
        "top_k": 40,
        "max_output_tokens": request.max_new_tokens,
    }

    print(f"Generating story with theme: '{request.theme}' for words: {request.words}")

    try:
        # Submit the generation task to the thread pool
        future = executor.submit(generate_text_with_model, story_prompt, gen_config, safety_settings)
        response, error = future.result()
        
        if error:
            raise Exception(error)

        if response and hasattr(response, 'text'):
            story_text = response.text
        else:
            story_text = "Could not generate a story with the given words."

        return {
            "words": request.words,
            "theme": request.theme,
            "story": story_text.strip()
        }

    except Exception as e:
        print(f"Error during story generation: {e}")
        raise HTTPException(status_code=500, detail=f"Error during story generation: {str(e)}")

# --- Health Check Endpoint ---
@app.get("/health")
async def health_check():
    return {
        "status": "ok", 
        "model_ready": model is not None,
        "model_name": MODEL_NAME,
        "cpu_threads": NUM_THREADS,
        "api_configured": GEMINI_API_KEY is not None
    }

# --- Main Block to Run Uvicorn ---
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)

