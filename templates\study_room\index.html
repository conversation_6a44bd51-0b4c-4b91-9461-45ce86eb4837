{% extends "base.html" %}

{% block title %}Study Room - GRE Vocabulary Learner{% endblock %}

{% block extra_css %}
<style>
    .study-room-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 15px;
        color: white;
        transition: transform 0.3s ease;
    }
    
    .study-room-card:hover {
        transform: translateY(-5px);
    }
    
    .timer-display {
        font-size: 3rem;
        font-weight: bold;
        color: var(--primary-color);
    }
    
    .task-item {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
    }
    
    .task-item:hover {
        background: rgba(255, 255, 255, 0.1);
    }
    
    .task-priority-high {
        border-left: 4px solid #dc3545;
    }
    
    .task-priority-medium {
        border-left: 4px solid #ffc107;
    }
    
    .task-priority-low {
        border-left: 4px solid #28a745;
    }
    
    .session-stats {
        background: linear-gradient(135deg, #ff7722 0%, #ff9955 100%);
        border-radius: 15px;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="bi bi-clock"></i> Study Room
            </h1>
        </div>
    </div>
    
    {% if active_session %}
    <!-- Active Study Session -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card study-room-card">
                <div class="card-body text-center">
                    <h3><i class="bi bi-play-circle"></i> Active Session: {{ active_session.title }}</h3>
                    <div class="timer-display" id="session-timer">00:00:00</div>
                    <p class="mb-3">{{ active_session.description or "Focus and achieve your goals!" }}</p>
                    
                    <div class="d-flex justify-content-center gap-3">
                        <button class="btn btn-warning" onclick="pauseSession()">
                            <i class="bi bi-pause"></i> Pause
                        </button>
                        <button class="btn btn-success" onclick="completeSession()">
                            <i class="bi bi-check-circle"></i> Complete
                        </button>
                        <a href="{{ url_for('study_room.session_view', session_id=active_session.id) }}" class="btn btn-info">
                            <i class="bi bi-eye"></i> View Details
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <!-- Start New Session -->
    <div class="row mb-4">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-body text-center">
                    <h3><i class="bi bi-plus-circle"></i> Start a New Study Session</h3>
                    <p class="text-muted">Create a focused study environment with timer and background audio</p>
                    <a href="{{ url_for('study_room.start_session') }}" class="btn btn-primary btn-lg">
                        <i class="bi bi-play"></i> Start Session
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Dashboard Grid -->
    <div class="row">
        <!-- Tasks Section -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="bi bi-list-task"></i> Pending Tasks</h5>
                    <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addTaskModal">
                        <i class="bi bi-plus"></i> Add Task
                    </button>
                </div>
                <div class="card-body">
                    {% if pending_tasks %}
                        {% for task in pending_tasks[:5] %}
                        <div class="task-item task-priority-{{ task.priority }}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ task.title }}</h6>
                                    {% if task.description %}
                                    <p class="text-muted small mb-1">{{ task.description[:100] }}{% if task.description|length > 100 %}...{% endif %}</p>
                                    {% endif %}
                                    <small class="text-muted">
                                        {% if task.estimated_time %}
                                        <i class="bi bi-clock"></i> {{ task.estimated_time }} min
                                        {% endif %}
                                        {% if task.due_date %}
                                        <i class="bi bi-calendar"></i> {{ task.due_date.strftime('%m/%d') }}
                                        {% endif %}
                                    </small>
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="markTaskComplete({{ task.id }})">
                                            <i class="bi bi-check"></i> Complete
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="startTaskTimer({{ task.id }})">
                                            <i class="bi bi-play"></i> Start Timer
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        {% if pending_tasks|length > 5 %}
                        <div class="text-center mt-3">
                            <a href="{{ url_for('study_room.tasks') }}" class="btn btn-outline-primary">
                                View All Tasks ({{ pending_tasks|length }})
                            </a>
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center text-muted">
                            <i class="bi bi-list-task" style="font-size: 3rem; opacity: 0.3;"></i>
                            <p>No pending tasks. Add one to get started!</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Recent Sessions -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5><i class="bi bi-clock-history"></i> Recent Sessions</h5>
                </div>
                <div class="card-body">
                    {% if recent_sessions %}
                        {% for session in recent_sessions %}
                        <div class="d-flex justify-content-between align-items-center mb-3 p-3 rounded" style="background: rgba(255, 255, 255, 0.05);">
                            <div>
                                <h6 class="mb-1">{{ session.title }}</h6>
                                <small class="text-muted">
                                    {{ session.start_time.strftime('%m/%d/%Y %I:%M %p') }}
                                    {% if session.actual_duration %}
                                    - {{ session.actual_duration }} min
                                    {% endif %}
                                </small>
                            </div>
                            <span class="badge bg-{{ 'success' if session.status == 'completed' else 'warning' }}">
                                {{ session.status.title() }}
                            </span>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center text-muted">
                            <i class="bi bi-clock-history" style="font-size: 3rem; opacity: 0.3;"></i>
                            <p>No recent sessions. Start your first study session!</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Stats -->
    <div class="row">
        <div class="col-md-4 mb-3">
            <div class="card session-stats">
                <div class="card-body text-center">
                    <h3>{{ recent_sessions|length }}</h3>
                    <p class="mb-0">Total Sessions</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card session-stats">
                <div class="card-body text-center">
                    <h3>{{ pending_tasks|length }}</h3>
                    <p class="mb-0">Pending Tasks</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card session-stats">
                <div class="card-body text-center">
                    <h3>
                        {% set total_time = recent_sessions|selectattr('actual_duration')|map(attribute='actual_duration')|sum %}
                        {{ total_time or 0 }}
                    </h3>
                    <p class="mb-0">Minutes Studied</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Task Modal -->
<div class="modal fade" id="addTaskModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Task</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ url_for('study_room.create_task') }}" method="POST">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="title" class="form-label">Task Title *</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <label for="estimated_time" class="form-label">Estimated Time (minutes)</label>
                            <input type="number" class="form-control" id="estimated_time" name="estimated_time" min="1">
                        </div>
                        <div class="col-md-6">
                            <label for="priority" class="form-label">Priority</label>
                            <select class="form-select" id="priority" name="priority">
                                <option value="low">Low</option>
                                <option value="medium" selected>Medium</option>
                                <option value="high">High</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-3">
                        <label for="due_date" class="form-label">Due Date</label>
                        <input type="date" class="form-control" id="due_date" name="due_date">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Task</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Timer functionality
let sessionStartTime = {% if active_session %}'{{ active_session.start_time.isoformat() }}'{% else %}null{% endif %};
let timerInterval;

function updateTimer() {
    if (!sessionStartTime) return;
    
    const start = new Date(sessionStartTime);
    const now = new Date();
    const elapsed = Math.floor((now - start) / 1000);
    
    const hours = Math.floor(elapsed / 3600);
    const minutes = Math.floor((elapsed % 3600) / 60);
    const seconds = elapsed % 60;
    
    const display = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    
    const timerElement = document.getElementById('session-timer');
    if (timerElement) {
        timerElement.textContent = display;
    }
}

// Start timer if there's an active session
if (sessionStartTime) {
    updateTimer();
    timerInterval = setInterval(updateTimer, 1000);
}

// Session control functions
function pauseSession() {
    fetch(`{{ url_for('study_room.pause_session', session_id=active_session.id if active_session else 0) }}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    });
}

function completeSession() {
    if (!sessionStartTime) return;
    
    const start = new Date(sessionStartTime);
    const now = new Date();
    const actualDuration = Math.floor((now - start) / 60000); // in minutes
    
    fetch(`{{ url_for('study_room.complete_session', session_id=active_session.id if active_session else 0) }}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            actual_duration: actualDuration
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    });
}

// Task management functions
function markTaskComplete(taskId) {
    fetch(`{{ url_for('study_room.update_task', task_id=0) }}`.replace('0', taskId), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            status: 'completed'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    });
}

function startTaskTimer(taskId) {
    // This would integrate with the session timer
    alert('Task timer functionality coming soon!');
}
</script>
{% endblock %}
