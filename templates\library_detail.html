{% extends 'base.html' %}

{% block title %}{{ library_name }}{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h2 class="mb-0">{{ library_name }}</h2>
        <div class="btn-group">
            {% if not (current_library and current_library[0] == library_id) %}
                <form method="post" action="{{ url_for('set_current_library') }}" class="d-inline">
                    <input type="hidden" name="library_id" value="{{ library_id }}">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="bi bi-star"></i> Set as Default
                    </button>
                </form>
            {% else %}
                <span class="badge bg-success ms-2"><i class="bi bi-check-circle"></i> Current Default</span>
            {% endif %}
            <a href="{{ url_for('add_to_library', library_id=library_id) }}" class="btn btn-outline-primary">
                <i class="bi bi-plus-circle"></i> Add Words
            </a>
            <a href="{{ url_for('import_all_to_library', library_id=library_id) }}" class="btn btn-outline-primary">
                <i class="bi bi-cloud-download"></i> Import All Words
            </a>
            <a href="{{ url_for('export_library', library_id=library_id) }}" class="btn btn-outline-primary">
                <i class="bi bi-download"></i> Export
            </a>
            <a href="{{ url_for('edit_library', library_id=library_id) }}" class="btn btn-outline-secondary">
                <i class="bi bi-pencil"></i> Edit
            </a>
            {% if not (current_library and current_library[0] == library_id) %}
                <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteLibraryModal">
                    <i class="bi bi-trash"></i> Delete
                </button>
            {% endif %}
        </div>
    </div>
    
    <div class="card-body">
        {% set stats = get_library_stats(library_id) %}
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stat-card total-words">
                    <div class="stat-icon">
                        <i class="bi bi-book"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ stats.total }}</div>
                        <div class="stat-label">Total Words</div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card learned-words">
                    <div class="stat-icon">
                        <i class="bi bi-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ stats.learned }}</div>
                        <div class="stat-label">Learned Words</div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card remaining-words">
                    <div class="stat-icon">
                        <i class="bi bi-hourglass-split"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ stats.remaining }}</div>
                        <div class="stat-label">Words to Learn</div>
                    </div>
                </div>
            </div>
        </div>
        
        {% if words %}
        <!-- Only show Learned Words section -->
        <h4 class="mb-3"><i class="bi bi-check-circle"></i> Learned Words</h4>
        {% set learned_words = get_learned_words_from_library(library_id) %}
        {% if learned_words %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Word</th>
                        <th>Definition</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for word, definition, source, learned in learned_words %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ word }}</td>
                        <td>{{ definition|truncate(100) }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('search') }}?word={{ word }}" class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-info-circle"></i>
                                </a>
                                <form method="post" action="{{ url_for('mark_word') }}" class="d-inline">
                                    <input type="hidden" name="word" value="{{ word }}">
                                    <input type="hidden" name="action" value="unlearned">
                                    <input type="hidden" name="redirect_url" value="{{ url_for('library_detail', library_id=library_id) }}">
                                    <button type="submit" class="btn btn-sm btn-outline-warning">
                                        <i class="bi bi-x-circle"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info mt-3">
            <i class="bi bi-info-circle"></i> You haven't learned any words in this library yet.
        </div>
        {% endif %}

        <!-- Unlearned Words section -->
        <h4 class="mb-3 mt-4"><i class="bi bi-book"></i> Words to Learn</h4>
        {% set unlearned_words = get_unlearned_words_from_library(library_id) %}
        {% if unlearned_words %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Word</th>
                        <th>Definition</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for word, definition, source, learned in unlearned_words %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ word }}</td>
                        <td>{{ definition|truncate(100) }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('search') }}?word={{ word }}" class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-info-circle"></i>
                                </a>
                                <form method="post" action="{{ url_for('mark_word') }}" class="d-inline">
                                    <input type="hidden" name="word" value="{{ word }}">
                                    <input type="hidden" name="action" value="learned">
                                    <input type="hidden" name="redirect_url" value="{{ url_for('library_detail', library_id=library_id) }}">
                                    <button type="submit" class="btn btn-sm btn-outline-success">
                                        <i class="bi bi-check-circle"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-success mt-3">
            <i class="bi bi-emoji-smile"></i> Congratulations! You've learned all words in this library.
        </div>
        {% endif %}
        {% else %}
        <div class="alert alert-info">
            <i class="bi bi-info-circle"></i> This library doesn't have any words yet.
            <a href="{{ url_for('add_to_library', library_id=library_id) }}" class="btn btn-sm btn-primary ms-2">
                <i class="bi bi-plus-circle"></i> Add Words
            </a>
            <a href="{{ url_for('import_all_to_library', library_id=library_id) }}" class="btn btn-sm btn-primary ms-2">
                <i class="bi bi-cloud-download"></i> Import All Words
            </a>
        </div>
        {% endif %}
    </div>
    <div class="card-footer">
        <a href="{{ url_for('libraries') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Libraries
        </a>
    </div>
</div>

<!-- Delete Library Modal -->
<div class="modal fade" id="deleteLibraryModal" tabindex="-1" aria-labelledby="deleteLibraryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteLibraryModalLabel">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>Confirm Deletion
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <p class="mb-2">Are you sure you want to delete the library:</p>
                    <p class="fs-5 fw-bold text-center mb-2">"{{ library_name }}"</p>
                </div>
                <p class="text-danger">
                    <i class="bi bi-exclamation-circle me-1"></i>
                    <strong>This action cannot be undone.</strong> All words associated with this library will be removed from the library (but will remain in your database).
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-1"></i>Cancel
                </button>
                <form action="{{ url_for('delete_library', library_id=library_id) }}" method="post">
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash me-1"></i>Delete Library
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
