"""
Compatibility module to provide functions that have been removed from dependencies.
"""

def safe_str_cmp(a, b):
    """
    Replacement for werkzeug.security.safe_str_cmp which was removed in newer versions.
    This function performs a constant-time comparison of two strings.
    """
    if len(a) != len(b):
        return False
    
    result = 0
    for x, y in zip(a, b):
        result |= ord(x) ^ ord(y) if isinstance(x, str) else x ^ y
    
    return result == 0