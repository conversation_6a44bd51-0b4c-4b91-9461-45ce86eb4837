import requests
import json

api_url = "http://localhost:8000/generate"

payload = {
    "prompt": "Write a short story about a friendly robot learning to paint.",
    "max_new_tokens": 150,
    "temperature": 0.8
}

headers = {
    "Content-Type": "application/json"
}

try:
    response = requests.post(api_url, data=json.dumps(payload), headers=headers)
    response.raise_for_status() # Raises an exception for bad status codes
    result = response.json()
    print(f"Prompt: {result.get('prompt')}")
    print(f"Response: {result.get('response')}")
except requests.exceptions.RequestException as e:
    print(f"Error calling API: {e}")
    if hasattr(e, 'response') and e.response is not None:
        print(f"Response content: {e.response.text}")