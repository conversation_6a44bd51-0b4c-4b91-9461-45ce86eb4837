{% extends "base.html" %}

{% block title %}Start Study Session - GRE Vocabulary Learner{% endblock %}

{% block extra_css %}
<style>
    .session-form-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 20px;
        color: white;
    }
    
    .form-control, .form-select {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
    }
    
    .form-control:focus, .form-select:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: var(--primary-color);
        color: white;
        box-shadow: 0 0 0 0.2rem rgba(255, 119, 34, 0.25);
    }
    
    .form-control::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }
    
    .form-label {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 500;
    }
    
    .theme-card {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid transparent;
        border-radius: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .theme-card:hover {
        border-color: var(--primary-color);
        transform: translateY(-2px);
    }
    
    .theme-card.selected {
        border-color: var(--primary-color);
        background: rgba(255, 119, 34, 0.2);
    }
    
    .audio-preview {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 10px;
        padding: 1rem;
        margin-top: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card session-form-card">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <h2><i class="bi bi-play-circle"></i> Start New Study Session</h2>
                        <p class="mb-0">Create a focused study environment tailored to your needs</p>
                    </div>
                    
                    <form action="{{ url_for('study_room.start_session') }}" method="POST">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        
                        <!-- Basic Session Info -->
                        <div class="row mb-4">
                            <div class="col-md-8">
                                <label for="title" class="form-label">Session Title *</label>
                                <input type="text" class="form-control" id="title" name="title" 
                                       placeholder="e.g., GRE Vocabulary Review" required>
                            </div>
                            <div class="col-md-4">
                                <label for="planned_duration" class="form-label">Duration (minutes) *</label>
                                <input type="number" class="form-control" id="planned_duration" 
                                       name="planned_duration" min="5" max="480" value="25" required>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"
                                      placeholder="What will you focus on during this session?"></textarea>
                        </div>
                        
                        <!-- Background Theme Selection -->
                        <div class="mb-4">
                            <label class="form-label">Background Theme</label>
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <div class="theme-card p-3 text-center" data-theme="default">
                                        <i class="bi bi-circle" style="font-size: 2rem; color: #6c757d;"></i>
                                        <h6 class="mt-2 mb-0">Default</h6>
                                        <small>Clean & Simple</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="theme-card p-3 text-center" data-theme="forest">
                                        <i class="bi bi-tree" style="font-size: 2rem; color: #28a745;"></i>
                                        <h6 class="mt-2 mb-0">Forest</h6>
                                        <small>Nature Vibes</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="theme-card p-3 text-center" data-theme="ocean">
                                        <i class="bi bi-water" style="font-size: 2rem; color: #17a2b8;"></i>
                                        <h6 class="mt-2 mb-0">Ocean</h6>
                                        <small>Calm Waters</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="theme-card p-3 text-center" data-theme="space">
                                        <i class="bi bi-stars" style="font-size: 2rem; color: #6f42c1;"></i>
                                        <h6 class="mt-2 mb-0">Space</h6>
                                        <small>Cosmic Focus</small>
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" id="background_theme" name="background_theme" value="default">
                        </div>
                        
                        <!-- Background Audio -->
                        <div class="mb-4">
                            <label for="background_audio" class="form-label">Background Audio (Optional)</label>
                            <input type="url" class="form-control" id="background_audio" name="background_audio"
                                   placeholder="YouTube URL for background music/sounds">
                            <div class="form-text text-light">
                                <i class="bi bi-info-circle"></i> 
                                Enter a YouTube URL for background sounds like rain, white noise, or study music.
                            </div>
                            
                            <!-- Audio Preview -->
                            <div id="audio-preview" class="audio-preview d-none">
                                <h6><i class="bi bi-music-note"></i> Audio Preview</h6>
                                <div id="audio-info"></div>
                            </div>
                        </div>
                        
                        <!-- Quick Duration Presets -->
                        <div class="mb-4">
                            <label class="form-label">Quick Duration Presets</label>
                            <div class="d-flex gap-2 flex-wrap">
                                <button type="button" class="btn btn-outline-light btn-sm" onclick="setDuration(25)">
                                    25 min (Pomodoro)
                                </button>
                                <button type="button" class="btn btn-outline-light btn-sm" onclick="setDuration(45)">
                                    45 min
                                </button>
                                <button type="button" class="btn btn-outline-light btn-sm" onclick="setDuration(60)">
                                    1 hour
                                </button>
                                <button type="button" class="btn btn-outline-light btn-sm" onclick="setDuration(90)">
                                    1.5 hours
                                </button>
                                <button type="button" class="btn btn-outline-light btn-sm" onclick="setDuration(120)">
                                    2 hours
                                </button>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('study_room.index') }}" class="btn btn-outline-light">
                                <i class="bi bi-arrow-left"></i> Back
                            </a>
                            <button type="submit" class="btn btn-warning btn-lg">
                                <i class="bi bi-play"></i> Start Session
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Popular Study Playlists -->
    <div class="row mt-5">
        <div class="col-12">
            <h4 class="text-center mb-4">Popular Study Audio</h4>
            <div class="row g-3">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="bi bi-cloud-rain" style="font-size: 2rem; color: #17a2b8;"></i>
                            <h6 class="mt-2">Rain Sounds</h6>
                            <button class="btn btn-sm btn-outline-primary" 
                                    onclick="setAudio('https://www.youtube.com/watch?v=mPZkdNFkNps')">
                                Use This Audio
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="bi bi-soundwave" style="font-size: 2rem; color: #6c757d;"></i>
                            <h6 class="mt-2">White Noise</h6>
                            <button class="btn btn-sm btn-outline-primary" 
                                    onclick="setAudio('https://www.youtube.com/watch?v=nMfPqeZjc2c')">
                                Use This Audio
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="bi bi-music-note" style="font-size: 2rem; color: #28a745;"></i>
                            <h6 class="mt-2">Lo-Fi Study</h6>
                            <button class="btn btn-sm btn-outline-primary" 
                                    onclick="setAudio('https://www.youtube.com/watch?v=jfKfPfyJRdk')">
                                Use This Audio
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Theme selection
document.querySelectorAll('.theme-card').forEach(card => {
    card.addEventListener('click', function() {
        // Remove selected class from all cards
        document.querySelectorAll('.theme-card').forEach(c => c.classList.remove('selected'));
        
        // Add selected class to clicked card
        this.classList.add('selected');
        
        // Update hidden input
        document.getElementById('background_theme').value = this.dataset.theme;
    });
});

// Set default theme as selected
document.querySelector('[data-theme="default"]').classList.add('selected');

// Duration preset functions
function setDuration(minutes) {
    document.getElementById('planned_duration').value = minutes;
}

// Audio functions
function setAudio(url) {
    document.getElementById('background_audio').value = url;
    validateAudio();
}

// Audio URL validation
document.getElementById('background_audio').addEventListener('input', validateAudio);

function validateAudio() {
    const audioInput = document.getElementById('background_audio');
    const audioPreview = document.getElementById('audio-preview');
    const audioInfo = document.getElementById('audio-info');
    const url = audioInput.value.trim();
    
    if (!url) {
        audioPreview.classList.add('d-none');
        return;
    }
    
    // Basic YouTube URL validation
    const youtubeRegex = /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/;
    const match = url.match(youtubeRegex);
    
    if (match) {
        const videoId = match[1];
        audioInfo.innerHTML = `
            <div class="d-flex align-items-center">
                <img src="https://img.youtube.com/vi/${videoId}/mqdefault.jpg" 
                     alt="Video thumbnail" style="width: 80px; height: 60px; object-fit: cover; border-radius: 5px;">
                <div class="ms-3">
                    <div class="text-success">
                        <i class="bi bi-check-circle"></i> Valid YouTube URL
                    </div>
                    <small>Video ID: ${videoId}</small>
                </div>
            </div>
        `;
        audioPreview.classList.remove('d-none');
    } else if (url) {
        audioInfo.innerHTML = `
            <div class="text-warning">
                <i class="bi bi-exclamation-triangle"></i> 
                Please enter a valid YouTube URL
            </div>
        `;
        audioPreview.classList.remove('d-none');
    }
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const title = document.getElementById('title').value.trim();
    const duration = parseInt(document.getElementById('planned_duration').value);
    
    if (!title) {
        e.preventDefault();
        alert('Please enter a session title.');
        return;
    }
    
    if (!duration || duration < 5 || duration > 480) {
        e.preventDefault();
        alert('Please enter a valid duration between 5 and 480 minutes.');
        return;
    }
});
</script>
{% endblock %}
