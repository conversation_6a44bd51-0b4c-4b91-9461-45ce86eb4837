{% extends 'base.html' %}

{% block title %}Search Results{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>
            <i class="bi bi-search"></i> Search Results for "{{ query }}"
        </h1>
        <a href="{{ url_for('search') }}" class="btn btn-outline-primary">
            <i class="bi bi-arrow-left"></i> New Search
        </a>
    </div>

    {% if results %}
        <div class="card">
            <div class="card-header bg-light">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Found {{ results|length }} words</h5>

                    {% if libraries %}
                        <div class="dropdown">
                            <button class="btn btn-outline-primary dropdown-toggle" type="button" id="addToLibraryDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-plus-circle"></i> Add Selected to Library
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="addToLibraryDropdown">
                                {% for library in libraries %}
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="addSelectedToLibrary({{ library.id }}, '{{ library.name }}')">
                                            {{ library.name }}
                                        </a>
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    {% endif %}
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                </div>
                            </th>
                            <th>Word</th>
                            <th>Definition</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for word in results %}
                            <tr>
                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input word-checkbox" type="checkbox" value="{{ word.id }}" data-word="{{ word.word }}">
                                    </div>
                                </td>
                                <td>
                                    <a href="{{ url_for('word_detail', word=word.word) }}">
                                        {{ word.word }}
                                    </a>
                                </td>
                                <td>{{ word.definition|truncate(100) }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('word_detail', word=word.word) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye"></i> View
                                        </a>
                                        <a href="{{ url_for('learning_session') }}?word={{ word.word }}" class="btn btn-sm btn-outline-success">
                                            <i class="bi bi-play-circle"></i> Learn
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <button class="btn btn-outline-primary" onclick="generateStoryFromSelected()">
                            <i class="bi bi-book"></i> Generate Story with Selected
                        </button>
                    </div>
                    <div>
                        <button class="btn btn-outline-success" onclick="learnSelected()">
                            <i class="bi bi-play-circle"></i> Learn Selected Words
                        </button>
                    </div>
                </div>
            </div>
        </div>
    {% else %}
        <div class="alert alert-info">
            <i class="bi bi-info-circle"></i> No words found matching "{{ query }}". Try a different search term.
        </div>
    {% endif %}
</div>

<!-- Hidden forms for actions -->
<form id="addToLibraryForm" method="post" action="" style="display: none;">
    <input type="hidden" name="word_ids" id="addToLibraryWordIds">
</form>

<form id="generateStoryForm" method="post" action="{{ url_for('story_generator') }}" style="display: none;">
    <input type="hidden" name="custom_word_list" id="storyWordList">
    <input type="hidden" name="word_source" value="custom">
</form>

<form id="learnWordsForm" method="post" action="{{ url_for('learning_session') }}" style="display: none;">
    <input type="hidden" name="word_list" id="learnWordList">
</form>

<script>
    function toggleSelectAll() {
        const selectAll = document.getElementById('selectAll');
        const checkboxes = document.querySelectorAll('.word-checkbox');

        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll.checked;
        });
    }

    function getSelectedWordIds() {
        const checkboxes = document.querySelectorAll('.word-checkbox:checked');
        return Array.from(checkboxes).map(checkbox => checkbox.value);
    }

    function getSelectedWords() {
        const checkboxes = document.querySelectorAll('.word-checkbox:checked');
        return Array.from(checkboxes).map(checkbox => checkbox.dataset.word);
    }

    function addSelectedToLibrary(libraryId, libraryName) {
        const wordIds = getSelectedWordIds();

        if (wordIds.length === 0) {
            alert('Please select at least one word to add to the library.');
            return;
        }

        if (confirm(`Add ${wordIds.length} selected word(s) to "${libraryName}"?`)) {
            const form = document.getElementById('addToLibraryForm');
            form.action = "/library/" + libraryId + "/add-multiple-words";

            document.getElementById('addToLibraryWordIds').value = wordIds.join(',');
            form.submit();
        }
    }

    function generateStoryFromSelected() {
        const words = getSelectedWords();

        if (words.length === 0) {
            alert('Please select at least one word to include in the story.');
            return;
        }

        if (words.length > 10) {
            alert('Please select 10 or fewer words for the story.');
            return;
        }

        document.getElementById('storyWordList').value = words.join(',');
        document.getElementById('generateStoryForm').submit();
    }

    function learnSelected() {
        const words = getSelectedWords();

        if (words.length === 0) {
            alert('Please select at least one word to learn.');
            return;
        }

        document.getElementById('learnWordList').value = words.join(',');
        document.getElementById('learnWordsForm').submit();
    }
</script>
{% endblock %}
