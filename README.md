# GRE Vocabulary Learning Tool

A comprehensive application designed to help users prepare for the GRE by expanding their vocabulary through interactive learning tools, spaced repetition, and AI-powered features.

## Overview

This application provides a complete solution for GRE vocabulary preparation, combining traditional learning methods with modern AI technologies. It allows users to:

- Search and explore vocabulary words
- Learn through interactive flashcards and quizzes
- Create custom word libraries
- Track learning progress
- Generate AI-powered stories using vocabulary words
- Manage user accounts and progress
- Sync data with Google Drive for cloud backup

## Features

### 1. Search & Explore

The search functionality allows users to:
- Look up words and their definitions
- View example sentences
- Access phonetic information and pronunciation
- Mark words as learned or to review later

The search engine uses a Binary Search Tree (BST) for efficient word retrieval and supports both exact and partial matching.

### 2. Interactive Learning

The learning system implements spaced repetition techniques to optimize vocabulary retention:
- Flashcard-based learning sessions
- Adaptive difficulty based on user performance
- Progress tracking across learning sessions
- Customizable learning pace

The application recommends words to study based on learning history and difficulty level, prioritizing words that need review.

### 3. Custom Libraries

Users can organize their vocabulary study with custom libraries:
- Create topic-specific word collections
- Import vocabulary lists from CSV files
- Copy words between libraries
- Track progress for each library separately

The application comes with pre-populated libraries, including a comprehensive "Mongoose" library containing common GRE vocabulary words.

### 4. Word of the Day

A daily vocabulary feature that:
- Presents a new word each day
- Shows definition, examples, and usage
- Provides quick access to detailed information
- Helps build vocabulary consistently over time

The word selection uses a deterministic algorithm based on the current date to ensure consistency.

### 5. AI-Powered Story Generation

An innovative feature that creates custom stories incorporating vocabulary words:
- Select words to include in the story
- Choose story themes and characters
- Generate engaging narratives that demonstrate word usage in context
- Includes explanations of each word's meaning as used in the story

The application supports multiple AI models:
- Phi-3 Mini for efficient local generation
- Google's Gemini for more advanced narratives
- Integration with LM Studio for custom models

### 6. User Management System

The application includes a comprehensive user management system:
- User registration and authentication
- Personal profiles with learning statistics
- Progress tracking across devices
- Custom settings and preferences
- Password reset functionality
- Social login options (Google, Facebook)

### 7. Admin Control Panel

Administrators have access to a powerful control panel:
- User management (create, edit, delete users)
- Usage statistics and analytics
- Content management for word libraries
- System settings and configuration
- Backup and restore functionality
- User activity monitoring

### 8. Cloud Synchronization

The application integrates with Google Drive for data storage:
- Automatic backup of user progress
- Synchronization across multiple devices
- Secure storage of vocabulary libraries
- Version history and recovery options
- Offline mode with synchronization when online

## Technical Architecture

The application is built with:
- Flask web framework for the backend
- SQLite database for local data storage
- Google Drive API for cloud synchronization
- Bootstrap for responsive frontend design
- Multiple AI models for story generation
- Flask-Login for authentication
- Flask-Admin for the admin interface

### Key Components:

1. **SearchEngine**: Core class that handles word storage, retrieval, and management
2. **BinarySearchTree**: Data structure for efficient word lookup
3. **RecommendationEngine**: Manages word recommendations and learning algorithms
4. **AI Integration**: Connects to various AI models for story generation
5. **UserManager**: Handles user authentication and profile management
6. **SyncManager**: Manages Google Drive synchronization
7. **AdminPanel**: Provides administrative controls and analytics

## Getting Started

1. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

2. Configure Google Drive API:
   - Create a Google Cloud project
   - Enable the Google Drive API
   - Create OAuth credentials
   - Download the credentials.json file to the project directory

3. Run the application:
   ```
   python app.py
   ```

4. Access the web interface at `http://localhost:5000`

## Data Management

The application uses SQLite databases to store:
- Vocabulary words and definitions
- Learning progress and history
- Custom libraries and word collections
- User profiles and authentication data
- Application settings and configurations

All data can be automatically synchronized with Google Drive for backup and multi-device access.

## User Roles and Permissions

The application supports multiple user roles:
- **Admin**: Full access to all features and admin panel
- **Teacher**: Can create and manage word libraries and monitor student progress
- **Student**: Standard user with access to learning features
- **Guest**: Limited access to basic features without an account

## Security Features

The application implements several security measures:
- Secure password hashing with bcrypt
- CSRF protection for all forms
- Rate limiting for login attempts
- Secure session management
- Data encryption for sensitive information
- OAuth 2.0 for third-party authentication

## AI Integration

The story generation feature supports multiple AI models:
- **Phi-3 Mini**: Efficient local model for basic story generation
- **Gemini**: Google's advanced model for higher-quality narratives
- **LM Studio**: Integration with custom local models

The application automatically selects the best available model or allows manual selection.

## Learning Methodology

The application implements proven vocabulary learning techniques:
- **Spaced Repetition**: Words are reviewed at increasing intervals
- **Contextual Learning**: Words are presented in meaningful contexts
- **Active Recall**: Interactive quizzes force active engagement
- **Narrative Association**: AI-generated stories create memorable associations

## Future Enhancements

Planned features include:
- Mobile application with offline support
- Advanced analytics and learning insights
- Additional AI-powered learning tools
- Expanded vocabulary datasets
- Collaborative learning features
- Integration with other learning platforms
