import csv

def search(word):
    word = word.lower().strip()
    found = False
    print(f"Searching for: '{word}'")
    
    with open('gre_master_wordlist.csv','r',encoding='utf-8') as file:
        reader = csv.reader(file)
        row_count = 0
        
        for row in reader:
            row_count += 1
            if not row:
                continue
            
            # Print the first few rows to see what we're working with
            if row_count <= 3:
                print(f"Row {row_count}: {row}")
            
            # Skip header row if it exists
            if row_count == 1 and row[0].lower() == "words":
                continue
                
            # Debug the comparison
            if row and len(row) > 0:
                # Clean the word from the CSV (remove "Barron4841" if present)
                csv_word = row[0].lower().strip()
                
                if row_count <= 20:  # Only print first 20 comparisons
                    print(f"Comparing: '{csv_word}' == '{word}' -> {csv_word == word}")
                
                if csv_word == word:
                    found = True
                    print("\n" + "="*50)
                    print(f"Word: {row[0]}")
                    print(f"Definition: {row[1]}")
                    print(f"Row number: {row_count}")
                    print("="*50 + "\n")
                    break
    
    if not found:
        print(f"\nWord '{word}' not found in vocabulary list (checked {row_count} rows)\n")

# Test with a word from the file
search('abate')
