# Core Flask dependencies
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-WTF==1.1.1
WTForms==3.0.1

# Security dependencies
Werkzeug==2.3.7
pyotp==2.9.0
qrcode[pil]==7.4.2
cryptography==41.0.7

# Database
SQLAlchemy==2.0.23

# File processing
pandas==2.1.3
chardet==5.2.0

# HTTP requests
requests==2.31.0

# Date/time handling
python-dateutil==2.8.2

# Image processing (for QR codes)
Pillow==10.1.0

# Development dependencies (optional)
python-dotenv==1.0.0

# Additional utilities
# Note: The following are built-in Python modules and don't need to be listed in requirements.txt
# secrets, hashlib, hmac, time, functools, os, re, io, base64, json, csv, tempfile, uuid, datetime, threading

# Text processing
markdown==3.5.1
