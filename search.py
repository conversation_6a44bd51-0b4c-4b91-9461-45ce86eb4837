import csv
import sqlite3
import random
import os
import sys
import requests
import json
import webbrowser
from typing import List, Dict, Tuple, Optional, Any, Union
from datetime import datetime
import tempfile
import threading
# Try to import optional dependencies
try:
    from gtts import gTTS
    from playsound import playsound
    AUDIO_SUPPORT = True
except ImportError:
    AUDIO_SUPPORT = False

# Increase recursion limit (temporary solution)
sys.setrecursionlimit(10000)

class TreeNode:
    def __init__(self, data, definition=None, source=None, learned=False):
        self.data = data
        self.definition = definition
        self.source = source
        self.learned = learned
        self.left = None
        self.right = None

class BinarySearchTree:
    def __init__(self):
        self.root = None

    def insert(self, word, definition=None, source=None, learned=False):
        """Insert a word into the BST using iteration instead of recursion"""
        if not word:
            return

        # Convert to lowercase for case-insensitive comparison
        word = word.lower()

        if self.root is None:
            self.root = TreeNode(word, definition, source, learned)
            return

        current = self.root
        while True:
            if word < current.data:
                if current.left is None:
                    current.left = TreeNode(word, definition, source, learned)
                    return
                current = current.left
            elif word > current.data:
                if current.right is None:
                    current.right = TreeNode(word, definition, source, learned)
                    return
                current = current.right
            else:
                # Word already exists, update its properties
                current.definition = definition
                current.source = source
                current.learned = learned
                return

    def _insert_recursive(self, node, word, definition, source, learned):
        """Recursively insert a word into the BST"""
        if word < node.data:
            if node.left is None:
                node.left = TreeNode(word, definition, source, learned)
            else:
                self._insert_recursive(node.left, word, definition, source, learned)
        elif word > node.data:
            if node.right is None:
                node.right = TreeNode(word, definition, source, learned)
            else:
                self._insert_recursive(node.right, word, definition, source, learned)
        else:
            # Word already exists, update its properties
            node.definition = definition
            node.source = source
            node.learned = learned

    def inorder_traversal(self):
        """Return all nodes in sorted order"""
        result = []
        self._inorder_recursive(self.root, result)
        return result

    def _inorder_recursive(self, node, result):
        if node:
            self._inorder_recursive(node.left, result)
            result.append((node.data, node.definition, node.source, node.learned))
            self._inorder_recursive(node.right, result)

    def search(self, word):
        """Search for a word in the BST"""
        if not word:
            return None

        # Convert to lowercase for case-insensitive search
        word = word.lower()
        return self._search_recursive(self.root, word)

    def _search_recursive(self, node, word):
        """Recursively search for a word in the BST"""
        if node is None or node.data == word:
            return node
        if word < node.data:
            return self._search_recursive(node.left, word)
        return self._search_recursive(node.right, word)

class SearchEngine:
    def __init__(self, db_name):
        self.db_name = db_name
        self.conn = None
        self.cursor = None
        self.bst = BinarySearchTree()  # Initialize BST
        self.connection_pool = []  # Connection pool
        self.max_pool_size = 5
        self.connection_lock = threading.Lock()  # Add thread lock
        self.ensure_tables_exist()
        self.load_bst_from_database()  # Load BST on initialization

    def _get_connection(self):
        """Get a thread-safe database connection"""
        try:
            # Create a new connection for each request to avoid threading issues
            conn = sqlite3.connect(self.db_name, check_same_thread=False)
            conn.row_factory = sqlite3.Row  # Enable row factory for named columns
            # Enable WAL mode for better concurrency
            conn.execute('PRAGMA journal_mode=WAL')
            return conn
        except Exception as e:
            print(f"Error creating database connection: {e}")
            raise

    def _release_connection(self, conn):
        """Close the database connection"""
        try:
            if conn:
                conn.close()
        except Exception as e:
            print(f"Error closing connection: {e}")

    def _execute_query(self, query, params=(), fetch_one=False, commit=False):
        """Execute a database query with proper error handling

        Args:
            query: SQL query string
            params: Query parameters
            fetch_one: If True, fetch one result; if False, fetch all
            commit: If True, commit the transaction

        Returns:
            Query results or None on error
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute(query, params)

            if commit:
                conn.commit()
                return cursor.lastrowid
            elif fetch_one:
                return cursor.fetchone()
            else:
                return cursor.fetchall()
        except Exception as e:
            print(f"Error executing query: {e}")
            print(f"Query: {query}")
            print(f"Params: {params}")
            import traceback
            traceback.print_exc()
            return None
        finally:
            self._release_connection(conn)

    def load_bst_from_database(self):
        """Load all words from the database into the BST"""
        try:
            words = self._execute_query(
                'SELECT word, definition, source, learned FROM words ORDER BY word'
            )

            if not words:
                return

            # Build a balanced BST
            self._build_bst_from_sorted_array(words, 0, len(words) - 1)
        except Exception as e:
            print(f"Error loading BST from database: {e}")
            import traceback
            traceback.print_exc()

    def _build_bst_from_sorted_array(self, sorted_words, start, end):
        """Build a balanced BST from a sorted array of words"""
        if start > end:
            return

        # Find the middle element and make it the root
        mid = (start + end) // 2
        word, definition, source, learned = sorted_words[mid]
        self.bst.insert(word, definition, source, learned)

        # Recursively build left and right subtrees
        self._build_bst_from_sorted_array(sorted_words, start, mid - 1)
        self._build_bst_from_sorted_array(sorted_words, mid + 1, end)

    def get_word(self, word):
        """Get a word from the database

        Args:
            word: The word to retrieve

        Returns:
            dict: Word data or None if not found
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()

            # Check if example column exists
            cursor.execute("PRAGMA table_info(words)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]
            has_example = 'example' in column_names

            if has_example:
                cursor.execute(
                    'SELECT id, word, definition, source, learned, example FROM words WHERE word = ?',
                    (word,)
                )
            else:
                cursor.execute(
                    'SELECT id, word, definition, source, learned FROM words WHERE word = ?',
                    (word,)
                )

            result = cursor.fetchone()
            if not result:
                return None

            # Convert to dictionary and ensure example field exists
            word_dict = dict(result)
            if not has_example:
                word_dict['example'] = ""  # Default empty example

            return word_dict
        except Exception as e:
            print(f"Error getting word: {e}")
            return None
        finally:
            self._release_connection(conn)

    def get_word_by_id(self, word_id):
        """Get a word by its ID

        Args:
            word_id: The word ID

        Returns:
            dict: Word data or None if not found
        """
        result = self._execute_query(
            'SELECT id, word, definition, source, learned FROM words WHERE id = ?',
            (word_id,),
            fetch_one=True
        )

        if not result:
            return None

        return dict(result)

    def insert_word(self, word: str, definition: str = None, source: str = None, learned: bool = False):
        """Insert a word into the database and return its ID

        Args:
            word: The word to insert
            definition: Definition of the word
            source: Source of the word
            learned: Whether the word is learned

        Returns:
            int: ID of the inserted/updated word
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()

            # Check if word already exists
            cursor.execute('SELECT id FROM words WHERE word = ?', (word,))
            existing = cursor.fetchone()

            if existing:
                # Update existing word
                word_id = existing[0]
                cursor.execute(
                    'UPDATE words SET definition = ?, source = ?, learned = ? WHERE id = ?',
                    (definition, source, learned, word_id)
                )
                conn.commit()

                # Update BST if it exists
                if hasattr(self, 'bst') and self.bst:
                    self.bst.insert(word, definition, source, learned)

                return word_id
            else:
                # Insert new word
                cursor.execute(
                    'INSERT INTO words (word, definition, source, learned) VALUES (?, ?, ?, ?)',
                    (word, definition, source, learned)
                )
                conn.commit()
                word_id = cursor.lastrowid

                # Update BST if it exists
                if hasattr(self, 'bst') and self.bst:
                    self.bst.insert(word, definition, source, learned)

                return word_id
        except Exception as e:
            print(f"Error inserting word: {e}")
            return None
        finally:
            self._release_connection(conn)

    def is_word_learned_by_user(self, word_id, user_id):
        """Check if a word is learned by a specific user

        Args:
            word_id: The word ID
            user_id: The user ID

        Returns:
            bool: True if learned, False otherwise
        """
        if user_id is None:
            return False

        result = self._execute_query(
            'SELECT id FROM learned_history WHERE word_id = ? AND user_id = ?',
            (word_id, user_id),
            fetch_one=True
        )

        return result is not None

    def mark_word_as_learned(self, word, user_id=None):
        """Mark a word as learned for a specific user

        Args:
            word: The word to mark as learned
            user_id: ID of the user, or None for global setting

        Returns:
            bool: True if successful, False otherwise
        """
        # Get word ID
        word_data = self.get_word(word)
        if not word_data:
            print(f"Word '{word}' not found in database")
            return False

        word_id = word_data['id']

        # Check if already marked as learned for this user
        if user_id is not None and self.is_word_learned_by_user(word_id, user_id):
            # Already learned
            return True

        # Add to learned_history
        self._execute_query(
            'INSERT INTO learned_history (word_id, user_id) VALUES (?, ?)',
            (word_id, user_id),
            commit=True
        )

        # Update the word's global learned status (for backward compatibility)
        self._execute_query(
            'UPDATE words SET learned = 1 WHERE id = ?',
            (word_id,),
            commit=True
        )

        # Update the BST
        node = self.bst.search(word)
        if node:
            node.learned = True

        return True

    def mark_word_as_unlearned(self, word, user_id=None):
        """Mark a word as unlearned for a specific user

        Args:
            word: The word to mark as unlearned
            user_id: ID of the user, or None for global setting

        Returns:
            bool: True if successful, False otherwise
        """
        # Get word ID
        word_data = self.get_word(word)
        if not word_data:
            print(f"Word '{word}' not found in database")
            return False

        word_id = word_data['id']

        # Remove from learned_history
        if user_id is not None:
            self._execute_query(
                'DELETE FROM learned_history WHERE word_id = ? AND user_id = ?',
                (word_id, user_id),
                commit=True
            )
        else:
            self._execute_query(
                'DELETE FROM learned_history WHERE word_id = ? AND user_id IS NULL',
                (word_id,),
                commit=True
            )

        # Check if the word is still learned by any user
        count = self._execute_query(
            'SELECT COUNT(*) as count FROM learned_history WHERE word_id = ?',
            (word_id,),
            fetch_one=True
        )

        # If no users have learned this word, update the global learned status
        if count and count['count'] == 0:
            self._execute_query(
                'UPDATE words SET learned = 0 WHERE id = ?',
                (word_id,),
                commit=True
            )

            # Update the BST
            node = self.bst.search(word)
            if node:
                node.learned = False

        return True

    def get_learned_words_for_user(self, user_id):
        """Get all words learned by a specific user

        Args:
            user_id: ID of the user

        Returns:
            list: List of (word, definition, source) tuples
        """
        if user_id is None:
            return []

        return self._execute_query('''
            SELECT w.word, w.definition, w.source
            FROM words w
            JOIN learned_history lh ON w.id = lh.word_id
            WHERE lh.user_id = ?
            ORDER BY w.word
        ''', (user_id,))

    def get_learning_words_for_user(self, user_id, count=10):
        """Get words for a user's learning session

        Args:
            user_id: ID of the user
            count: Number of words to return

        Returns:
            list: List of (word, definition, source) tuples
        """
        if user_id is None:
            return self.get_learning_words(count)

        # Get user's current library
        current_lib = self.get_current_library(user_id)

        if current_lib:
            # Get unlearned words from current library
            unlearned_words = self._execute_query('''
                SELECT w.word, w.definition, w.source
                FROM words w
                JOIN library_words lw ON w.id = lw.word_id
                LEFT JOIN learned_history lh ON w.id = lh.word_id AND lh.user_id = ?
                WHERE lw.library_id = ? AND lh.id IS NULL
                ORDER BY RANDOM()
                LIMIT ?
            ''', (user_id, current_lib, count))

            # If we got enough words, return them
            if unlearned_words and len(unlearned_words) >= count:
                return unlearned_words

            # Otherwise, get more words from other libraries
            remaining = count - (len(unlearned_words) if unlearned_words else 0)
            additional_words = self._execute_query('''
                SELECT w.word, w.definition, w.source
                FROM words w
                JOIN library_words lw ON w.id = lw.word_id
                JOIN libraries l ON lw.library_id = l.id
                LEFT JOIN learned_history lh ON w.id = lh.word_id AND lh.user_id = ?
                WHERE lh.id IS NULL
                AND lw.library_id != ?
                ORDER BY RANDOM()
                LIMIT ?
            ''', (user_id, current_lib, remaining))

            if unlearned_words and additional_words:
                return unlearned_words + additional_words
            elif additional_words:
                return additional_words
            elif unlearned_words:
                return unlearned_words

        # No current library or not enough words, get random unlearned words
        return self._execute_query('''
            SELECT w.word, w.definition, w.source
            FROM words w
            LEFT JOIN learned_history lh ON w.id = lh.word_id AND lh.user_id = ?
            WHERE lh.id IS NULL
            ORDER BY RANDOM()
            LIMIT ?
        ''', (user_id, count))

    def get_library_stats(self, library_id, user_id=None):
        """Get statistics for a library

        Args:
            library_id: ID of the library
            user_id: ID of the user, or None for global stats

        Returns:
            dict: Library statistics
        """
        # Get total words in library
        total_result = self._execute_query(
            'SELECT COUNT(*) as count FROM library_words WHERE library_id = ?',
            (library_id,),
            fetch_one=True
        )

        total = total_result['count'] if total_result else 0

        # Get learned words in library
        if user_id is not None:
            learned_result = self._execute_query(
                '''
                SELECT COUNT(*) as count
                FROM library_words lw
                JOIN learned_history lh ON lw.word_id = lh.word_id
                WHERE lw.library_id = ? AND lh.user_id = ?
                ''',
                (library_id, user_id),
                fetch_one=True
            )
        else:
            learned_result = self._execute_query(
                '''
                SELECT COUNT(*) as count
                FROM library_words lw
                JOIN words w ON lw.word_id = w.id
                WHERE lw.library_id = ? AND w.learned = 1
                ''',
                (library_id,),
                fetch_one=True
            )

        learned = learned_result['count'] if learned_result else 0

        return {
            'total': total,
            'learned': learned,
            'remaining': total - learned,
            'percent_complete': round((learned / total * 100) if total > 0 else 0, 1)
        }

    def get_library_learned_words(self, library_id, user_id=None):
        """Get learned words from a library

        Args:
            library_id: ID of the library
            user_id: ID of the user, or None for global learned words

        Returns:
            list: List of (word, definition, source) tuples
        """
        if user_id is not None:
            return self._execute_query('''
                SELECT w.word, w.definition, w.source
                FROM words w
                JOIN library_words lw ON w.id = lw.word_id
                JOIN learned_history lh ON w.id = lh.word_id
                WHERE lw.library_id = ? AND lh.user_id = ?
                ORDER BY w.word
            ''', (library_id, user_id))
        else:
            return self._execute_query('''
                SELECT w.word, w.definition, w.source
                FROM words w
                JOIN library_words lw ON w.id = lw.word_id
                WHERE lw.library_id = ? AND w.learned = 1
                ORDER BY w.word
            ''', (library_id,))

    def get_library_unlearned_words(self, library_id, user_id=None):
        """Get unlearned words from a library

        Args:
            library_id: ID of the library
            user_id: ID of the user, or None for global unlearned words

        Returns:
            list: List of (word, definition, source) tuples
        """
        if user_id is not None:
            return self._execute_query('''
                SELECT w.word, w.definition, w.source
                FROM words w
                JOIN library_words lw ON w.id = lw.word_id
                LEFT JOIN learned_history lh ON w.id = lh.word_id AND lh.user_id = ?
                WHERE lw.library_id = ? AND lh.id IS NULL
                ORDER BY w.word
            ''', (user_id, library_id))
        else:
            return self._execute_query('''
                SELECT w.word, w.definition, w.source
                FROM words w
                JOIN library_words lw ON w.id = lw.word_id
                WHERE lw.library_id = ? AND w.learned = 0
                ORDER BY w.word
            ''', (library_id,))

    def get_total_word_count(self):
        """Get the total number of words in the database"""
        result = self._execute_query(
            'SELECT COUNT(*) as count FROM words',
            fetch_one=True
        )

        return result['count'] if result else 0

    def get_user_libraries(self, user_id):
        """Get libraries created by a user

        Args:
            user_id: ID of the user

        Returns:
            list: List of (id, name, description, created_date) tuples
        """
        # For now, return all libraries since we don't have library ownership
        # In a future update, we would filter by user_id
        return self._execute_query('''
            SELECT id, name, description, created_date
            FROM libraries
            ORDER BY name
        ''')

    def ensure_tables_exist(self):
        """Create database tables if they don't exist"""
        conn = self._get_connection()
        try:
            cursor = conn.cursor()

            # Create words table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS words (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    word TEXT UNIQUE NOT NULL,
                    definition TEXT NOT NULL,
                    source TEXT,
                    learned INTEGER DEFAULT 0,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Create learned_history table for user-specific learned status
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS learned_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    word_id INTEGER NOT NULL,
                    user_id INTEGER,
                    learned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (word_id) REFERENCES words (id) ON DELETE CASCADE,
                    UNIQUE (word_id, user_id)
                )
            ''')

            # Check if user_id column exists in learned_history table (migration)
            cursor.execute("PRAGMA table_info(learned_history)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]
            if 'user_id' not in column_names:
                print("Adding user_id column to learned_history table...")
                try:
                    cursor.execute("ALTER TABLE learned_history ADD COLUMN user_id INTEGER")
                    conn.commit()
                    print("learned_history table migrated successfully")
                except Exception as e:
                    print(f"Error migrating learned_history table: {e}")
                    conn.rollback()
                    raise

            # Ensure learning_sessions table exists
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS learning_sessions (
                    id TEXT PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    end_time TIMESTAMP,
                    status TEXT DEFAULT 'active',
                    words_reviewed INTEGER DEFAULT 0,
                    words_learned INTEGER DEFAULT 0
                )
            ''')

            # Check if status column exists in learning_sessions table (migration)
            cursor.execute("PRAGMA table_info(learning_sessions)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]
            if 'status' not in column_names:
                print("Adding status column to learning_sessions table...")
                try:
                    cursor.execute("ALTER TABLE learning_sessions ADD COLUMN status TEXT DEFAULT 'active'")
                    conn.commit()
                    print("learning_sessions table migrated successfully")
                except Exception as e:
                    print(f"Error migrating learning_sessions table: {e}")
                    conn.rollback()
                    raise

            # Ensure session_words table exists
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS session_words (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    word_id INTEGER NOT NULL,
                    learned INTEGER DEFAULT 0,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (session_id) REFERENCES learning_sessions (id) ON DELETE CASCADE,
                    FOREIGN KEY (word_id) REFERENCES words (id) ON DELETE CASCADE
                )
            ''')

            # Create libraries table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS libraries (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    user_id INTEGER
                )
            ''')

            # Create library_words table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS library_words (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    library_id INTEGER NOT NULL,
                    word_id INTEGER NOT NULL,
                    FOREIGN KEY (library_id) REFERENCES libraries (id) ON DELETE CASCADE,
                    FOREIGN KEY (word_id) REFERENCES words (id) ON DELETE CASCADE,
                    UNIQUE (library_id, word_id)
                )
            ''')

            # Create user_settings table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER UNIQUE NOT NULL,
                    current_library_id INTEGER,
                    theme TEXT DEFAULT 'light',
                    daily_goal INTEGER DEFAULT 10,
                    FOREIGN KEY (current_library_id) REFERENCES libraries (id) ON DELETE SET NULL
                )
            ''')

            # Create learning_sessions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS learning_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    start_time TIMESTAMP NOT NULL,
                    end_time TIMESTAMP,
                    words_reviewed INTEGER DEFAULT 0,
                    words_learned INTEGER DEFAULT 0
                )
            ''')

            # Create session_words table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS session_words (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id INTEGER NOT NULL,
                    word_id INTEGER NOT NULL,
                    correct INTEGER DEFAULT 0,
                    FOREIGN KEY (session_id) REFERENCES learning_sessions (id) ON DELETE CASCADE,
                    FOREIGN KEY (word_id) REFERENCES words (id) ON DELETE CASCADE
                )
            ''')

            # Create examples table for word examples
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS examples (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    word_id INTEGER NOT NULL,
                    example_text TEXT NOT NULL,
                    source TEXT,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (word_id) REFERENCES words (id) ON DELETE CASCADE
                )
            ''')

            # Add missing columns to existing tables
            try:
                # Add user_id column to libraries table if it doesn't exist
                cursor.execute('ALTER TABLE libraries ADD COLUMN user_id INTEGER DEFAULT 1')
            except sqlite3.OperationalError:
                pass  # Column already exists

            try:
                # Add example column to words table if it doesn't exist
                cursor.execute('ALTER TABLE words ADD COLUMN example TEXT')
            except sqlite3.OperationalError:
                pass  # Column already exists

            try:
                # Add learned column to session_words table if it doesn't exist
                cursor.execute('ALTER TABLE session_words ADD COLUMN learned INTEGER DEFAULT 0')
            except sqlite3.OperationalError:
                pass  # Column already exists

            try:
                # Add user_id column to learned_history table if it doesn't exist
                cursor.execute('ALTER TABLE learned_history ADD COLUMN user_id INTEGER')
            except sqlite3.OperationalError:
                pass  # Column already exists

            try:
                # Add timestamp column to session_words table if it doesn't exist
                cursor.execute('ALTER TABLE session_words ADD COLUMN timestamp DATETIME DEFAULT CURRENT_TIMESTAMP')
            except sqlite3.OperationalError:
                pass  # Column already exists

            # Create indexes for better performance (with error handling)
            try:
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_words_word ON words (word)')
            except sqlite3.OperationalError:
                pass  # Index might already exist or table structure issue

            try:
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_learned_history_user ON learned_history (user_id)')
            except sqlite3.OperationalError:
                pass  # user_id column might not exist yet

            try:
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_learned_history_word ON learned_history (word_id)')
            except sqlite3.OperationalError:
                pass

            try:
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_library_words_library ON library_words (library_id)')
            except sqlite3.OperationalError:
                pass

            try:
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_library_words_word ON library_words (word_id)')
            except sqlite3.OperationalError:
                pass

            try:
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_settings_user ON user_settings (user_id)')
            except sqlite3.OperationalError:
                pass

            conn.commit()
        except Exception as e:
            print(f"Error creating tables: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self._release_connection(conn)

    def load_csv(self, filename):
        """Load words from a CSV file into the database"""
        words = []
        try:
            with open(filename, 'r', encoding='utf-8') as file:
                # Try to detect if it's a tab-delimited file
                first_line = file.readline()
                file.seek(0)  # Reset file pointer

                if '\t' in first_line:
                    delimiter = '\t'
                else:
                    delimiter = ','

                reader = csv.reader(file, delimiter=delimiter)
                for row in reader:
                    if len(row) >= 3:
                        words.append((row[0].strip(), row[1].strip(), row[2].strip()))
                    elif len(row) == 2:
                        words.append((row[0].strip(), row[1].strip(), None))
                    elif len(row) == 1 and row[0].strip():
                        words.append((row[0].strip(), None, None))

            # Insert words into database
            conn = self._get_connection()
            cursor = conn.cursor()
            for word, definition, source in words:
                cursor.execute('INSERT INTO words (word, definition, source) VALUES (?, ?, ?)', (word, definition, source))
            conn.commit()
            self._release_connection(conn)

            return len(words)
        except Exception as e:
            print(f"Error loading CSV: {e}")
            return 0
        finally:
            self.close()

    def search_word(self, word: str) -> Tuple[str, str, str, bool]:
        """Search for a word and return (word, definition, source, learned) if found"""
        try:
            # First try BST search if available
            if self.bst and self.bst.root:
                node = self.bst.search(word)
                if node:
                    return (node.data, node.definition, node.source, node.learned)

            # Fallback to database search
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute('SELECT word, definition, source, learned FROM words WHERE word = ?', (word,))
            result = cursor.fetchone()
            self._release_connection(conn)

            if result:
                return result
            return None
        except Exception as e:
            print(f"Error searching for word '{word}': {e}")
            return None

    def initialize_user_libraries(self, user_id: int):
        """Initialize user libraries - placeholder method for compatibility"""
        try:
            # This method is called during user loading but doesn't need to do anything
            # Libraries are handled separately in the application
            # Just ensure the user has access to default libraries
            pass
        except Exception as e:
            print(f"Error initializing user libraries: {e}")

    def get_all_words(self) -> List[Tuple[str, str, str, bool]]:
        """Get all words in sorted order using BST traversal"""
        if not self.bst or not self.bst.root:
            # If BST is empty, try to load from database
            try:
                conn = self._get_connection()
                cursor = conn.cursor()
                cursor.execute('SELECT word, definition, source, learned FROM words ORDER BY word')
                words = cursor.fetchall()
                self._release_connection(conn)
                for word, definition, source, learned in words:
                    self.bst.insert(word, definition, source, learned)
            except Exception as e:
                print(f"Error loading words from database: {e}")
                import traceback
                traceback.print_exc()
                return []

        return self.bst.inorder_traversal()

    def get_learned_words(self) -> List[Tuple[str, str, str, bool]]:
        """Get all learned words"""
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('''
                SELECT word, definition, source, learned
                FROM words
                WHERE learned = 1
                ORDER BY word
            ''')
            return cursor.fetchall()
        finally:
            self._release_connection(conn)

    def get_remaining_words(self) -> List[Tuple[str, str, str, bool]]:
        """Get all unlearned words"""
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('''
                SELECT word, definition, source, learned
                FROM words
                WHERE learned = 0
                ORDER BY word
            ''')
            return cursor.fetchall()
        finally:
            self._release_connection(conn)

    def get_libraries(self, user_id=None, include_hidden=False):
        """Get all libraries for a specific user

        Args:
            user_id: ID of the user whose libraries to get, or None for all libraries
            include_hidden: Whether to include hidden libraries

        Returns:
            list: List of (id, name, description, created_date) tuples
        """
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            # Check if libraries table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='libraries'")
            if not cursor.fetchone():
                # Create the table if it doesn't exist
                self.ensure_library_tables_exist()
                return []

            # Check if user_id column exists
            cursor.execute("PRAGMA table_info(libraries)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]
            has_user_id = 'user_id' in column_names

            # If user_id column doesn't exist, migrate the database
            if not has_user_id:
                self.migrate_database_for_user_libraries()
                # Check again after migration
                cursor.execute("PRAGMA table_info(libraries)")
                columns = cursor.fetchall()
                column_names = [column[1] for column in columns]
                has_user_id = 'user_id' in column_names

            # Build the query based on parameters
            query = '''
                SELECT id, name, description, created_date
                FROM libraries
                WHERE 1=1
            '''
            params = []

            # Add user_id filter if provided and column exists
            if user_id is not None and has_user_id:
                query += ' AND (user_id = ? OR user_id IS NULL)'
                params.append(user_id)

            # Add hidden filter if needed
            if 'hidden' in column_names and not include_hidden:
                query += ' AND (hidden = 0 OR hidden IS NULL)'

            # Add order by
            query += ' ORDER BY name'

            # Execute the query
            cursor.execute(query, params)
            return cursor.fetchall()
        except Exception as e:
            print(f"Error getting libraries: {e}")
            import traceback
            traceback.print_exc()
            return []
        finally:
            self._release_connection(conn)

    def get_library_words(self, library_id):
        """Get all words in a library

        Args:
            library_id: ID of the library

        Returns:
            list: List of (word, definition, source, learned) tuples
        """
        if not library_id:
            return []

        conn = self._get_connection()
        try:
            cursor = conn.cursor()

            # Check if library exists
            cursor.execute('SELECT id FROM libraries WHERE id = ?', (library_id,))
            if not cursor.fetchone():
                print(f"Library ID {library_id} not found")
                return []

            # Get words in the library
            cursor.execute('''
                SELECT w.word, w.definition, w.source, COALESCE(w.learned, 0) as learned
                FROM words w
                JOIN library_words lw ON w.id = lw.word_id
                WHERE lw.library_id = ?
                ORDER BY w.word
            ''', (library_id,))

            return cursor.fetchall()
        except Exception as e:
            print(f"Error getting library words: {e}")
            import traceback
            traceback.print_exc()
            return []
        finally:
            self._release_connection(conn)

    def mark_word_as_learned(self, word: str):
        """Mark a word as learned and record in history"""
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            # First get the word ID
            cursor.execute('SELECT id FROM words WHERE word = ?', (word,))
            result = cursor.fetchone()
            if result:
                word_id = result[0]

                # Update the learned status
                cursor.execute('UPDATE words SET learned = 1 WHERE id = ?', (word_id,))

                # Add to learned history if not already there
                cursor.execute('SELECT COUNT(*) FROM learned_history WHERE word_id = ?', (word_id,))
                if cursor.fetchone()[0] == 0:
                    cursor.execute('INSERT INTO learned_history (word_id) VALUES (?)', (word_id,))

                conn.commit()

                # Update the BST if it exists
                if hasattr(self, 'bst') and self.bst:
                    node = self.bst.search(word)
                    if node:
                        node.learned = True

                return True
            return False
        except Exception as e:
            print(f"Error marking word as learned: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            self._release_connection(conn)

    def mark_word_as_unlearned(self, word: str):
        """Mark a word as unlearned"""
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            # First get the word ID
            cursor.execute('SELECT id FROM words WHERE word = ?', (word,))
            result = cursor.fetchone()
            if result:
                word_id = result[0]

                # Update the learned status
                cursor.execute('UPDATE words SET learned = 0 WHERE id = ?', (word_id,))

                # Remove from learned history if exists
                cursor.execute('DELETE FROM learned_history WHERE word_id = ?', (word_id,))

                conn.commit()

                # Update the BST if it exists
                if hasattr(self, 'bst') and self.bst:
                    node = self.bst.search(word)
                    if node:
                        node.learned = False

                return True
            return False
        except Exception as e:
            print(f"Error marking word as unlearned: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            self._release_connection(conn)

    def get_learning_words(self, count=10):
        """Get words for a learning session"""
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            # Get unlearned words first
            cursor.execute('''
                SELECT word, definition, source, learned
                FROM words
                WHERE learned = 0
                ORDER BY RANDOM()
                LIMIT ?
            ''', (count,))
            words = cursor.fetchall()

            # If we don't have enough unlearned words, get some learned ones too
            if len(words) < count:
                remaining = count - len(words)
                cursor.execute('''
                    SELECT word, definition, source, learned
                    FROM words
                    WHERE learned = 1
                    ORDER BY RANDOM()
                    LIMIT ?
                ''', (remaining,))
                words.extend(cursor.fetchall())

            return words
        except Exception as e:
            print(f"Error getting learning words: {e}")
            return []
        finally:
            self._release_connection(conn)

    def get_random_words(self, count=5):
        """Get a random selection of words from the database"""
        conn = self._get_connection()
        try:
            cursor = conn.cursor()

            # Check if example column exists
            cursor.execute("PRAGMA table_info(words)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]
            has_example = 'example' in column_names

            if has_example:
                cursor.execute('''
                    SELECT word, definition, source, learned, example
                    FROM words
                    ORDER BY RANDOM()
                    LIMIT ?
                ''', (count,))
            else:
                cursor.execute('''
                    SELECT word, definition, source, learned
                    FROM words
                    ORDER BY RANDOM()
                    LIMIT ?
                ''', (count,))

            words = []
            for row in cursor.fetchall():
                word_dict = {
                    'word': row[0],
                    'definition': row[1],
                    'source': row[2],
                    'learned': row[3],
                    'example': row[4] if has_example and len(row) > 4 else ""
                }
                words.append(word_dict)

            return words
        except Exception as e:
            print(f"Error getting random words: {e}")
            return []
        finally:
            self._release_connection(conn)

    def get_total_word_count(self):
        """Get the total number of words in the database"""
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM words')
            count = cursor.fetchone()[0]
            return count
        except Exception as e:
            print(f"Error getting total word count: {e}")
            return 0
        finally:
            self._release_connection(conn)

    def get_learned_word_count(self):
        """Get the number of learned words in the database"""
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM words WHERE learned = 1')
            count = cursor.fetchone()[0]
            return count
        except Exception as e:
            print(f"Error getting learned word count: {e}")
            return 0
        finally:
            self._release_connection(conn)

    def mark_word_as_learned(self, word):
        """Mark a word as learned"""
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('UPDATE words SET learned = 1 WHERE word = ?', (word,))
            conn.commit()
            return True
        except Exception as e:
            print(f"Error marking word as learned: {e}")
            return False
        finally:
            self._release_connection(conn)

    def mark_word_as_unlearned(self, word):
        """Mark a word as not learned"""
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('UPDATE words SET learned = 0 WHERE word = ?', (word,))
            conn.commit()
            return True
        except Exception as e:
            print(f"Error marking word as unlearned: {e}")
            return False
        finally:
            self._release_connection(conn)

    def create_library(self, name: str, description: str = None, user_id: int = None) -> int:
        """Create a new library and return its ID

        Args:
            name: Name of the library
            description: Optional description
            user_id: ID of the user who owns this library

        Returns:
            int: ID of the created library, or None if failed
        """
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            try:
                cursor.execute(
                    'INSERT INTO libraries (name, description, user_id) VALUES (?, ?, ?)',
                    (name, description, user_id)
                )
                conn.commit()
                return cursor.lastrowid
            except sqlite3.IntegrityError:
                print(f"Library with name '{name}' already exists for user {user_id}")
                return None
        finally:
            self._release_connection(conn)

    def delete_library(self, library_id: int) -> Union[bool, str]:
        """Delete a library and its word associations

        Args:
            library_id: ID of the library to delete

        Returns:
            bool: True if successful
            str: Error message if failed
        """
        if not library_id:
            return "Invalid library ID"

        conn = self._get_connection()

        # Check if connection was successfully established
        if not conn:
            return "Failed to establish database connection"

        try:
            # Start a transaction
            conn.execute('BEGIN TRANSACTION')

            try:
                # Check if library exists
                cursor = conn.cursor()
                cursor.execute('SELECT id, name FROM libraries WHERE id = ?', (library_id,))
                library = cursor.fetchone()
                if not library:
                    if conn:  # Check if connection exists before rollback
                        conn.rollback()
                    return f"Library with ID {library_id} not found"

                # Check if it's the current library
                current_lib = self.get_current_library()
                if current_lib and current_lib == library_id:
                    if conn:  # Check if connection exists before rollback
                        conn.rollback()
                    return f"Cannot delete the current library '{library[1]}'. Please set another library as current first."

                # Get count of words in the library for reporting
                cursor.execute('SELECT COUNT(*) FROM library_words WHERE library_id = ?', (library_id,))
                word_count = cursor.fetchone()[0]

                # Delete word associations
                cursor.execute('DELETE FROM library_words WHERE library_id = ?', (library_id,))

                # Delete the library
                cursor.execute('DELETE FROM libraries WHERE id = ?', (library_id,))

                # Commit the transaction
                conn.commit()
                print(f"Successfully deleted library ID {library_id} with {word_count} word associations")
                return True
            except Exception as e:
                # Rollback in case of error
                if conn:  # Check if connection exists before rollback
                    conn.rollback()
                print(f"Error deleting library: {e}")
                import traceback
                traceback.print_exc()
                return f"Database error: {str(e)}"
        finally:
            self._release_connection(conn)

    def add_word_to_library(self, library_id: int, word: str) -> bool:
        """Add a word to a library"""
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            try:
                # Get word ID
                cursor.execute('SELECT id FROM words WHERE word = ?', (word,))
                result = cursor.fetchone()
                if not result:
                    print(f"Word '{word}' not found in database")
                    return False

                word_id = result[0]

                # Add to library
                cursor.execute(
                    'INSERT INTO library_words (library_id, word_id) VALUES (?, ?)',
                    (library_id, word_id)
                )
                conn.commit()
                return True
            except sqlite3.IntegrityError:
                # Word already in library
                return True
            except Exception as e:
                print(f"Error adding word to library: {e}")
                return False
        finally:
            self._release_connection(conn)

    def remove_word_from_library(self, library_id: int, word: str) -> bool:
        """Remove a word from a library"""
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            try:
                # Get word ID
                cursor.execute('SELECT id FROM words WHERE word = ?', (word,))
                result = cursor.fetchone()
                if not result:
                    print(f"Word '{word}' not found in database")
                    return False

                word_id = result[0]

                # Remove from library
                cursor.execute(
                    'DELETE FROM library_words WHERE library_id = ? AND word_id = ?',
                    (library_id, word_id)
                )
                conn.commit()
                return True
            except Exception as e:
                print(f"Error removing word from library: {e}")
                return False
        finally:
            self._release_connection(conn)

    def get_libraries(self, user_id=None):
        """Get all libraries for a user

        Args:
            user_id: ID of the user, or None for global libraries

        Returns:
            list: List of (id, name, description, created_date) tuples
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()

            if user_id is not None:
                # Get user's libraries and global libraries
                cursor.execute('''
                    SELECT id, name, description, created_date
                    FROM libraries
                    WHERE user_id = ? OR user_id IS NULL
                    ORDER BY name
                ''', (user_id,))
            else:
                # Get only global libraries
                cursor.execute('''
                    SELECT id, name, description, created_date
                    FROM libraries
                    WHERE user_id IS NULL
                    ORDER BY name
                ''')

            return cursor.fetchall()
        finally:
            self._release_connection(conn)

    def export_library(self, library_id: int, filename: str) -> bool:
        """Export a library to a CSV file"""
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            try:
                words = self.get_library_words(library_id)

                with open(filename, 'w', newline='', encoding='utf-8') as file:
                    writer = csv.writer(file, delimiter='\t')
                    for word, definition, source, _ in words:
                        if definition and source:
                            writer.writerow([word, definition, source])
                        elif definition:
                            writer.writerow([word, definition])
                        else:
                            writer.writerow([word])

                return True
            except Exception as e:
                print(f"Error exporting library: {e}")
                return False
        finally:
            self._release_connection(conn)

    def import_library(self, name: str, description: str, filename: str) -> int:
        """Import a CSV file as a new library"""
        try:
            # Create new library
            library_id = self.create_library(name, description)
            if not library_id:
                return None

            # Load words from CSV
            words = []
            with open(filename, 'r', encoding='utf-8') as file:
                # Detect delimiter
                sample = file.read(1024)
                file.seek(0)

                if '\t' in sample:
                    delimiter = '\t'
                else:
                    delimiter = ','

                reader = csv.reader(file, delimiter=delimiter)
                for row in reader:
                    if len(row) >= 3:
                        words.append((row[0].strip(), row[1].strip(), row[2].strip()))
                    elif len(row) == 2:
                        words.append((row[0].strip(), row[1].strip(), None))
                    elif len(row) == 1 and row[0].strip():
                        words.append((row[0].strip(), None, None))

            # Add words to database and library
            for word, definition, source in words:
                # Add to database if not exists
                self.insert_word(word, definition, source)

                # Add to library
                self.add_word_to_library(library_id, word)

            return library_id
        except Exception as e:
            print(f"Error importing library: {e}")
            return None

    def get_word_details_from_api(self, word: str) -> Dict:
        """Get detailed information about a word from the dictionary API"""
        try:
            response = requests.get(f"https://api.dictionaryapi.dev/api/v2/entries/en/{word}")
            if response.status_code == 200:
                return response.json()
            else:
                print(f"API error: {response.status_code}")
                return None
        except Exception as e:
            print(f"Error fetching word details: {e}")
            return None

    def get_word_examples(self, word: str) -> List[str]:
        """Get example sentences for a word from the API"""
        details = self.get_word_details_from_api(word)
        if not details:
            return []

        examples = []
        try:
            for entry in details:
                for meaning in entry.get('meanings', []):
                    for definition in meaning.get('definitions', []):
                        if 'example' in definition:
                            examples.append(definition['example'])
        except Exception as e:
            print(f"Error parsing examples: {e}")

        return examples

    def get_word_phonetics(self, word: str) -> List[Dict]:
        """Get phonetic information for a word from the API"""
        details = self.get_word_details_from_api(word)
        if not details:
            return []

        phonetics = []
        try:
            for entry in details:
                for phonetic in entry.get('phonetics', []):
                    if 'text' in phonetic or 'audio' in phonetic:
                        phonetics.append(phonetic)
        except Exception as e:
            print(f"Error parsing phonetics: {e}")

        return phonetics

    def play_pronunciation(self, word: str) -> bool:
        """Play the pronunciation of a word using text-to-speech or API audio"""
        if not AUDIO_SUPPORT:
            print("Audio support not available. Install gtts and playsound packages.")
            return False

        try:
            # First try to get audio from the API
            phonetics = self.get_word_phonetics(word)
            audio_url = None

            for phonetic in phonetics:
                if 'audio' in phonetic and phonetic['audio']:
                    audio_url = phonetic['audio']
                    break

            if audio_url:
                try:
                    # Download and play the audio file
                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
                    temp_file.close()

                    response = requests.get(audio_url)
                    with open(temp_file.name, 'wb') as f:
                        f.write(response.content)

                    playsound(temp_file.name)

                    # Clean up
                    os.unlink(temp_file.name)
                    return True
                except Exception as e:
                    print(f"Error playing audio from API: {e}")

            # Fallback to text-to-speech
            tts = gTTS(text=word, lang='en')
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
            temp_file.close()

            tts.save(temp_file.name)
            playsound(temp_file.name)

            # Clean up
            os.unlink(temp_file.name)
            return True
        except Exception as e:
            print(f"Error with text-to-speech: {e}")
            return False

    def search_google(self, word: str):
        """Open a Google search for the word in a web browser"""
        search_url = f"https://www.google.com/search?q=define+{word}"
        webbrowser.open(search_url)

    def close(self):
        """Close the database connection"""
        try:
            if self.cursor:
                self.cursor.close()
            if self.conn:
                self.conn.close()
            self.cursor = None
            self.conn = None
        except sqlite3.Error as e:
            print(f"Error closing database connection: {e}")
            import traceback
            traceback.print_exc()

    def debug_database_structure(self):
        """Print database structure for debugging"""
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            # Get table names
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()

            result = {}
            for table in tables:
                table_name = table[0]
                # Get column info for each table
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                result[table_name] = [col[1] for col in columns]  # col[1] is column name

                # Get row count
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                result[f"{table_name}_count"] = count

            return result
        except Exception as e:
            print(f"Error getting database structure: {e}")
            import traceback
            traceback.print_exc()
            return {"error": str(e)}
        finally:
            self._release_connection(conn)

    def generate_story_for_words(self, words, theme="adventure", characters="", scenario="", model_preference="auto"):
        """Generate a story using either Phi, Gemini, or LM Studio model to teach vocabulary words

        Args:
            words: List of words to include in the story
            theme: Theme of the story
            characters: Characters to include in the story
            scenario: Scenario for the story
            model_preference: Which AI model to use (auto, phi, gemini, lmstudio)

        Returns:
            str: Generated story with vocabulary words highlighted and explained
        """
        if not words:
            return "Please provide at least one word to include in the story."

        # Get definitions for the words
        word_definitions = []
        for word in words:
            word_data = self.get_word(word)
            if word_data:
                word_definitions.append(f"{word}: {word_data['definition']}")

        # Create the prompt for the AI model
        prompt = f"""Create an engaging story that incorporates the following vocabulary words: {', '.join(words)}.

Theme: {theme}
"""

        if characters:
            prompt += f"Characters: {characters}\n"

        if scenario:
            prompt += f"Scenario: {scenario}\n"

        prompt += f"""
For each vocabulary word:
1. Use it naturally in the story
2. Bold the word when first used
3. After the story, provide a brief explanation of how each word was used

Here are the definitions of the words:
{chr(10).join(word_definitions)}

Make the story creative, engaging, and educational. The story should be 300-500 words.
"""

        # Determine which model to use
        if model_preference == "auto":
            # Try Gemini first, then Phi, then LM Studio
            story = self._generate_with_gemini(prompt, words, theme)
            if not story:
                story = self._generate_with_phi(prompt, words, theme)
            if not story:
                story = self._generate_with_lmstudio(prompt, words, theme)
        elif model_preference == "gemini":
            story = self._generate_with_gemini(prompt, words, theme)
        elif model_preference == "phi":
            story = self._generate_with_phi(prompt, words, theme)
        elif model_preference == "lmstudio":
            story = self._generate_with_lmstudio(prompt, words, theme)
        else:
            # Default to auto if invalid preference
            story = self._generate_with_gemini(prompt, words, theme)
            if not story:
                story = self._generate_with_phi(prompt, words, theme)
            if not story:
                story = self._generate_with_lmstudio(prompt, words, theme)

        # If all models fail, return an error message
        if not story:
            return "Sorry, I couldn't generate a story at this time. Please try again later."

        # Format the story with HTML for better display
        story = self._format_story_html(story, words)

        return story

    def _generate_with_gemini(self, prompt, words, theme):
        """Generate a story using the Gemini API

        Args:
            prompt: The prompt for the story
            words: List of words to include
            theme: Theme of the story

        Returns:
            str: Generated story or None if failed
        """
        try:
            import requests
            import json

            # Prepare the request
            url = "http://localhost:8001/generate_story"
            payload = {
                "words": words,
                "theme": theme,
                "prompt": prompt,
                "max_new_tokens": 1024,
                "temperature": 0.8
            }

            # Make the request
            response = requests.post(url, json=payload, timeout=60)

            if response.status_code == 200:
                result = response.json()
                return result.get("story", "")
            else:
                print(f"Gemini API error: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            print(f"Error generating story with Gemini: {e}")
            return None

    def _generate_with_phi(self, prompt, words, theme):
        """Generate a story using the Phi-3 API

        Args:
            prompt: The prompt for the story
            words: List of words to include
            theme: Theme of the story

        Returns:
            str: Generated story or None if failed
        """
        try:
            import requests
            import json

            # Prepare the request
            url = "http://localhost:8000/generate_story"
            payload = {
                "words": words,
                "theme": theme,
                "characters": "",
                "scenario": "",
                "max_new_tokens": 1024,
                "temperature": 0.8
            }

            # Make the request
            response = requests.post(url, json=payload, timeout=60)

            if response.status_code == 200:
                result = response.json()
                return result.get("story", "")
            else:
                print(f"Phi API error: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            print(f"Error generating story with Phi: {e}")
            return None

    def _generate_with_lmstudio(self, prompt, words, theme):
        """Generate a story using the LM Studio API

        Args:
            prompt: The prompt for the story
            words: List of words to include
            theme: Theme of the story

        Returns:
            str: Generated story or None if failed
        """
        try:
            import requests
            import json

            # Prepare the request
            url = "http://localhost:8002/generate_story"
            payload = {
                "prompt": prompt,
                "max_new_tokens": 1024,
                "temperature": 0.8,
                "top_p": 0.95,
                "top_k": 40
            }

            # Make the request
            response = requests.post(url, json=payload, timeout=60)

            if response.status_code == 200:
                result = response.json()
                return result.get("response", "")
            else:
                print(f"LM Studio API error: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            print(f"Error generating story with LM Studio: {e}")
            return None

    def _format_story_html(self, story, words):
        """Format the story with HTML for better display

        Args:
            story: The generated story
            words: List of words to highlight

        Returns:
            str: Formatted story with HTML
        """
        # Replace bold markers with HTML
        story = story.replace("**", "<strong>", 1)
        for word in words:
            # Make sure we only bold the first occurrence of each word
            # Use case-insensitive replacement
            import re
            pattern = re.compile(re.escape(word), re.IGNORECASE)
            story = pattern.sub(f"<strong>{word}</strong>", story, count=1)

        # Replace newlines with HTML breaks
        story = story.replace("\n", "<br>")

        # Add a horizontal rule before the explanations section
        if "Explanations:" in story:
            story = story.replace("Explanations:", "<hr><h4>Explanations:</h4>")
        elif "Explanation:" in story:
            story = story.replace("Explanation:", "<hr><h4>Explanations:</h4>")
        elif "Word Explanations:" in story:
            story = story.replace("Word Explanations:", "<hr><h4>Explanations:</h4>")

        return story

    def get_or_create_default_library(self, user_id=None):
        """Get the default 'Mongoose' library or create it if it doesn't exist

        Args:
            user_id: ID of the user, or None for the default user

        Returns:
            int: ID of the default library
        """
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            # Check if Mongoose library exists for this user
            if user_id is not None:
                cursor.execute('''
                    SELECT id FROM libraries WHERE name = 'Mongoose' AND user_id = ?
                ''', (user_id,))
            else:
                cursor.execute('''
                    SELECT id FROM libraries WHERE name = 'Mongoose' AND user_id IS NULL
                ''')

            result = cursor.fetchone()

            if result:
                # Library exists, return its ID
                return result[0]
            else:
                # Create the library
                description = "Default library for ongoing vocabulary learning"
                return self.create_library("Mongoose", description, user_id)
        except Exception as e:
            print(f"Error getting/creating default library: {e}")
            return None
        finally:
            self._release_connection(conn)

    def set_current_library(self, library_id, user_id=None):
        """Set the current library for a user

        Args:
            library_id: ID of the library to set as current
            user_id: ID of the user, or None for global setting

        Returns:
            bool: True if successful, False otherwise
        """
        if not library_id:
            return False

        conn = self._get_connection()
        try:
            cursor = conn.cursor()

            # Check if library exists
            cursor.execute('SELECT id FROM libraries WHERE id = ?', (library_id,))
            if not cursor.fetchone():
                print(f"Library ID {library_id} not found")
                return False

            # Ensure app_settings table exists
            self.ensure_library_tables_exist()

            # Check if user_id column exists
            cursor.execute("PRAGMA table_info(app_settings)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]
            has_user_id = 'user_id' in column_names

            if user_id is not None and has_user_id:
                # Check if user has a settings row
                cursor.execute('SELECT id FROM app_settings WHERE user_id = ?', (user_id,))
                if cursor.fetchone():
                    # Update existing row
                    cursor.execute(
                        'UPDATE app_settings SET current_library = ? WHERE user_id = ?',
                        (library_id, user_id)
                    )
                else:
                    # Insert new row
                    cursor.execute(
                        'INSERT INTO app_settings (current_library, user_id) VALUES (?, ?)',
                        (library_id, user_id)
                    )
            else:
                # Update global setting
                cursor.execute(
                    'UPDATE app_settings SET current_library = ? WHERE id = 1',
                    (library_id,)
                )

            conn.commit()
            return True
        except Exception as e:
            print(f"Error setting current library: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            self._release_connection(conn)

    def get_current_library(self, user_id=None):
        """Get the current library for a user

        Args:
            user_id: ID of the user, or None for global setting

        Returns:
            int: ID of the current library, or None if not set
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()

            # Check if app_settings table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='app_settings'")
            if not cursor.fetchone():
                # Create the table
                self.ensure_library_tables_exist()
                return None

            # Check if user_id column exists
            cursor.execute("PRAGMA table_info(app_settings)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]
            has_user_id = 'user_id' in column_names

            if user_id is not None and has_user_id:
                # Get current library for this user
                cursor.execute('''
                    SELECT current_library FROM app_settings WHERE user_id = ?
                ''', (user_id,))
                result = cursor.fetchone()

                if result:
                    return result[0]
                else:
                    # Create a new settings row for this user
                    cursor.execute('''
                        INSERT INTO app_settings (user_id, current_library)
                        VALUES (?, NULL)
                    ''', (user_id,))
                    conn.commit()
                    return None
            else:
                # Get global current library
                cursor.execute('SELECT current_library FROM app_settings WHERE user_id IS NULL LIMIT 1')
                result = cursor.fetchone()

                if result:
                    return result[0]
                else:
                    # Create a default settings row
                    cursor.execute('INSERT INTO app_settings (id, user_id, current_library) VALUES (1, NULL, NULL)')
                    conn.commit()
                    return None
        finally:
            self._release_connection(conn)

    def get_current_library_info(self, user_id=None) -> Optional[Tuple]:
        """Get information about the current library for a user

        Args:
            user_id: ID of the user, or None for the default user

        Returns:
            tuple: (id, name, description, created_date) of current library, or None if not set
        """
        try:
            library_id = self.get_current_library(user_id)
            if not library_id:
                # No current library set, try to get or create default library
                library_id = self.get_or_create_default_library(user_id)
                if library_id:
                    # Set as current library
                    self.set_current_library(library_id, user_id)
                else:
                    return None

            conn = self._get_connection()
            try:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, name, description, created_date
                    FROM libraries
                    WHERE id = ?
                ''', (library_id,))
                return cursor.fetchone()
            finally:
                self._release_connection(conn)
        except Exception as e:
            print(f"Error getting current library info: {e}")
            return None

    def get_library_details(self, library_id: int) -> Optional[Tuple]:
        """Get details for a library

        Args:
            library_id: ID of the library

        Returns:
            tuple: (id, name, description, created_date) or None if not found
        """
        if not library_id:
            return None

        conn = self._get_connection()
        try:
            cursor = conn.cursor()

            # Get library details
            cursor.execute('''
                SELECT id, name, description, created_date
                FROM libraries
                WHERE id = ?
            ''', (library_id,))

            return cursor.fetchone()
        except Exception as e:
            print(f"Error getting library details: {e}")
            import traceback
            traceback.print_exc()
            return None
        finally:
            self._release_connection(conn)



    def ensure_library_tables_exist(self):
        """Ensure that all library-related tables exist in the database"""
        conn = self._get_connection()
        try:
            cursor = conn.cursor()

            # Create libraries table if it doesn't exist
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS libraries (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    user_id INTEGER,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(name, user_id)
                )
            ''')

            # Create library_words table if it doesn't exist
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS library_words (
                    id INTEGER PRIMARY KEY,
                    library_id INTEGER,
                    word_id INTEGER,
                    FOREIGN KEY (library_id) REFERENCES libraries (id) ON DELETE CASCADE,
                    FOREIGN KEY (word_id) REFERENCES words (id) ON DELETE CASCADE,
                    UNIQUE(library_id, word_id)
                )
            ''')

            # Create app_settings table if it doesn't exist
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS app_settings (
                    id INTEGER PRIMARY KEY,
                    user_id INTEGER UNIQUE,
                    current_library INTEGER,
                    FOREIGN KEY (current_library) REFERENCES libraries (id) ON DELETE SET NULL
                )
            ''')

            conn.commit()
        except Exception as e:
            print(f"Error creating library tables: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self._release_connection(conn)

    def migrate_database_for_user_libraries(self):
        """Migrate the database to support user-specific libraries"""
        conn = self._get_connection()
        try:
            cursor = conn.cursor()

            # Check if libraries table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='libraries'")
            if not cursor.fetchone():
                # Libraries table doesn't exist yet, no need to migrate
                return

            # Check if user_id column exists in libraries table
            cursor.execute("PRAGMA table_info(libraries)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]

            if 'user_id' not in column_names:
                print("Adding user_id column to libraries table...")
                try:
                    # Add user_id column to libraries table
                    cursor.execute("ALTER TABLE libraries ADD COLUMN user_id INTEGER")

                    # Update the unique constraint by recreating the table
                    cursor.execute("""
                        CREATE TABLE libraries_new (
                            id INTEGER PRIMARY KEY,
                            name TEXT,
                            description TEXT,
                            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            user_id INTEGER,
                            UNIQUE(name, user_id)
                        )
                    """)
                    cursor.execute("INSERT INTO libraries_new SELECT id, name, description, created_date, user_id FROM libraries")
                    cursor.execute("DROP TABLE libraries")
                    cursor.execute("ALTER TABLE libraries_new RENAME TO libraries")
                    print("Libraries table migrated successfully")
                except Exception as e:
                    print(f"Error migrating libraries table: {e}")
                    conn.rollback()
                    raise

            # Check if user_id column exists in learned_history table
            cursor.execute("PRAGMA table_info(learned_history)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]
            if 'user_id' not in column_names:
                print("Adding user_id column to learned_history table...")
                try:
                    # Add user_id column to learned_history table
                    cursor.execute("ALTER TABLE learned_history ADD COLUMN user_id INTEGER")
                    print("learned_history table migrated successfully")
                except Exception as e:
                    print(f"Error migrating learned_history table: {e}")
                    conn.rollback()
                    raise

            # Check if app_settings table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='app_settings'")
            if not cursor.fetchone():
                # app_settings table doesn't exist yet, create it with user_id
                cursor.execute("""
                    CREATE TABLE app_settings (
                        id INTEGER PRIMARY KEY,
                        user_id INTEGER UNIQUE,
                        current_library INTEGER,
                        FOREIGN KEY (current_library) REFERENCES libraries(id)
                    )
                """)
                print("Created app_settings table with user_id")
            else:
                # Check if user_id column exists in app_settings table
                cursor.execute("PRAGMA table_info(app_settings)")
                columns = cursor.fetchall()
                column_names = [column[1] for column in columns]

                if 'user_id' not in column_names:
                    print("Adding user_id column to app_settings table...")
                    try:
                        # Create new app_settings table with user_id
                        cursor.execute("""
                            CREATE TABLE app_settings_new (
                                id INTEGER PRIMARY KEY,
                                user_id INTEGER UNIQUE,
                                current_library INTEGER,
                                FOREIGN KEY (current_library) REFERENCES libraries(id)
                            )
                        """)

                        # Copy existing settings to the default user (NULL)
                        cursor.execute("INSERT INTO app_settings_new (id, current_library) SELECT id, current_library FROM app_settings")

                        # Drop old table and rename new one
                        cursor.execute("DROP TABLE app_settings")
                        cursor.execute("ALTER TABLE app_settings_new RENAME TO app_settings")
                        print("app_settings table migrated successfully")
                    except Exception as e:
                        print(f"Error migrating app_settings table: {e}")
                        conn.rollback()
                        raise

            conn.commit()
            print("Database migration completed successfully")
        except Exception as e:
            print(f"Error during database migration: {e}")
            import traceback
            traceback.print_exc()
            conn.rollback()
        finally:
            self._release_connection(conn)

    def create_library(self, user_id, name, description=""):
        """Create a new word library for a user

        Args:
            user_id: ID of the user
            name: Name of the library
            description: Optional description

        Returns:
            int: ID of the created library, or None if failed
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute(
                "INSERT INTO libraries (user_id, name, description) VALUES (?, ?, ?)",
                (user_id, name, description)
            )
            conn.commit()
            result = cursor.lastrowid
            return result
        except Exception as e:
            print(f"Error creating library: {e}")
            return None
        finally:
            self._release_connection(conn)

    def update_library(self, library_id, name, description=""):
        """Update a library's details

        Args:
            library_id: ID of the library
            name: New name for the library
            description: New description

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.conn.execute(
                "UPDATE libraries SET name = ?, description = ? WHERE id = ?",
                (name, description, library_id)
            )
            self.conn.commit()
            return True
        except Exception as e:
            print(f"Error updating library: {e}")
            return False

    def delete_library(self, library_id):
        """Delete a library and its word associations

        Args:
            library_id: ID of the library to delete

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Delete word associations first
            self.conn.execute("DELETE FROM library_words WHERE library_id = ?", (library_id,))
            # Delete the library
            self.conn.execute("DELETE FROM libraries WHERE id = ?", (library_id,))
            self.conn.commit()
            return True
        except Exception as e:
            print(f"Error deleting library: {e}")
            return False

    def get_library(self, library_id):
        """Get details of a specific library

        Args:
            library_id: ID of the library

        Returns:
            dict: Library details or None if not found
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute(
                "SELECT id, user_id, name, description, created_date FROM libraries WHERE id = ?",
                (library_id,)
            )
            row = cursor.fetchone()
            if row:
                return {
                    'id': row[0],
                    'user_id': row[1],
                    'name': row[2],
                    'description': row[3],
                    'created_date': row[4]
                }
            return None
        except Exception as e:
            print(f"Error getting library: {e}")
            return None
        finally:
            self._release_connection(conn)

    def get_libraries_for_user(self, user_id):
        """Get all libraries for a user

        Args:
            user_id: ID of the user

        Returns:
            list: List of library dictionaries
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute(
                "SELECT id, name, description, created_date FROM libraries WHERE user_id = ? ORDER BY name",
                (user_id,)
            )
            libraries = []
            for row in cursor.fetchall():
                libraries.append({
                    'id': row[0],
                    'name': row[1],
                    'description': row[2],
                    'created_date': row[3],
                    'word_count': self.get_library_word_count(row[0])
                })
            return libraries
        except Exception as e:
            print(f"Error getting libraries: {e}")
            return []
        finally:
            self._release_connection(conn)

    def get_library_word_count(self, library_id):
        """Get the number of words in a library

        Args:
            library_id: ID of the library

        Returns:
            int: Number of words in the library
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute(
                "SELECT COUNT(*) FROM library_words WHERE library_id = ?",
                (library_id,)
            )
            result = cursor.fetchone()[0]
            return result
        except Exception as e:
            print(f"Error getting library word count: {e}")
            return 0
        finally:
            self._release_connection(conn)

    def add_word_to_library(self, library_id, word_id):
        """Add a word to a library

        Args:
            library_id: ID of the library
            word_id: ID of the word

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute(
                "INSERT OR IGNORE INTO library_words (library_id, word_id) VALUES (?, ?)",
                (library_id, word_id)
            )
            conn.commit()
            # Return True if a row was inserted, False if the word was already in the library
            result = cursor.rowcount > 0
            return result
        except Exception as e:
            print(f"Error adding word to library: {e}")
            return False
        finally:
            self._release_connection(conn)

    def remove_word_from_library_by_id(self, library_id, word_id):
        """Remove a word from a library using word ID

        Args:
            library_id: ID of the library
            word_id: ID of the word

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute(
                "DELETE FROM library_words WHERE library_id = ? AND word_id = ?",
                (library_id, word_id)
            )
            conn.commit()
            return True
        except Exception as e:
            print(f"Error removing word from library: {e}")
            return False
        finally:
            self._release_connection(conn)

    def get_words_in_library(self, library_id):
        """Get all words in a library

        Args:
            library_id: ID of the library

        Returns:
            list: List of word dictionaries
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()

            # Check if example column exists
            cursor.execute("PRAGMA table_info(words)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]
            has_example = 'example' in column_names

            if has_example:
                cursor.execute(
                    """
                    SELECT w.id, w.word, w.definition, w.example
                    FROM words w
                    JOIN library_words lw ON w.id = lw.word_id
                    WHERE lw.library_id = ?
                    ORDER BY w.word
                    """,
                    (library_id,)
                )
                words = []
                for row in cursor.fetchall():
                    words.append({
                        'id': row[0],
                        'word': row[1],
                        'definition': row[2],
                        'example': row[3] or ""
                    })
            else:
                cursor.execute(
                    """
                    SELECT w.id, w.word, w.definition, w.source
                    FROM words w
                    JOIN library_words lw ON w.id = lw.word_id
                    WHERE lw.library_id = ?
                    ORDER BY w.word
                    """,
                    (library_id,)
                )
                words = []
                for row in cursor.fetchall():
                    words.append({
                        'id': row[0],
                        'word': row[1],
                        'definition': row[2],
                        'example': ""  # Default empty example
                    })
            return words
        except Exception as e:
            print(f"Error getting words in library: {e}")
            return []
        finally:
            self._release_connection(conn)

    def get_learned_words_from_library(self, user_id, library_id):
        """Get words from a library that the user has learned

        Args:
            user_id: ID of the user
            library_id: ID of the library

        Returns:
            list: List of word dictionaries
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute(
                """
                SELECT w.id, w.word, w.definition, w.example
                FROM words w
                JOIN library_words lw ON w.id = lw.word_id
                JOIN user_progress up ON w.id = up.word_id
                WHERE lw.library_id = ? AND up.user_id = ? AND up.status = 'learned'
                ORDER BY w.word
                """,
                (library_id, user_id)
            )
            words = []
            for row in cursor.fetchall():
                words.append({
                    'id': row[0],
                    'word': row[1],
                    'definition': row[2],
                    'example': row[3]
                })
            return words
        except Exception as e:
            print(f"Error getting learned words from library: {e}")
            return []
        finally:
            self._release_connection(conn)

    def get_unlearned_words_from_library(self, user_id, library_id):
        """Get words from a library that the user has NOT learned

        Args:
            user_id: ID of the user
            library_id: ID of the library

        Returns:
            list: List of word dictionaries
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()

            # Check if example column exists
            cursor.execute("PRAGMA table_info(words)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]
            has_example = 'example' in column_names

            if has_example:
                cursor.execute(
                    """
                    SELECT w.id, w.word, w.definition, w.example
                    FROM words w
                    JOIN library_words lw ON w.id = lw.word_id
                    LEFT JOIN learned_history lh ON w.id = lh.word_id AND lh.user_id = ?
                    WHERE lw.library_id = ? AND lh.id IS NULL
                    ORDER BY w.word
                    """,
                    (user_id, library_id)
                )
                words = []
                for row in cursor.fetchall():
                    words.append({
                        'id': row[0],
                        'word': row[1],
                        'definition': row[2],
                        'example': row[3] or ""
                    })
            else:
                cursor.execute(
                    """
                    SELECT w.id, w.word, w.definition, w.source
                    FROM words w
                    JOIN library_words lw ON w.id = lw.word_id
                    LEFT JOIN learned_history lh ON w.id = lh.word_id AND lh.user_id = ?
                    WHERE lw.library_id = ? AND lh.id IS NULL
                    ORDER BY w.word
                    """,
                    (user_id, library_id)
                )
                words = []
                for row in cursor.fetchall():
                    words.append({
                        'id': row[0],
                        'word': row[1],
                        'definition': row[2],
                        'example': ""  # Default empty example
                    })
            return words
        except Exception as e:
            print(f"Error getting unlearned words from library: {e}")
            return []
        finally:
            self._release_connection(conn)

    def is_word_learned(self, user_id, word):
        """Check if a word is learned by a user

        Args:
            user_id: ID of the user
            word: Word to check

        Returns:
            bool: True if the word is learned, False otherwise
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute(
                """
                SELECT up.status
                FROM user_progress up
                JOIN words w ON up.word_id = w.id
                WHERE up.user_id = ? AND w.word = ? AND up.status = 'learned'
                """,
                (user_id, word)
            )
            result = cursor.fetchone() is not None
            return result
        except Exception as e:
            print(f"Error checking if word is learned: {e}")
            return False
        finally:
            self._release_connection(conn)

    def start_learning_session(self, user_id, session_id):
        """Start a learning session for a user"""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute(
                """
                INSERT OR REPLACE INTO learning_sessions
                (id, user_id, start_time, status)
                VALUES (?, ?, datetime('now'), 'active')
                """,
                (session_id, user_id)
            )
            conn.commit()
            return True
        except Exception as e:
            print(f"Error starting learning session: {e}")
            return False
        finally:
            self._release_connection(conn)

    def end_learning_session(self, session_id, words_reviewed, words_learned):
        """End a learning session"""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute(
                """
                UPDATE learning_sessions
                SET end_time = datetime('now'),
                    status = 'completed',
                    words_reviewed = ?,
                    words_learned = ?
                WHERE id = ?
                """,
                (words_reviewed, words_learned, session_id)
            )
            conn.commit()
            return True
        except Exception as e:
            print(f"Error ending learning session: {e}")
            return False
        finally:
            self._release_connection(conn)

    def record_session_word(self, session_id, word_id, learned=0):
        """Record a word in a learning session"""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute(
                """
                INSERT OR REPLACE INTO session_words
                (session_id, word_id, learned, timestamp)
                VALUES (?, ?, ?, datetime('now'))
                """,
                (session_id, word_id, learned)
            )
            conn.commit()
            return True
        except Exception as e:
            print(f"Error recording session word: {e}")
            return False
        finally:
            self._release_connection(conn)

class LearningSession:
    """
    DEPRECATED: Class to manage a vocabulary learning session
    This class is kept for backward compatibility but is no longer used in the application.
    """

    def __init__(self, search_engine, word_count: int = 10):
        self.search_engine = search_engine
        self.word_count = word_count
        self.current_words = []
        self.current_index = 0
        self.start_time = None
        self.end_time = None

    def start_session(self):
        """Start a new learning session"""
        self.current_words = self.search_engine.get_learning_words(self.word_count)
        self.current_index = 0
        self.start_time = datetime.now()
        return len(self.current_words) > 0

    def get_current_word(self):
        """Get the current word in the session"""
        if 0 <= self.current_index < len(self.current_words):
            return self.current_words[self.current_index]
        return None

    def next_word(self):
        """Move to the next word in the session"""
        if self.current_index < len(self.current_words) - 1:
            self.current_index += 1
            return True
        return False

    def prev_word(self):
        """Move to the previous word in the session"""
        if self.current_index > 0:
            self.current_index -= 1
            return True
        return False

    def mark_current_as_learned(self):
        """Mark the current word as learned"""
        if 0 <= self.current_index < len(self.current_words):
            word = self.current_words[self.current_index][0]
            return self.search_engine.mark_word_as_learned(word)
        return False

    def mark_current_as_unlearned(self):
        """Mark the current word as unlearned"""
        if 0 <= self.current_index < len(self.current_words):
            word = self.current_words[self.current_index][0]
            return self.search_engine.mark_word_as_unlearned(word)
        return False

    def end_session(self):
        """End the current learning session"""
        self.end_time = datetime.now()
        duration = self.end_time - self.start_time
        return {
            'words_reviewed': self.current_index + 1,
            'total_words': len(self.current_words),
            'duration_seconds': duration.total_seconds()
        }

class DBContextManager:
    def __init__(self, search_engine):
        self.search_engine = search_engine
        self.conn = None
        self.cursor = None

    def __enter__(self):
        self.conn = self.search_engine._get_connection()
        self.cursor = self.conn.cursor()
        return self.cursor

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit the context manager and handle transaction"""
        try:
            if exc_type is None:
                self.conn.commit()
            else:
                self.conn.rollback()
                # Log the exception details if needed
                if exc_val:
                    print(f"Transaction rolled back due to: {exc_val}")
        finally:
            self.search_engine._release_connection(self.conn)
