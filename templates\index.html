{% extends "base.html" %}

{% block title %}GRE Vocabulary Learning Tool{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section">
    <div class="hero-content">
        <h1 class="hero-title">GRE Vocabulary Learning Tool</h1>
        <p class="hero-subtitle">Expand your vocabulary and prepare for the GRE with our interactive learning tools.</p>
        
        {% if current_library %}
        <div class="current-library-badge">
            <i class="bi bi-collection"></i> Current Library: {{ current_library[1] }}
        </div>
        {% endif %}
        
        <div class="action-buttons">
            <a href="{{ url_for('search') }}" class="btn action-button search-button">
                <i class="bi bi-search"></i> Search Words
            </a>
            
            {% if current_user.is_authenticated %}
            <!-- Remove the Start Learning button 
            <a href="{{ url_for('learning_session') }}" class="btn action-button learn-button">
                <i class="bi bi-mortarboard"></i> Start Learning
            </a>
            -->
            {% else %}
            <a href="{{ url_for('auth.login') }}" class="btn action-button learn-button">
                <i class="bi bi-lock"></i> Login to Start Learning
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- Quick Search Form -->
<div class="row mt-4">
    <div class="col-md-6 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Quick Search</h5>
            </div>
            <div class="card-body">
                <form method="get" action="{{ url_for('search') }}" class="d-flex">
                    <input type="text" name="word" class="form-control" placeholder="Enter a word to search" required>
                    <button type="submit" class="btn btn-primary ms-2">
                        <i class="bi bi-search"></i> Search
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Dashboard Section -->
<div class="container mt-5">
    <div class="row">
        <!-- Statistics Cards -->
        <div class="col-md-4 mb-4">
            <div class="card dashboard-card">
                <div class="card-body text-center">
                    <h5 class="card-title">Total Words</h5>
                    <p class="card-text display-4">{{ total_words }}</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-4">
            <div class="card dashboard-card">
                <div class="card-body text-center">
                    <h5 class="card-title">Words Learned</h5>
                    <p class="card-text display-4">{{ learned_words }}</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4 mb-4">
            <div class="card dashboard-card">
                <div class="card-body text-center">
                    <h5 class="card-title">Words Remaining</h5>
                    <p class="card-text display-4">{{ remaining_words }}</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Features Section -->
    <div class="row mt-4">
        <div class="col-12">
            <h2 class="section-title">Features</h2>
        </div>
        
        <div class="col-md-4 mb-4">
            <div class="card feature-card">
                <div class="card-body">
                    <h5 class="card-title"><i class="bi bi-search"></i> Search & Explore</h5>
                    <p class="card-text">Look up words, definitions, and examples to expand your vocabulary.</p>
                    <a href="{{ url_for('search') }}" class="btn btn-outline-primary">Search Words</a>
                </div>
            </div>
        </div>
        
        <!-- Remove the Learning Sessions card -->
        <!--
        <div class="col-md-4 mb-4">
            <div class="card feature-card">
                <div class="card-body">
                    <h5 class="card-title"><i class="bi bi-mortarboard"></i> Learning Sessions</h5>
                    <p class="card-text">Practice with interactive flashcards and track your progress.</p>
                    {% if current_user.is_authenticated %}
                    <a href="{{ url_for('learning_session') }}" class="btn btn-outline-primary">Start Learning</a>
                    {% else %}
                    <a href="{{ url_for('auth.login') }}" class="btn btn-outline-primary">Login to Access</a>
                    {% endif %}
                </div>
            </div>
        </div>
        -->
        
        <div class="col-md-4 mb-4">
            <div class="card feature-card">
                <div class="card-body">
                    <h5 class="card-title"><i class="bi bi-book"></i> AI Story Generator</h5>
                    <p class="card-text">Create custom stories with vocabulary words to enhance learning.</p>
                    {% if current_user.is_authenticated %}
                    <a href="{{ url_for('story_generator') }}" class="btn btn-outline-primary">Generate Stories</a>
                    {% else %}
                    <a href="{{ url_for('auth.login') }}" class="btn btn-outline-primary">Login to Access</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recommended Words Section -->
    <div class="row mt-4">
        <div class="col-12">
            <h2 class="section-title">Recommended Words</h2>
        </div>
        
        {% for word in recommended_words %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card word-card">
                <div class="card-header">
                    <h5 class="mb-0">{{ word[0] }}</h5>
                </div>
                <div class="card-body">
                    <p class="card-text">{{ word[1] }}</p>
                    {% if word[2] %}
                    <p class="card-text"><small class="text-muted">Source: {{ word[2] }}</small></p>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('search', word=word[0]) }}" class="btn btn-sm btn-outline-primary">View Details</a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <!-- Call to Action -->
    {% if not current_user.is_authenticated %}
    <div class="row mt-5">
        <div class="col-12">
            <div class="card cta-card">
                <div class="card-body text-center">
                    <h3>Ready to enhance your GRE vocabulary?</h3>
                    <p>Create an account to track your progress and access all features.</p>
                    <div class="mt-3">
                        <a href="{{ url_for('auth.register') }}" class="btn btn-primary me-2">Register Now</a>
                        <a href="{{ url_for('auth.login') }}" class="btn btn-outline-primary">Login</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Ensure equal height for stat cards
        function equalizeStatCardHeight() {
            const statCards = document.querySelectorAll('.stat-card');
            let maxHeight = 0;
            
            // Reset heights
            statCards.forEach(card => {
                card.style.height = 'auto';
            });
            
            // Find max height
            statCards.forEach(card => {
                const height = card.offsetHeight;
                if (height > maxHeight) {
                    maxHeight = height;
                }
            });
            
            // Set equal heights
            if (window.innerWidth >= 768) {
                statCards.forEach(card => {
                    card.style.height = maxHeight + 'px';
                });
            }
        }
        
        // Run on load and resize
        equalizeStatCardHeight();
        window.addEventListener('resize', equalizeStatCardHeight);
        
        // Add animation to stat numbers
        const statNumbers = document.querySelectorAll('.stat-number');
        statNumbers.forEach(number => {
            const finalValue = parseInt(number.textContent);
            let startValue = 0;
            const duration = 1500;
            const increment = finalValue / (duration / 16); // 60fps
            
            function updateNumber() {
                startValue += increment;
                if (startValue < finalValue) {
                    number.textContent = Math.floor(startValue);
                    requestAnimationFrame(updateNumber);
                } else {
                    number.textContent = finalValue;
                }
            }
            
            // Start animation when element is in viewport
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        updateNumber();
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.1 });
            
            observer.observe(number);
        });
    });
</script>
{% endblock %}
