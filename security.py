"""
Enhanced security utilities for the GRE Vocabulary App
"""
import os
import secrets
import hashlib
import hmac
import time
from datetime import datetime, timedelta
from functools import wraps
from flask import request, session, g, flash, redirect, url_for, current_app
from models import User, db

# Optional imports for 2FA functionality
try:
    import pyotp
    import qrcode
    import io
    import base64
    TOTP_AVAILABLE = True
except ImportError:
    TOTP_AVAILABLE = False

class SecurityManager:
    """Centralized security management"""

    def __init__(self, app=None):
        self.app = app
        if app:
            self.init_app(app)

    def init_app(self, app):
        """Initialize security manager with Flask app"""
        app.config.setdefault('SECURITY_PASSWORD_SALT', secrets.token_urlsafe(32))
        app.config.setdefault('SECURITY_CSRF_TIME_LIMIT', 3600)
        app.config.setdefault('SECURITY_LOGIN_ATTEMPTS_LIMIT', 5)
        app.config.setdefault('SECURITY_LOGIN_LOCKOUT_DURATION', 30)  # minutes

    @staticmethod
    def generate_csrf_token():
        """Generate CSRF token"""
        if '_csrf_token' not in session:
            session['_csrf_token'] = secrets.token_urlsafe(32)
        return session['_csrf_token']

    @staticmethod
    def validate_csrf_token(token):
        """Validate CSRF token"""
        return token and token == session.get('_csrf_token')

    @staticmethod
    def hash_password(password, salt=None):
        """Hash password with salt"""
        if salt is None:
            salt = secrets.token_hex(16)

        # Use PBKDF2 with SHA-256
        key = hashlib.pbkdf2_hmac('sha256',
                                  password.encode('utf-8'),
                                  salt.encode('utf-8'),
                                  100000)  # 100,000 iterations
        return f"{salt}${key.hex()}"

    @staticmethod
    def verify_password(password, hashed):
        """Verify password against hash"""
        try:
            salt, key = hashed.split('$')
            return SecurityManager.hash_password(password, salt) == hashed
        except ValueError:
            return False

    @staticmethod
    def generate_secure_token(length=32):
        """Generate cryptographically secure token"""
        return secrets.token_urlsafe(length)

    @staticmethod
    def rate_limit_check(identifier, limit=5, window=300):
        """Check rate limiting for an identifier"""
        # This is a simple in-memory rate limiter
        # In production, use Redis or database
        current_time = time.time()
        key = f"rate_limit_{identifier}"

        if not hasattr(g, 'rate_limits'):
            g.rate_limits = {}

        if key not in g.rate_limits:
            g.rate_limits[key] = []

        # Clean old entries
        g.rate_limits[key] = [t for t in g.rate_limits[key]
                              if current_time - t < window]

        # Check limit
        if len(g.rate_limits[key]) >= limit:
            return False

        # Add current attempt
        g.rate_limits[key].append(current_time)
        return True

class TwoFactorAuth:
    """Two-Factor Authentication utilities"""

    @staticmethod
    def generate_qr_code(user, app_name="GRE Vocabulary App"):
        """Generate QR code for TOTP setup"""
        if not TOTP_AVAILABLE:
            return None

        totp_uri = user.get_totp_uri(app_name)
        if not totp_uri:
            return None

        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(totp_uri)
        qr.make(fit=True)

        img = qr.make_image(fill_color="black", back_color="white")

        # Convert to base64 for display
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        buffer.seek(0)

        img_str = base64.b64encode(buffer.getvalue()).decode()
        return f"data:image/png;base64,{img_str}"

# Decorators for enhanced security
def csrf_required(f):
    """Decorator to require CSRF token for POST requests"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if request.method == "POST":
            token = request.form.get('csrf_token')
            if not SecurityManager.validate_csrf_token(token):
                flash('Security token expired. Please try again.', 'danger')
                return redirect(request.url)
        return f(*args, **kwargs)
    return decorated_function

def rate_limited(limit=5, window=300):
    """Decorator for rate limiting"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            identifier = request.remote_addr
            if not SecurityManager.rate_limit_check(identifier, limit, window):
                flash('Too many requests. Please try again later.', 'warning')
                return redirect(url_for('index'))
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def admin_required(f):
    """Decorator to require admin privileges"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not hasattr(g, 'user') or not g.user or not g.user.is_admin():
            flash('Admin privileges required.', 'danger')
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    return decorated_function

def two_factor_required(f):
    """Decorator to require 2FA verification"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if (hasattr(g, 'user') and g.user and
            g.user.two_factor_enabled and
            not session.get('2fa_verified')):
            return redirect(url_for('auth.verify_2fa'))
        return f(*args, **kwargs)
    return decorated_function

def account_active_required(f):
    """Decorator to require active account"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if hasattr(g, 'user') and g.user:
            if not g.user.is_active:
                flash('Your account has been deactivated. Please contact support.', 'danger')
                return redirect(url_for('auth.logout'))
            if g.user.is_account_locked():
                flash('Your account is temporarily locked due to failed login attempts.', 'warning')
                return redirect(url_for('auth.logout'))
        return f(*args, **kwargs)
    return decorated_function

# Security utilities
def sanitize_filename(filename):
    """Sanitize uploaded filename"""
    import re
    # Remove path components
    filename = os.path.basename(filename)
    # Remove non-alphanumeric characters except dots and hyphens
    filename = re.sub(r'[^a-zA-Z0-9.-]', '_', filename)
    # Limit length
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        filename = name[:255-len(ext)] + ext
    return filename

def validate_file_type(filename, allowed_extensions):
    """Validate file type by extension"""
    return ('.' in filename and
            filename.rsplit('.', 1)[1].lower() in allowed_extensions)

def get_client_ip():
    """Get client IP address considering proxies"""
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.remote_addr

# Initialize security manager
security_manager = SecurityManager()
