{% extends 'base.html' %}

{% block title %}{{ library.name }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('libraries') }}">Libraries</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ library.name }}</li>
                </ol>
            </nav>
            <h1>{{ library.name }}</h1>
            {% if library.description %}
                <p class="lead">{{ library.description }}</p>
            {% endif %}
        </div>
        <div class="btn-group">
            <a href="{{ url_for('edit_library', library_id=library.id) }}" class="btn btn-outline-primary">
                <i class="bi bi-pencil"></i> Edit
            </a>
            <a href="{{ url_for('learning_session') }}?library_id={{ library.id }}" class="btn btn-success">
                <i class="bi bi-play-circle"></i> Learn
            </a>
            <a href="{{ url_for('story_generator') }}?library_id={{ library.id }}" class="btn btn-primary">
                <i class="bi bi-book"></i> Generate Story
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <h5 class="card-title">Total Words</h5>
                    <p class="display-4">{{ words|length }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <h5 class="card-title">Learned</h5>
                    {% set stats = get_library_stats(library.id) %}
                    <p class="display-4 text-success">{{ stats.learned }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <h5 class="card-title">Remaining</h5>
                    <p class="display-4 text-primary">{{ stats.remaining }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <h5 class="card-title">Progress</h5>
                    <div class="progress mt-3" style="height: 25px;">
                        <div class="progress-bar bg-success" role="progressbar"
                             style="width: {{ stats.percent }}%;"
                             aria-valuenow="{{ stats.percent }}" aria-valuemin="0" aria-valuemax="100">
                            {{ stats.percent }}%
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Words in Library</h4>
                <div class="btn-group">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addWordsModal">
                        <i class="bi bi-plus-circle"></i> Add Words
                    </button>
                    <button type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                        <span class="visually-hidden">Toggle Dropdown</span>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#addWordsModal">
                            <i class="bi bi-plus-circle"></i> Add Words Manually
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('add_to_library', library_id=library.id) }}">
                            <i class="bi bi-file-earmark-arrow-up"></i> Upload CSV File
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('import_all_to_library', library_id=library.id) }}">
                            <i class="bi bi-cloud-download"></i> Import All Words
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('download_csv_sample') }}">
                            <i class="bi bi-download"></i> Download CSV Sample
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="card-body">
            {% if words %}
                <div class="mb-3">
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" class="form-control" id="wordFilter" placeholder="Filter words..." onkeyup="filterWords()">
                        <button class="btn btn-outline-secondary" type="button" onclick="clearFilter()">Clear</button>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover" id="wordsTable">
                        <thead>
                            <tr>
                                <th>Word</th>
                                <th>Definition</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for word in words %}
                                <tr>
                                    <td>
                                        <a href="{{ url_for('search', word=word['word']) }}">
                                            {{ word['word'] }}
                                        </a>
                                    </td>
                                    <td>{{ word['definition']|truncate(100) }}</td>
                                    <td>
                                        {% if is_word_learned(word['word']) %}
                                            <span class="badge bg-success">Learned</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Not Learned</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('search', word=word['word']) }}" class="btn btn-outline-primary">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <form method="post" action="{{ url_for('remove_word_from_library', library_id=library['id'], word=word['word']) }}"
                                                  onsubmit="return confirm('Remove {{ word['word'] }} from this library?');">
                                                <button type="submit" class="btn btn-outline-danger">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> This library doesn't have any words yet.
                    Click "Add Words" to start building your vocabulary collection.
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Add Words Modal -->
<div class="modal fade" id="addWordsModal" tabindex="-1" aria-labelledby="addWordsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addWordsModalLabel">Add Words to Library</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="/library/{{ library.id }}/add-multiple-words">
                    <div class="mb-3">
                        <label for="words" class="form-label">Enter Words</label>
                        <textarea class="form-control" id="words" name="words" rows="5" placeholder="Enter one word per line"></textarea>
                        <div class="form-text">
                            Enter one word per line. Words that don't exist in the database will be added automatically.
                        </div>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> Add Words
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    function filterWords() {
        const filter = document.getElementById('wordFilter').value.toLowerCase();
        const table = document.getElementById('wordsTable');
        const rows = table.getElementsByTagName('tr');

        for (let i = 1; i < rows.length; i++) {
            const wordCell = rows[i].getElementsByTagName('td')[0];
            const definitionCell = rows[i].getElementsByTagName('td')[1];

            if (wordCell) {
                const wordText = wordCell.textContent || wordCell.innerText;
                const definitionText = definitionCell.textContent || definitionCell.innerText;

                if (wordText.toLowerCase().indexOf(filter) > -1 ||
                    definitionText.toLowerCase().indexOf(filter) > -1) {
                    rows[i].style.display = '';
                } else {
                    rows[i].style.display = 'none';
                }
            }
        }
    }

    function clearFilter() {
        document.getElementById('wordFilter').value = '';
        filterWords();
    }
</script>
{% endblock %}


