
{% extends "base.html" %}

{% block title %}Edit Library - {{ library_name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h2 class="mb-0 fs-4">Edit Library</h2>
                    <span class="badge bg-light text-dark">ID: {{ library_id }}</span>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('edit_library', library_id=library_id) }}">
                        <div class="mb-4">
                            <label for="name" class="form-label fw-bold">Library Name</label>
                            <input type="text" class="form-control form-control-lg" id="name" name="name" 
                                   value="{{ library_name }}" required autofocus
                                   placeholder="Enter library name">
                            <div class="form-text">Choose a descriptive name for your library</div>
                        </div>
                        <div class="mb-4">
                            <label for="description" class="form-label fw-bold">Description</label>
                            <textarea class="form-control" id="description" name="description" 
                                      rows="4" placeholder="Enter library description">{{ library_description }}</textarea>
                            <div class="form-text">Describe the purpose or content of this library</div>
                        </div>
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a href="{{ url_for('library_detail', library_id=library_id) }}" 
                               class="btn btn-outline-secondary btn-lg px-4">
                                <i class="bi bi-x-circle me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg px-4">
                                <i class="bi bi-check-circle me-2"></i>Save Changes
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer bg-light">
                    <div class="small text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        Editing this library will not affect the words it contains
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

