// Main JavaScript file for GRE Vocabulary Learner

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Audio playback for pronunciation - support both data-audio and data-word
    const audioButtons = document.querySelectorAll('.play-audio, .play-pronunciation');
    if (audioButtons) {
        audioButtons.forEach(button => {
            button.addEventListener('click', function() {
                const audioUrl = this.getAttribute('data-audio');
                const word = this.getAttribute('data-word');
                
                if (audioUrl) {
                    const audio = new Audio(audioUrl);
                    audio.play();
                } else if (word) {
                    // Call the API endpoint to play pronunciation
                    fetch(`/play-pronunciation/${word}`)
                        .then(response => response.json())
                        .then(data => {
                            if (!data.success) {
                                // Fallback to dictionary API if local pronunciation fails
                                fetch(`https://api.dictionaryapi.dev/api/v2/entries/en/${word}`)
                                    .then(response => response.json())
                                    .then(data => {
                                        if (Array.isArray(data) && data.length > 0) {
                                            const phonetics = data[0].phonetics || [];
                                            const audioFile = phonetics.find(p => p.audio)?.audio;
                                            if (audioFile) {
                                                const audio = new Audio(audioFile);
                                                audio.play();
                                            }
                                        }
                                    })
                                    .catch(error => console.error('Error fetching pronunciation:', error));
                            }
                        })
                        .catch(error => console.error('Error playing pronunciation:', error));
                }
            });
        });
    }
    
    // Add progress tracking for learning sessions
    const progressBar = document.querySelector('.session-progress');
    if (progressBar) {
        const total = parseInt(progressBar.getAttribute('data-total') || '0');
        const current = parseInt(progressBar.getAttribute('data-current') || '0');
        
        if (total > 0) {
            const percentage = Math.round((current / total) * 100);
            progressBar.style.width = `${percentage}%`;
            progressBar.setAttribute('aria-valuenow', percentage);
        }
    }
    
    // Auto-dismiss alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    if (alerts) {
        alerts.forEach(alert => {
            setTimeout(() => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000);
        });
    }
    
    // Word list filter functionality
    const wordFilter = document.getElementById('wordFilter');
    if (wordFilter) {
        wordFilter.addEventListener('input', function() {
            const filterValue = this.value.toLowerCase();
            const wordRows = document.querySelectorAll('tbody tr');
            
            wordRows.forEach(row => {
                const word = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                const definition = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
                
                if (word.includes(filterValue) || definition.includes(filterValue)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }
    
    // Mark all words checkbox functionality
    const markAllCheckbox = document.getElementById('markAllWords');
    if (markAllCheckbox) {
        markAllCheckbox.addEventListener('change', function() {
            const wordCheckboxes = document.querySelectorAll('input[name="word"]');
            wordCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }
    
    // Learning session keyboard shortcuts
    const learningSession = document.querySelector('.word-card');
    if (learningSession) {
        document.addEventListener('keydown', function(e) {
            // Show definition with spacebar
            if (e.code === 'Space') {
                const showDefinitionBtn = document.getElementById('showDefinition');
                const definitionSection = document.getElementById('definitionSection');
                
                if (showDefinitionBtn && showDefinitionBtn.style.display !== 'none') {
                    e.preventDefault();
                    definitionSection.style.display = 'block';
                    showDefinitionBtn.style.display = 'none';
                }
            }
            
            // Next word with right arrow
            if (e.code === 'ArrowRight') {
                const nextButton = document.querySelector('button[name="action"][value="next"]');
                if (nextButton && !nextButton.disabled) {
                    e.preventDefault();
                    nextButton.click();
                }
            }
            
            // Previous word with left arrow
            if (e.code === 'ArrowLeft') {
                const prevButton = document.querySelector('button[name="action"][value="prev"]');
                if (prevButton && !prevButton.disabled) {
                    e.preventDefault();
                    prevButton.click();
                }
            }
            
            // Mark as learned with 'L' key
            if (e.code === 'KeyL') {
                const learnedButton = document.querySelector('button[name="action"][value="learned"]');
                if (learnedButton) {
                    e.preventDefault();
                    learnedButton.click();
                }
            }
            
            // Mark as unlearned with 'U' key
            if (e.code === 'KeyU') {
                const unlearnedButton = document.querySelector('button[name="action"][value="unlearned"]');
                if (unlearnedButton) {
                    e.preventDefault();
                    unlearnedButton.click();
                }
            }
        });
    }
});

// Confirm deletion before submitting the form
document.addEventListener('DOMContentLoaded', function() {
    const deleteButtons = document.querySelectorAll('.delete-library-btn');
    if (deleteButtons) {
        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                if (!confirm('Are you sure you want to delete this library? This action cannot be undone.')) {
                    e.preventDefault();
                }
            });
        });
    }
});

// Search form handling
document.addEventListener('DOMContentLoaded', function() {
    // Handle all search forms
    const searchForms = document.querySelectorAll('form[action*="search"]');
    searchForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const wordInput = this.querySelector('input[name="word"]');
            if (!wordInput || !wordInput.value.trim()) {
                e.preventDefault();
                alert('Please enter a word to search');
                return false;
            }
        });
    });
    
    // Handle search buttons that are links
    const searchButtons = document.querySelectorAll('a.search-button, a[href*="search"]');
    searchButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // If it's a direct link to search without a word parameter, let it go through
            if (this.href.indexOf('?word=') === -1 && this.href.indexOf('&word=') === -1) {
                return true;
            }
            
            // Extract the word parameter
            const url = new URL(this.href);
            const word = url.searchParams.get('word');
            
            // If no word is provided, prompt the user
            if (!word || !word.trim()) {
                e.preventDefault();
                const userWord = prompt('Enter a word to search:');
                if (userWord && userWord.trim()) {
                    // Redirect to search with the provided word
                    window.location.href = `${url.origin}${url.pathname}?word=${encodeURIComponent(userWord.trim())}`;
                }
                return false;
            }
        });
    });
    
    // Add keyboard shortcut for quick search
    document.addEventListener('keydown', function(e) {
        // Alt+S or Ctrl+/ to focus search
        if ((e.altKey && e.code === 'KeyS') || (e.ctrlKey && e.code === 'Slash')) {
            e.preventDefault();
            const searchInput = document.querySelector('form[action*="search"] input[name="word"]');
            if (searchInput) {
                searchInput.focus();
            }
        }
    });
});
