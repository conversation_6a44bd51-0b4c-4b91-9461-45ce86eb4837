#!/usr/bin/env python3
"""
Test script to verify libraries functionality
"""

import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_libraries():
    """Test libraries functionality"""
    try:
        from app import app
        from search import SearchEngine
        
        print("=== TESTING LIBRARIES FUNCTIONALITY ===")
        
        # Test SearchEngine initialization
        search_engine = SearchEngine("gre_words.db")
        print("✅ SearchEngine initialized successfully")
        
        # Test get_libraries_for_user method
        try:
            # Test with a dummy user ID
            libraries = search_engine.get_libraries_for_user(1)
            print(f"✅ get_libraries_for_user returned {len(libraries)} libraries")
            
            if libraries:
                print("Sample library structure:")
                for i, lib in enumerate(libraries[:2]):  # Show first 2 libraries
                    print(f"  Library {i+1}: {lib}")
            else:
                print("  No libraries found for user")
                
        except Exception as e:
            print(f"❌ get_libraries_for_user failed: {e}")
        
        # Test route registration
        print("\n=== CHECKING LIBRARY ROUTES ===")
        library_routes = [
            'libraries',
            'create_library', 
            'view_library',
            'delete_library',
            'download_csv_sample',
            'add_to_library',
            'edit_library'
        ]
        
        registered_endpoints = [rule.endpoint for rule in app.url_map.iter_rules()]
        
        for route in library_routes:
            if route in registered_endpoints:
                print(f"✅ {route} - REGISTERED")
            else:
                print(f"❌ {route} - MISSING")
        
        # Test app context and template rendering
        print("\n=== TESTING TEMPLATE RENDERING ===")
        with app.app_context():
            try:
                # Mock a user in g
                from flask import g
                class MockUser:
                    def __init__(self):
                        self.id = 1
                        self.username = "test_user"
                
                g.user = MockUser()
                
                # Test libraries route
                with app.test_client() as client:
                    # This would normally require authentication, but we'll test the route exists
                    response = client.get('/libraries')
                    print(f"✅ Libraries route accessible (status: {response.status_code})")
                    
            except Exception as e:
                print(f"❌ Template rendering test failed: {e}")
        
        print("\n=== TESTING CSV PROCESSOR ===")
        try:
            from csv_processor import csv_processor
            sample_csv = csv_processor.generate_sample_csv()
            print("✅ CSV sample generation works")
            print(f"  Sample CSV length: {len(sample_csv)} characters")
        except Exception as e:
            print(f"❌ CSV processor test failed: {e}")
        
        print("\n=== TEST COMPLETED ===")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_libraries()
    sys.exit(0 if success else 1)
