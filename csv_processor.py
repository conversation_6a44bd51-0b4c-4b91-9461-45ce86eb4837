"""
Enhanced CSV processing for vocabulary uploads
"""
import csv
import io
import os
import re
import chardet
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from flask import current_app
import pandas as pd

@dataclass
class ValidationResult:
    """Result of CSV validation"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    processed_rows: int
    total_rows: int

@dataclass
class WordEntry:
    """Represents a vocabulary word entry"""
    word: str
    definition: str
    pronunciation: Optional[str] = None
    part_of_speech: Optional[str] = None
    example: Optional[str] = None
    source: Optional[str] = None
    difficulty: Optional[int] = None
    row_number: int = 0

class CSVProcessor:
    """Enhanced CSV processor for vocabulary uploads"""

    def __init__(self):
        self.max_file_size = 16 * 1024 * 1024  # 16MB
        self.allowed_encodings = ['utf-8', 'utf-8-sig', 'latin1', 'cp1252']

        # Flexible column mapping - multiple possible names for each field
        self.column_mappings = {
            'word': ['word', 'term', 'vocabulary', 'vocab', 'lexeme', 'entry', 'headword', 'lemma'],
            'definition': ['definition', 'meaning', 'def', 'explanation', 'description', 'sense', 'gloss'],
            'pronunciation': ['pronunciation', 'phonetic', 'ipa', 'phonetics', 'sound'],
            'part_of_speech': ['part_of_speech', 'pos', 'part of speech', 'grammar', 'type', 'category'],
            'example': ['example', 'sentence', 'usage', 'sample', 'illustration', 'context'],
            'source': ['source', 'origin', 'reference', 'book', 'material', 'from'],
            'difficulty': ['difficulty', 'level', 'grade', 'complexity', 'hard']
        }

        self.optional_columns = ['pronunciation', 'part_of_speech', 'example', 'source', 'difficulty']
        self.required_columns = ['word', 'definition']  # Only word and definition are required

    def detect_encoding(self, file_content: bytes) -> str:
        """Detect file encoding"""
        try:
            result = chardet.detect(file_content)
            encoding = result['encoding']
            confidence = result['confidence']

            if confidence > 0.7 and encoding in self.allowed_encodings:
                return encoding
            else:
                # Fallback to UTF-8
                return 'utf-8'
        except Exception:
            return 'utf-8'

    def detect_delimiter(self, sample_text: str) -> str:
        """Detect CSV delimiter"""
        delimiters = [',', '\t', ';', '|']
        delimiter_counts = {}

        for delimiter in delimiters:
            delimiter_counts[delimiter] = sample_text.count(delimiter)

        # Return the delimiter with the highest count
        return max(delimiter_counts, key=delimiter_counts.get)

    def detect_column_mapping(self, headers: List[str]) -> Dict[str, str]:
        """Detect column mapping based on flexible column names"""
        mapping = {}
        normalized_headers = [h.lower().strip() for h in headers]

        for field, possible_names in self.column_mappings.items():
            for header in normalized_headers:
                if header in possible_names:
                    mapping[field] = header
                    break

        return mapping

    def detect_vocabulary_list_format(self, headers: List[str]) -> bool:
        """Detect if this is a vocabulary list format (multiple columns of words)"""
        # Clean headers and remove empty ones
        clean_headers = [h.strip() for h in headers if h and h.strip()]

        # Check if headers look like vocabulary sources/categories
        vocab_source_indicators = [
            'greg', 'mat', 'vocab', 'capacity', 'manhattan', 'magoosh',
            'barron', 'kaplan', 'princeton', 'gre', 'sat', 'toefl',
            'test', 'prep', 'words', 'list', 'source'
        ]

        # If we have multiple columns and they seem to be vocabulary sources
        if len(clean_headers) >= 2:
            header_text = ' '.join(clean_headers).lower()
            source_matches = sum(1 for indicator in vocab_source_indicators
                               if indicator in header_text)

            # If at least 30% of headers contain vocabulary source indicators
            if source_matches >= len(clean_headers) * 0.3:
                return True
        return False

    def extract_words_from_vocabulary_list(self, file_path: str) -> Tuple[List[str], List[str]]:
        """Extract words from vocabulary list format (multiple columns of words)"""
        words = []
        errors = []

        try:
            # Read file content
            with open(file_path, 'rb') as f:
                file_content = f.read()

            # Detect encoding and delimiter
            encoding = self.detect_encoding(file_content)
            text_content = file_content.decode(encoding)
            delimiter = self.detect_delimiter(text_content[:1024])

            # Parse CSV
            csv_reader = csv.DictReader(io.StringIO(text_content), delimiter=delimiter)
            headers = csv_reader.fieldnames

            if not headers:
                errors.append("No headers found in CSV file")
                return [], errors

            # Clean headers and remove empty ones
            clean_headers = [h.strip() for h in headers if h and h.strip()]

            # Extract words from all columns
            for row_num, row in enumerate(csv_reader, start=2):
                for header in clean_headers:
                    word = row.get(header, '').strip()
                    if word and self._is_valid_word(word):
                        # Remove any source indicators from the word
                        cleaned_word = self._clean_word_from_source(word)
                        if cleaned_word and cleaned_word not in words:
                            words.append(cleaned_word)

            return words, errors

        except Exception as e:
            errors.append(f"Error processing vocabulary list: {str(e)}")
            return [], errors

    def _clean_word_from_source(self, word: str) -> str:
        """Clean word by removing source indicators and numbers"""
        import re

        # Remove common source indicators and numbers
        word = re.sub(r'\d+', '', word)  # Remove numbers
        word = re.sub(r'[^\w\s\'-]', '', word)  # Remove special characters except hyphens and apostrophes
        word = word.strip()

        # Skip if it's just a source name or empty
        source_names = ['greg', 'mat', 'vocab', 'capacity', 'manhattan', 'magoosh',
                       'barron', 'kaplan', 'princeton', 'gre', 'sat', 'toefl']

        if word.lower() in source_names or len(word) < 2:
            return ''

        return word

    def validate_file(self, file_path: str) -> ValidationResult:
        """Validate CSV file structure and content"""
        errors = []
        warnings = []
        processed_rows = 0
        total_rows = 0

        try:
            # Check file size
            file_size = os.path.getsize(file_path)
            if file_size > self.max_file_size:
                errors.append(f"File size ({file_size / 1024 / 1024:.1f}MB) exceeds maximum allowed size (16MB)")
                return ValidationResult(False, errors, warnings, 0, 0)

            # Read file content
            with open(file_path, 'rb') as f:
                file_content = f.read()

            # Detect encoding
            encoding = self.detect_encoding(file_content)

            # Decode content
            try:
                text_content = file_content.decode(encoding)
            except UnicodeDecodeError:
                errors.append(f"Unable to decode file with encoding: {encoding}")
                return ValidationResult(False, errors, warnings, 0, 0)

            # Detect delimiter
            sample = text_content[:1024]  # First 1KB for delimiter detection
            delimiter = self.detect_delimiter(sample)

            # Parse CSV
            csv_reader = csv.DictReader(io.StringIO(text_content), delimiter=delimiter)

            # Validate headers
            headers = csv_reader.fieldnames
            if not headers:
                errors.append("No headers found in CSV file")
                return ValidationResult(False, errors, warnings, 0, 0)

            # Check if this is a vocabulary list format (multiple columns of words)
            if self.detect_vocabulary_list_format(headers):
                # This is a vocabulary list format - validate differently
                words, extract_errors = self.extract_words_from_vocabulary_list(file_path)

                if extract_errors:
                    errors.extend(extract_errors)
                    return ValidationResult(False, errors, warnings, 0, 0)

                if not words:
                    errors.append("No valid words found in the vocabulary list")
                    return ValidationResult(False, errors, warnings, 0, 0)

                # For vocabulary lists, we consider each word as a processed row
                processed_rows = len(words)
                total_rows = processed_rows

                warnings.append(f"Detected vocabulary list format with {len(words)} words from multiple sources")
                return ValidationResult(True, errors, warnings, processed_rows, total_rows)

            # Standard word-definition format validation
            # Detect column mapping using flexible names
            column_mapping = self.detect_column_mapping(headers)

            # Check required columns
            missing_required = []
            for required in self.required_columns:
                if required not in column_mapping:
                    missing_required.append(required)

            if missing_required:
                # Provide helpful error message with suggestions
                available_columns = ', '.join(headers)
                suggested_names = []
                for missing in missing_required:
                    if missing in self.column_mappings:
                        suggested_names.append(f"{missing}: {', '.join(self.column_mappings[missing])}")

                error_msg = f"Could not find required columns: {', '.join(missing_required)}\n"
                error_msg += f"Available columns: {available_columns}\n"
                error_msg += f"Accepted column names:\n" + '\n'.join(suggested_names)
                error_msg += f"\n\nAlternatively, if this is a vocabulary list with multiple columns of words, "
                error_msg += f"make sure the column headers contain vocabulary source names like 'GRE', 'Barron', 'Magoosh', etc."
                errors.append(error_msg)
                return ValidationResult(False, errors, warnings, 0, 0)

            # Validate rows
            for row_num, row in enumerate(csv_reader, start=2):  # Start at 2 (header is row 1)
                total_rows += 1

                # Normalize row keys
                normalized_row = {k.lower().strip(): v for k, v in row.items() if k}

                # Extract required fields using detected column mapping
                word = normalized_row.get(column_mapping.get('word', 'word'), '').strip()
                definition = normalized_row.get(column_mapping.get('definition', 'definition'), '').strip()

                if not word:
                    warnings.append(f"Row {row_num}: Empty word field")
                    continue

                if not definition:
                    warnings.append(f"Row {row_num}: Empty definition field")
                    continue

                # Validate word format
                if not self._is_valid_word(word):
                    warnings.append(f"Row {row_num}: Invalid word format: '{word}'")
                    continue

                # Validate definition length
                if len(definition) > 1000:
                    warnings.append(f"Row {row_num}: Definition too long (max 1000 characters)")
                    continue

                processed_rows += 1

            # Check if we have any valid rows
            if processed_rows == 0:
                errors.append("No valid vocabulary entries found in the file")
                return ValidationResult(False, errors, warnings, processed_rows, total_rows)

            return ValidationResult(True, errors, warnings, processed_rows, total_rows)

        except Exception as e:
            errors.append(f"Error processing file: {str(e)}")
            return ValidationResult(False, errors, warnings, 0, 0)

    def process_csv(self, file_path: str) -> Tuple[List[WordEntry], List[str]]:
        """Process CSV file and return word entries"""
        word_entries = []
        errors = []

        try:
            # First validate the file
            validation = self.validate_file(file_path)
            if not validation.is_valid:
                return [], validation.errors

            # Read file content
            with open(file_path, 'rb') as f:
                file_content = f.read()

            # Detect encoding and delimiter
            encoding = self.detect_encoding(file_content)
            text_content = file_content.decode(encoding)
            delimiter = self.detect_delimiter(text_content[:1024])

            # Parse CSV
            csv_reader = csv.DictReader(io.StringIO(text_content), delimiter=delimiter)

            # Detect headers and format
            headers = csv_reader.fieldnames

            # Check if this is a vocabulary list format
            if self.detect_vocabulary_list_format(headers):
                # Handle vocabulary list format
                words, extract_errors = self.extract_words_from_vocabulary_list(file_path)

                if extract_errors:
                    return [], extract_errors

                # Create word entries for vocabulary list (words only, no definitions)
                for word in words:
                    entry = WordEntry(
                        word=word.lower(),
                        definition=f"Definition for {word}",  # Placeholder definition
                        source="Vocabulary List Import",
                        row_number=len(word_entries) + 1
                    )
                    word_entries.append(entry)

                return word_entries, errors

            # Standard word-definition format processing
            # Detect column mapping
            column_mapping = self.detect_column_mapping(headers)

            for row_num, row in enumerate(csv_reader, start=2):
                # Normalize row keys
                normalized_row = {k.lower().strip(): v.strip() if v else ''
                                for k, v in row.items() if k}

                # Extract fields using detected column mapping
                word = normalized_row.get(column_mapping.get('word', 'word'), '')
                definition = normalized_row.get(column_mapping.get('definition', 'definition'), '')

                # Skip invalid rows
                if not word or not definition or not self._is_valid_word(word):
                    continue

                # Create word entry using flexible column mapping
                entry = WordEntry(
                    word=word.lower(),
                    definition=definition,
                    pronunciation=normalized_row.get(column_mapping.get('pronunciation', 'pronunciation')),
                    part_of_speech=normalized_row.get(column_mapping.get('part_of_speech', 'part_of_speech')),
                    example=normalized_row.get(column_mapping.get('example', 'example')),
                    source=normalized_row.get(column_mapping.get('source', 'source'), 'CSV Upload'),
                    difficulty=self._parse_difficulty(normalized_row.get(column_mapping.get('difficulty', 'difficulty'))),
                    row_number=row_num
                )

                word_entries.append(entry)

            return word_entries, errors

        except Exception as e:
            errors.append(f"Error processing CSV: {str(e)}")
            return [], errors

    def _is_valid_word(self, word: str) -> bool:
        """Validate word format"""
        if not word or len(word) < 1 or len(word) > 100:
            return False

        # Allow letters, hyphens, apostrophes, and spaces
        pattern = r'^[a-zA-Z\s\'-]+$'
        return bool(re.match(pattern, word))

    def _parse_difficulty(self, difficulty_str: str) -> Optional[int]:
        """Parse difficulty level from string"""
        if not difficulty_str:
            return None

        try:
            difficulty = int(difficulty_str)
            if 1 <= difficulty <= 5:
                return difficulty
        except (ValueError, TypeError):
            pass

        return None

    def generate_sample_csv(self) -> str:
        """Generate a sample CSV content for download"""
        sample_data = [
            ['word', 'definition', 'pronunciation', 'part_of_speech', 'example', 'source', 'difficulty'],
            ['aberrant', 'departing from an accepted standard', '/ˈæbərənt/', 'adjective',
             'His aberrant behavior worried his friends.', 'GRE Prep', '3'],
            ['abscond', 'leave hurriedly and secretly', '/æbˈskɒnd/', 'verb',
             'The thief absconded with the stolen goods.', 'GRE Prep', '4'],
            ['abstemious', 'restrained in eating or drinking', '/æbˈstiːmiəs/', 'adjective',
             'She was abstemious in her diet and exercise habits.', 'GRE Prep', '5']
        ]

        output = io.StringIO()
        writer = csv.writer(output)
        writer.writerows(sample_data)
        return output.getvalue()

# Global processor instance
csv_processor = CSVProcessor()
