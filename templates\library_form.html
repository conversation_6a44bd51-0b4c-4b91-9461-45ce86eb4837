{% extends 'base.html' %}

{% block title %}
    {% if library %}Edit Library{% else %}Create Library{% endif %}
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h1 class="mb-0">
                <i class="bi bi-collection"></i> 
                {% if library %}Edit Library{% else %}Create New Library{% endif %}
            </h1>
        </div>
        <div class="card-body">
            <form method="post" action="{% if library %}{{ url_for('edit_library', library_id=library.id) }}{% else %}{{ url_for('create_library') }}{% endif %}">
                <div class="mb-3">
                    <label for="name" class="form-label">Library Name</label>
                    <input type="text" class="form-control" id="name" name="name" 
                           value="{% if library %}{{ library.name }}{% endif %}" required>
                    <div class="form-text">Choose a descriptive name for your word library</div>
                </div>
                
                <div class="mb-3">
                    <label for="description" class="form-label">Description (Optional)</label>
                    <textarea class="form-control" id="description" name="description" rows="3">{% if library %}{{ library.description }}{% endif %}</textarea>
                    <div class="form-text">Add details about the purpose or content of this library</div>
                </div>
                
                <div class="d-flex justify-content-between">
                    <a href="{% if library %}{{ url_for('view_library', library_id=library.id) }}{% else %}{{ url_for('libraries') }}{% endif %}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> 
                        {% if library %}Save Changes{% else %}Create Library{% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    {% if library %}
    <div class="card mt-4">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0">Danger Zone</h5>
        </div>
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h5>Delete Library</h5>
                    <p class="text-muted mb-0">
                        Once you delete a library, there is no going back. Words will remain in the database.
                    </p>
                </div>
                <button class="btn btn-outline-danger" onclick="confirmDeleteLibrary({{ library.id }}, '{{ library.name }}')">
                    Delete Library
                </button>
            </div>
        </div>
    </div>
    
    <!-- Delete Library Modal -->
    <div class="modal fade" id="deleteLibraryModal" tabindex="-1" aria-labelledby="deleteLibraryModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteLibraryModalLabel">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the library "<span id="libraryNameToDelete"></span>"?</p>
                    <p class="text-danger">This action cannot be undone. All words in this library will be removed from the library (but not from the database).</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form id="deleteLibraryForm" method="post" action="">
                        <button type="submit" class="btn btn-danger">Delete Library</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function confirmDeleteLibrary(libraryId, libraryName) {
            document.getElementById('libraryNameToDelete').textContent = libraryName;
            document.getElementById('deleteLibraryForm').action = "{{ url_for('delete_library', library_id=0) }}".replace('0', libraryId);
            
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteLibraryModal'));
            deleteModal.show();
        }
    </script>
    {% endif %}
</div>
{% endblock %}

