{% extends 'base.html' %}

{% block title %}Debug Information{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h2>Debug Information</h2>
    </div>
    <div class="card-body">
        <h3>Library Information</h3>
        <pre>{{ debug_info.library }}</pre>
        
        <h3>Counts</h3>
        <ul>
            <li>Total Words: {{ debug_info.words_count }}</li>
            <li>Learned Words: {{ debug_info.learned_count }}</li>
            <li>Unlearned Words: {{ debug_info.unlearned_count }}</li>
        </ul>
        
        <h3>Stats</h3>
        <pre>{{ debug_info.stats }}</pre>
        
        <h3>All Words</h3>
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Word</th>
                        <th>Definition</th>
                        <th>Source</th>
                        <th>Learned</th>
                    </tr>
                </thead>
                <tbody>
                    {% for word, definition, source, learned in debug_info.words %}
                    <tr>
                        <td>{{ word }}</td>
                        <td>{{ definition|truncate(50) }}</td>
                        <td>{{ source }}</td>
                        <td>{{ learned }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <h3>Learned Words</h3>
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Word</th>
                        <th>Definition</th>
                        <th>Source</th>
                        <th>Learned</th>
                    </tr>
                </thead>
                <tbody>
                    {% for word, definition, source, learned in debug_info.learned_words %}
                    <tr>
                        <td>{{ word }}</td>
                        <td>{{ definition|truncate(50) }}</td>
                        <td>{{ source }}</td>
                        <td>{{ learned }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <h3>Unlearned Words</h3>
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Word</th>
                        <th>Definition</th>
                        <th>Source</th>
                        <th>Learned</th>
                    </tr>
                </thead>
                <tbody>
                    {% for word, definition, source, learned in debug_info.unlearned_words %}
                    <tr>
                        <td>{{ word }}</td>
                        <td>{{ definition|truncate(50) }}</td>
                        <td>{{ source }}</td>
                        <td>{{ learned }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}