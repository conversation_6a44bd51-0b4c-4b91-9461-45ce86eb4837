import random
from datetime import datetime, timedelta
import sqlite3
import os
import hashlib
import logging

# Set up logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('recommendations')

class RecommendationEngine:
    def __init__(self, db_path):
        """Initialize the recommendation engine with database path"""
        self.db_path = db_path
        self.conn = None
        self.cursor = None

        # Test connection to ensure database exists
        self._create_connection()
        if self.conn:
            self.close()

    def _create_connection(self):
        """Create a connection to the SQLite database"""
        try:
            # If connection already exists, return early
            if self.conn is not None:
                return

            # Create a new connection
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row
            self.cursor = self.conn.cursor()

            # Verify that the words table exists
            self.cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='words'")
            if not self.cursor.fetchone():
                print(f"Warning: 'words' table not found in database {self.db_path}")
                self.close()
                self.conn = None
                self.cursor = None
        except sqlite3.Error as e:
            print(f"Error connecting to database: {e}")
            import traceback
            traceback.print_exc()
            self.conn = None
            self.cursor = None

    def _ensure_connection(self):
        """Ensure database connection is active"""
        if self.conn is None or self.cursor is None:
            self._create_connection()

    def get_word_of_the_day(self):
        """Get a word of the day based on the current date"""
        self._create_connection()

        try:
            # Get today's date as string
            today = datetime.now().strftime('%Y-%m-%d')

            # Create a deterministic seed from the date
            seed = int(hashlib.md5(today.encode()).hexdigest(), 16) % 10000

            # Get total word count
            self.cursor.execute("SELECT COUNT(*) FROM words")
            result = self.cursor.fetchone()

            if not result or result[0] == 0:
                print("No words found in database or count is zero")
                return None

            total_words = result[0]

            # Use the seed to select a word
            word_index = seed % total_words

            # Get the word
            self.cursor.execute("""
                SELECT word, definition, source, learned
                FROM words
                LIMIT 1 OFFSET ?
            """, (word_index,))

            word_data = self.cursor.fetchone()

            if not word_data:
                print(f"No word found at index {word_index}")
                return None

            # Get examples for the word
            examples = []
            try:
                self.cursor.execute("""
                    SELECT example FROM word_examples
                    WHERE word = ?
                    LIMIT 3
                """, (word_data[0],))
                examples = [row[0] for row in self.cursor.fetchall()]
            except Exception as e:
                print(f"Error getting examples: {e}")

            # Format the result
            return {
                'word': word_data[0],
                'definition': word_data[1],
                'source': word_data[2],
                'learned': bool(word_data[3]),
                'examples': examples
            }
        except Exception as e:
            print(f"Error getting word of the day: {e}")
            import traceback
            traceback.print_exc()
            return None
        finally:
            self.close()

    def get_quick_learn_words(self, count=5):
        """Get a set of words for quick learning

        Prioritizes:
        1. Unlearned words from the current library
        2. Words that match the user's learning pattern
        3. Random selection of unlearned words
        """
        self._ensure_connection()

        try:
            words = []

            # First try to get unlearned words from current library
            try:
                self.cursor.execute("""
                    SELECT current_library FROM app_settings WHERE id = 1
                """)
                result = self.cursor.fetchone()
            except sqlite3.Error:
                # Handle case where app_settings table might not exist
                logger.warning("Could not query app_settings table")
                result = None

            if result and result['current_library']:
                current_library = result['current_library']

                # Get unlearned words from current library
                try:
                    self.cursor.execute("""
                        SELECT w.word, w.definition, w.source, w.learned
                        FROM words w
                        JOIN library_words lw ON w.id = lw.word_id
                        WHERE lw.library_id = ? AND w.learned = 0
                        ORDER BY RANDOM()
                        LIMIT ?
                    """, (current_library, count))

                    library_words = self.cursor.fetchall()
                    words.extend([dict(w) for w in library_words])
                except sqlite3.Error as e:
                    logger.warning(f"Error getting library words: {e}")

            # If we don't have enough words, get more random unlearned words
            if len(words) < count:
                remaining = count - len(words)

                # Get words that aren't already in our list
                existing_words = [w['word'] for w in words]

                try:
                    if existing_words:
                        placeholders = ','.join(['?'] * len(existing_words))
                        query = f"""
                            SELECT word, definition, source, learned
                            FROM words
                            WHERE learned = 0 AND word NOT IN ({placeholders})
                            ORDER BY RANDOM()
                            LIMIT ?
                        """
                        params = existing_words + [remaining]
                    else:
                        query = """
                            SELECT word, definition, source, learned
                            FROM words
                            WHERE learned = 0
                            ORDER BY RANDOM()
                            LIMIT ?
                        """
                        params = [remaining]

                    self.cursor.execute(query, params)
                    additional_words = self.cursor.fetchall()
                    words.extend([dict(w) for w in additional_words])
                except sqlite3.Error as e:
                    logger.warning(f"Error getting additional words: {e}")

            # Add examples to each word
            for word in words:
                try:
                    self.cursor.execute("""
                        SELECT example FROM words WHERE word = ? LIMIT 1
                    """, (word['word'],))
                    example = self.cursor.fetchone()
                    word['example'] = example['example'] if example else None
                except sqlite3.Error as e:
                    logger.warning(f"Error getting example for word {word['word']}: {e}")
                    word['example'] = None

            return words
        except Exception as e:
            logger.error(f"Error getting quick learn words: {e}")
            return []

    def get_recommended_words(self, count=6):
        """Get personalized word recommendations based on user's learning history and patterns"""
        self._ensure_connection()

        try:
            recommendations = []

            # Strategy 1: Get words similar to recently learned words
            try:
                # Get recently learned words
                self.cursor.execute("""
                    SELECT w.word
                    FROM words w
                    JOIN learned_history lh ON w.id = lh.word_id
                    ORDER BY lh.learned_date DESC
                    LIMIT 5
                """)

                recent_words = [row['word'] for row in self.cursor.fetchall()]

                # If user has learned words, find similar ones
                if recent_words:
                    # Get first letters of recently learned words
                    first_letters = [word[0].lower() for word in recent_words if word]

                    # Find the most common first letter
                    letter_counts = {}
                    for letter in first_letters:
                        letter_counts[letter] = letter_counts.get(letter, 0) + 1

                    # Sort by frequency
                    common_letters = sorted(letter_counts.items(), key=lambda x: x[1], reverse=True)

                    # Try to get words starting with the most common letters
                    for letter, _ in common_letters:
                        if len(recommendations) >= count // 2:
                            break

                        # Get words starting with this letter that aren't learned yet
                        self.cursor.execute("""
                            SELECT word, definition, source, learned
                            FROM words
                            WHERE learned = 0 AND LOWER(word) LIKE ?
                            ORDER BY RANDOM()
                            LIMIT ?
                        """, (f"{letter}%", count // 2 - len(recommendations)))

                        letter_words = self.cursor.fetchall()
                        recommendations.extend([dict(w) for w in letter_words])
            except sqlite3.Error as e:
                logger.warning(f"Error in recommendation strategy 1: {e}")

            # Strategy 2: Get words from the current library
            if len(recommendations) < count:
                try:
                    self.cursor.execute("""
                        SELECT current_library FROM app_settings WHERE id = 1
                    """)
                    result = self.cursor.fetchone()

                    if result and result['current_library']:
                        current_library = result['current_library']

                        # Get words from current library that aren't already in recommendations
                        existing_words = [w['word'] for w in recommendations]
                        placeholders = ','.join(['?'] * len(existing_words)) if existing_words else "''"

                        if existing_words:
                            query = f"""
                                SELECT w.word, w.definition, w.source, w.learned
                                FROM words w
                                JOIN library_words lw ON w.id = lw.word_id
                                WHERE lw.library_id = ? AND w.learned = 0
                                AND w.word NOT IN ({placeholders})
                                ORDER BY RANDOM()
                                LIMIT ?
                            """
                        else:
                            query = """
                                SELECT w.word, w.definition, w.source, w.learned
                                FROM words w
                                JOIN library_words lw ON w.id = lw.word_id
                                WHERE lw.library_id = ? AND w.learned = 0
                                ORDER BY RANDOM()
                                LIMIT ?
                            """

                        params = [current_library] + existing_words + [count - len(recommendations)] if existing_words else [current_library, count - len(recommendations)]
                        self.cursor.execute(query, params)

                        library_words = self.cursor.fetchall()
                        recommendations.extend([dict(w) for w in library_words])
                except sqlite3.Error as e:
                    logger.warning(f"Error in recommendation strategy 2: {e}")

            # Strategy 3: Get random challenging words
            if len(recommendations) < count:
                try:
                    # Get words that aren't already in recommendations
                    existing_words = [w['word'] for w in recommendations]
                    placeholders = ','.join(['?'] * len(existing_words)) if existing_words else "''"

                    query = f"""
                        SELECT word, definition, source, learned
                        FROM words
                        WHERE learned = 0
                        {f"AND word NOT IN ({placeholders})" if existing_words else ""}
                        ORDER BY RANDOM()
                        LIMIT ?
                    """

                    params = existing_words + [count - len(recommendations)] if existing_words else [count - len(recommendations)]
                    self.cursor.execute(query, params)

                    random_words = self.cursor.fetchall()
                    recommendations.extend([dict(w) for w in random_words])
                except sqlite3.Error as e:
                    logger.warning(f"Error in recommendation strategy 3: {e}")

            # Add examples to each word
            for word in recommendations:
                try:
                    self.cursor.execute("""
                        SELECT example FROM words WHERE word = ? LIMIT 1
                    """, (word['word'],))
                    example = self.cursor.fetchone()
                    word['example'] = example['example'] if example else None
                except sqlite3.Error as e:
                    logger.warning(f"Error getting example for word {word['word']}: {e}")
                    word['example'] = None

            return recommendations
        except Exception as e:
            logger.error(f"Error getting recommended words: {e}")
            return []

    def close(self):
        """Close the database connection"""
        try:
            if self.conn:
                self.conn.close()
                self.conn = None
                self.cursor = None
                logger.info("Database connection closed")
        except sqlite3.Error as e:
            logger.error(f"Error closing database connection: {e}")




