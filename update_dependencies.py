"""
This script updates Flask and its dependencies to compatible versions.
Run this script to fix compatibility issues.
"""
import subprocess
import sys
import os

def update_dependencies():
    print("Updating Flask and its dependencies to compatible versions...")
    
    # Install compatible versions
    subprocess.check_call([
        sys.executable, "-m", "pip", "install",
        "Flask==2.0.1",
        "Werkzeug==2.0.1",
        "flask-wtf==1.0.0",
        "flask-sqlalchemy==2.5.1"
    ])
    
    print("\nDependencies updated successfully!")
    print("\nTo use the updated dependencies:")
    print("1. Remove or comment out the patching code in app.py")
    print("2. Restart your application")
    
    # Ask if the user wants to run the application now
    response = input("\nDo you want to run the application now? (y/n): ")
    if response.lower() == 'y':
        os.environ['FLASK_ENV'] = 'development'
        subprocess.call([sys.executable, "app.py"])

if __name__ == "__main__":
    update_dependencies()

