"""
This script patches Flask-WTF to work with newer versions of Werkzeug.
Run this script before importing Flask-WTF.
"""
import sys
import types

# Create a mock module for werkzeug.security with safe_str_cmp
class MockSecurity:
    def safe_str_cmp(a, b):
        """
        Replacement for werkzeug.security.safe_str_cmp which was removed in newer versions.
        This function performs a constant-time comparison of two strings.
        """
        if len(a) != len(b):
            return False
        
        result = 0
        for x, y in zip(a, b):
            result |= ord(x) ^ ord(y) if isinstance(x, str) else x ^ y
        
        return result == 0

# Create the module
mock_security = types.ModuleType('werkzeug.security')
mock_security.safe_str_cmp = MockSecurity.safe_str_cmp

# Add other functions from werkzeug.security to our mock
try:
    from werkzeug.security import generate_password_hash, check_password_hash
    mock_security.generate_password_hash = generate_password_hash
    mock_security.check_password_hash = check_password_hash
except ImportError:
    pass

# Patch sys.modules
sys.modules['werkzeug.security'] = mock_security

print("Patched werkzeug.security with safe_str_cmp compatibility function")