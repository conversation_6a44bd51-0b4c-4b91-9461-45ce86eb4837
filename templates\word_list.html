{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>{{ title }}</h1>

    {% if words %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Word</th>
                        <th>Definition</th>
                        <th>Source</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for word in words %}
                        <tr>
                            <td>
                                <a href="{{ url_for('view_word', word=word.word) }}">{{ word.word }}</a>
                                {% if is_word_learned(word.word) %}
                                    <span class="badge bg-success ms-2">Learned</span>
                                {% endif %}
                            </td>
                            <td>{{ word.definition }}</td>
                            <td>{{ word.source }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('view_word', word=word.word) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i> View
                                    </a>

                                    {% if is_word_learned(word.word) %}
                                        <form method="post" action="{{ url_for('mark_word') }}" class="d-inline">
                                            <input type="hidden" name="word" value="{{ word.word }}">
                                            <input type="hidden" name="action" value="unlearned">
                                            <input type="hidden" name="redirect_url" value="{{ request.path }}">
                                            <button type="submit" class="btn btn-sm btn-outline-warning">
                                                <i class="bi bi-x-circle"></i> Mark Unlearned
                                            </button>
                                        </form>
                                    {% else %}
                                        <form method="post" action="{{ url_for('mark_word') }}" class="d-inline">
                                            <input type="hidden" name="word" value="{{ word.word }}">
                                            <input type="hidden" name="action" value="learned">
                                            <input type="hidden" name="redirect_url" value="{{ request.path }}">
                                            <button type="submit" class="btn btn-sm btn-outline-success">
                                                <i class="bi bi-check-circle"></i> Mark Learned
                                            </button>
                                        </form>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination controls if needed -->
        {% if pagination %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% if pagination.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for(request.endpoint, page=pagination.prev_num, **request.view_args) }}">Previous</a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">Previous</span>
                </li>
                {% endif %}

                {% for page in pagination.iter_pages() %}
                    {% if page %}
                        {% if page != pagination.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for(request.endpoint, page=page, **request.view_args) }}">{{ page }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if pagination.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for(request.endpoint, page=pagination.next_num, **request.view_args) }}">Next</a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">Next</span>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

    {% else %}
        <div class="alert alert-info">
            <i class="bi bi-info-circle"></i> No words found.
        </div>
    {% endif %}

    <!-- Action buttons -->
    <div class="mt-4">
        <a href="{{ url_for('index') }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Back to Home
        </a>

        {% if list_type == 'library' and library_id %}
        <a href="/library/{{ library_id }}/add-multiple-words" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Add Words
        </a>
        {% endif %}

        {% if words %}
        <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#exportModal">
            <i class="bi bi-download"></i> Export Words
        </button>
        {% endif %}
    </div>
</div>

<!-- Export Modal -->
{% if words %}
<div class="modal fade" id="exportModal" tabindex="-1" aria-labelledby="exportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exportModalLabel">Export Words</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Choose a format to export your words:</p>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('export_words', format='csv', list_type=list_type, **({'library_id': library_id} if library_id else {})) }}" class="btn btn-outline-primary">
                        <i class="bi bi-filetype-csv"></i> CSV Format
                    </a>
                    <a href="{{ url_for('export_words', format='txt', list_type=list_type, **({'library_id': library_id} if library_id else {})) }}" class="btn btn-outline-primary">
                        <i class="bi bi-filetype-txt"></i> Text Format
                    </a>
                    <a href="{{ url_for('export_words', format='json', list_type=list_type, **({'library_id': library_id} if library_id else {})) }}" class="btn btn-outline-primary">
                        <i class="bi bi-filetype-json"></i> JSON Format
                    </a>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
