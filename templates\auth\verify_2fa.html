{% extends "base.html" %}

{% block title %}Two-Factor Authentication - GRE Vocabulary Learner{% endblock %}

{% block extra_css %}
<style>
    .verification-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 20px;
        color: white;
        max-width: 500px;
        margin: 0 auto;
    }
    
    .verification-icon {
        font-size: 4rem;
        color: var(--primary-color);
        margin-bottom: 1rem;
    }
    
    .code-input {
        font-size: 2rem;
        letter-spacing: 1rem;
        text-align: center;
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 10px;
    }
    
    .code-input:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: var(--primary-color);
        color: white;
        box-shadow: 0 0 0 0.2rem rgba(255, 119, 34, 0.25);
    }
    
    .code-input::placeholder {
        color: rgba(255, 255, 255, 0.5);
        letter-spacing: 0.5rem;
    }
    
    .backup-code-section {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 10px;
        padding: 1.5rem;
        margin-top: 2rem;
    }
    
    .security-tips {
        background: rgba(255, 193, 7, 0.1);
        border: 1px solid rgba(255, 193, 7, 0.3);
        border-radius: 10px;
        padding: 1rem;
        margin-top: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center min-vh-100 align-items-center">
        <div class="col-lg-6">
            <div class="card verification-card">
                <div class="card-body p-5 text-center">
                    <i class="bi bi-shield-check verification-icon"></i>
                    
                    <h2 class="mb-3">Two-Factor Authentication</h2>
                    <p class="mb-4">
                        Enter the 6-digit code from your authenticator app to complete your login.
                    </p>
                    
                    <form action="{{ url_for('auth.verify_2fa') }}" method="POST" id="verificationForm">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        
                        <div class="mb-4">
                            <input type="text" class="form-control code-input" 
                                   id="token" name="token" placeholder="000000" 
                                   maxlength="6" pattern="[0-9]{6}" required autocomplete="one-time-code">
                            {% if errors.token %}
                            <div class="text-danger mt-2">
                                <i class="bi bi-exclamation-circle"></i> {{ errors.token }}
                            </div>
                            {% endif %}
                        </div>
                        
                        <button type="submit" class="btn btn-warning btn-lg w-100 mb-3">
                            <i class="bi bi-check-circle"></i> Verify & Login
                        </button>
                    </form>
                    
                    <!-- Backup Code Section -->
                    <div class="backup-code-section">
                        <h6><i class="bi bi-key"></i> Can't access your authenticator?</h6>
                        <p class="small mb-3">Use a backup code instead</p>
                        
                        <button class="btn btn-outline-light btn-sm" onclick="toggleBackupCode()">
                            <i class="bi bi-arrow-down-circle"></i> Use Backup Code
                        </button>
                        
                        <div id="backupCodeForm" class="d-none mt-3">
                            <form action="{{ url_for('auth.verify_2fa') }}" method="POST">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                <div class="mb-3">
                                    <input type="text" class="form-control" 
                                           name="backup_code" placeholder="Enter backup code" 
                                           style="text-transform: uppercase;">
                                </div>
                                <button type="submit" class="btn btn-outline-warning btn-sm">
                                    Verify Backup Code
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Security Tips -->
                    <div class="security-tips text-start">
                        <h6><i class="bi bi-lightbulb"></i> Security Tips</h6>
                        <ul class="small mb-0">
                            <li>Never share your verification codes with anyone</li>
                            <li>If you suspect unauthorized access, change your password immediately</li>
                            <li>Keep your backup codes in a secure location</li>
                        </ul>
                    </div>
                    
                    <!-- Alternative Actions -->
                    <div class="mt-4 pt-3 border-top border-light">
                        <p class="small mb-2">Having trouble?</p>
                        <div class="d-flex justify-content-center gap-3">
                            <a href="{{ url_for('auth.login') }}" class="btn btn-outline-light btn-sm">
                                <i class="bi bi-arrow-left"></i> Back to Login
                            </a>
                            <a href="#" class="btn btn-outline-light btn-sm" onclick="showHelp()">
                                <i class="bi bi-question-circle"></i> Get Help
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Help Modal -->
<div class="modal fade" id="helpModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-question-circle"></i> 2FA Help
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>Common Issues & Solutions</h6>
                
                <div class="accordion" id="helpAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#help1">
                                Code not working or expired
                            </button>
                        </h2>
                        <div id="help1" class="accordion-collapse collapse" data-bs-parent="#helpAccordion">
                            <div class="accordion-body">
                                <ul>
                                    <li>Make sure your phone's time is synchronized</li>
                                    <li>Try generating a new code (codes refresh every 30 seconds)</li>
                                    <li>Check if you're using the correct authenticator app</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#help2">
                                Lost access to authenticator app
                            </button>
                        </h2>
                        <div id="help2" class="accordion-collapse collapse" data-bs-parent="#helpAccordion">
                            <div class="accordion-body">
                                <ul>
                                    <li>Use one of your backup codes</li>
                                    <li>Contact support if you don't have backup codes</li>
                                    <li>You'll need to verify your identity to disable 2FA</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#help3">
                                New phone or device
                            </button>
                        </h2>
                        <div id="help3" class="accordion-collapse collapse" data-bs-parent="#helpAccordion">
                            <div class="accordion-body">
                                <ul>
                                    <li>Use a backup code to login</li>
                                    <li>Set up 2FA again on your new device</li>
                                    <li>Generate new backup codes for future use</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a href="mailto:<EMAIL>" class="btn btn-primary">Contact Support</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-format verification code input
document.getElementById('token').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, ''); // Remove non-digits
    if (value.length > 6) {
        value = value.substring(0, 6);
    }
    e.target.value = value;
});

// Auto-submit when 6 digits are entered
document.getElementById('token').addEventListener('input', function(e) {
    if (e.target.value.length === 6) {
        // Add a small delay to allow user to see the complete code
        setTimeout(() => {
            if (e.target.value.length === 6) {
                document.getElementById('verificationForm').submit();
            }
        }, 500);
    }
});

// Focus on token input when page loads
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('token').focus();
});

// Toggle backup code form
function toggleBackupCode() {
    const backupForm = document.getElementById('backupCodeForm');
    const isHidden = backupForm.classList.contains('d-none');
    
    if (isHidden) {
        backupForm.classList.remove('d-none');
        backupForm.querySelector('input').focus();
    } else {
        backupForm.classList.add('d-none');
        document.getElementById('token').focus();
    }
}

// Show help modal
function showHelp() {
    const helpModal = new bootstrap.Modal(document.getElementById('helpModal'));
    helpModal.show();
}

// Countdown timer for code refresh (optional enhancement)
let countdown = 30;
function updateCountdown() {
    const now = new Date();
    const seconds = now.getSeconds();
    countdown = 30 - (seconds % 30);
    
    // You could add a visual countdown here if desired
    setTimeout(updateCountdown, 1000);
}

// Start countdown
updateCountdown();
</script>
{% endblock %}
