{% extends 'base.html' %}

{% block title %}Merge Libraries{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h2 class="mb-0">Merge Libraries</h2>
    </div>
    <div class="card-body">
        <p class="card-text">
            Create a new library by merging words from multiple existing libraries.
        </p>
        
        <form method="post" action="{{ url_for('merge_libraries') }}">
            <div class="mb-3">
                <label for="name" class="form-label">New Library Name</label>
                <input type="text" class="form-control" id="name" name="name" required>
            </div>
            <div class="mb-3">
                <label for="description" class="form-label">Description (Optional)</label>
                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Select Libraries to Merge</label>
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> Select at least two libraries to merge.
                </div>
                
                {% if libraries %}
                <div class="list-group">
                    {% for lib_id, name, desc, _ in libraries %}
                    <label class="list-group-item">
                        <input class="form-check-input me-1" type="checkbox" name="source_libraries" value="{{ lib_id }}">
                        <strong>{{ name }}</strong>
                        {% if desc %}
                        <div><small class="text-muted">{{ desc }}</small></div>
                        {% endif %}
                    </label>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i> No libraries available to merge.
                </div>
                {% endif %}
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{{ url_for('libraries') }}" class="btn btn-outline-secondary">Cancel</a>
                <button type="submit" class="btn btn-primary">Create Merged Library</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}