{% extends 'base.html' %}

{% block title %}Learning Session{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Learning Session</h4>
                    <span class="badge bg-primary">{{ current_index + 1 }} / {{ total_words }}</span>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <h2 class="display-4 fw-bold text-primary">{{ word }}</h2>
                        
                        <div class="collapse" id="definitionCollapse">
                            <div class="card card-body mt-3 mb-3">
                                <h5 class="card-title">Definition</h5>
                                <p class="lead">{{ definition }}</p>
                                
                                {% if source %}
                                <div class="text-muted small mt-2">
                                    Source: {{ source }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <button class="btn btn-outline-primary mt-3" type="button" data-bs-toggle="collapse" data-bs-target="#definitionCollapse" aria-expanded="false" aria-controls="definitionCollapse">
                            Show/Hide Definition
                        </button>
                    </div>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{{ url_for('session_action', session_id=session_id, action='prev') }}" class="btn btn-outline-secondary {% if current_index == 0 %}disabled{% endif %}">
                            <i class="bi bi-arrow-left"></i> Previous
                        </a>
                        
                        <div class="btn-group" role="group">
                            <a href="{{ url_for('session_action', session_id=session_id, action='mark-learned') }}" class="btn btn-success">
                                <i class="bi bi-check-circle"></i> I Know This
                            </a>
                            <a href="{{ url_for('session_action', session_id=session_id, action='mark-unlearned') }}" class="btn btn-warning">
                                <i class="bi bi-x-circle"></i> Still Learning
                            </a>
                        </div>
                        
                        {% if current_index < total_words - 1 %}
                        <a href="{{ url_for('session_action', session_id=session_id, action='next') }}" class="btn btn-primary">
                            Next <i class="bi bi-arrow-right"></i>
                        </a>
                        {% else %}
                        <a href="{{ url_for('session_action', session_id=session_id, action='end') }}" class="btn btn-primary">
                            Finish <i class="bi bi-check2-all"></i>
                        </a>
                        {% endif %}
                    </div>
                </div>
                <div class="card-footer text-center">
                    <a href="{{ url_for('search', word=word) }}" class="btn btn-sm btn-link" target="_blank">
                        <i class="bi bi-search"></i> View Details
                    </a>
                    <a href="https://www.google.com/search?q=define+{{ word }}" class="btn btn-sm btn-link" target="_blank">
                        <i class="bi bi-google"></i> Search on Google
                    </a>
                    <a href="{{ url_for('session_action', session_id=session_id, action='end') }}" class="btn btn-sm btn-link text-danger">
                        <i class="bi bi-x-circle"></i> End Session
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}