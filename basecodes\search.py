import csv
import sqlite3
import random
import os
import sys
import requests
import json
import webbrowser
from typing import Any, List, Tuple, Dict, Optional
from datetime import datetime
import tempfile
import threading

# Try to import optional dependencies
try:
    from gtts import gTTS
    from playsound import playsound
    AUDIO_SUPPORT = True
except ImportError:
    AUDIO_SUPPORT = False

# Increase recursion limit (temporary solution)
sys.setrecursionlimit(10000)

class Node:
    def __init__(self, data, definition=None, source=None, learned=False):
        self.data = data  # The word
        self.definition = definition  # Word definition
        self.source = source  # Source of the word
        self.learned = learned  # Learning status
        self.left = None
        self.right = None

class BinarySearchTree:
    def __init__(self):
        self.root = None
        
    def insert(self, data, definition=None, source=None, learned=False):
        if not self.root:
            self.root = Node(data, definition, source, learned)
        else:
            self._insert_recursive(self.root, data, definition, source, learned)
            
    def _insert_recursive(self, node, data, definition, source, learned):
        if data < node.data:
            if node.left is None:
                node.left = Node(data, definition, source, learned)
            else:
                self._insert_recursive(node.left, data, definition, source, learned)
        elif data > node.data:
            if node.right is None:
                node.right = Node(data, definition, source, learned)
            else:
                self._insert_recursive(node.right, data, definition, source, learned)
        else:
            # Update existing node if the word already exists
            node.definition = definition
            node.source = source
            node.learned = learned
                
    def search(self, data):
        return self._search_recursive(self.root, data)
    
    def _search_recursive(self, node, data):
        if node is None or node.data == data:
            return node
        if data < node.data:
            return self._search_recursive(node.left, data)
        return self._search_recursive(node.right, data)
    
    def delete(self, data):
        self.root = self._delete_recursive(self.root, data)
        
    def _delete_recursive(self, node, data):
        if node is None:
            return node
        if data < node.data:
            node.left = self._delete_recursive(node.left, data)
        elif data > node.data:
            node.right = self._delete_recursive(node.right, data)
        else:
            if node.left is None:
                return node.right
            elif node.right is None:
                return node.left
            temp = self._min_value_node(node.right)
            node.data = temp.data
            node.definition = temp.definition
            node.source = temp.source
            node.learned = temp.learned
            node.right = self._delete_recursive(node.right, temp.data)
        return node
    
    def _min_value_node(self, node):
        current = node
        while current.left is not None:
            current = current.left
        return current
    
    def inorder_traversal(self):
        """Return all nodes in sorted order"""
        result = []
        self._inorder_recursive(self.root, result)
        return result
    
    def _inorder_recursive(self, node, result):
        if node:
            self._inorder_recursive(node.left, result)
            result.append((node.data, node.definition, node.source, node.learned))
            self._inorder_recursive(node.right, result)
    
    def get_words_by_letter(self, letter, limit=None, only_unlearned=False):
        """Get words starting with a specific letter"""
        result = []
        self._get_words_by_letter_recursive(self.root, letter.lower(), result, only_unlearned)
        if limit and len(result) > limit:
            return random.sample(result, limit)
        return result
    
    def _get_words_by_letter_recursive(self, node, letter, result, only_unlearned):
        if node:
            self._get_words_by_letter_recursive(node.left, letter, result, only_unlearned)
            if node.data.lower().startswith(letter):
                if not only_unlearned or not node.learned:
                    result.append((node.data, node.definition, node.source, node.learned))
            self._get_words_by_letter_recursive(node.right, letter, result, only_unlearned)

class SearchEngine:
    def __init__(self, db_name: str):
        self.db_name = db_name
        self.conn = sqlite3.connect(db_name)
        self.cursor = self.conn.cursor()
        self.create_tables()
        self.bst = BinarySearchTree()
        self.load_from_db()
        
        # Google Dictionary API endpoint
        self.dictionary_api_url = "https://api.dictionaryapi.dev/api/v2/entries/en/"
        
    def create_tables(self):
        # Check if words table exists
        self.cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='words'")
        table_exists = self.cursor.fetchone()
        
        if not table_exists:
            # Create the words table if it doesn't exist
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS words (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    word TEXT NOT NULL UNIQUE,
                    definition TEXT,
                    source TEXT,
                    learned BOOLEAN DEFAULT 0
                )
            ''')
        else:
            # Check if learned column exists
            self.cursor.execute("PRAGMA table_info(words)")
            columns = self.cursor.fetchall()
            column_names = [column[1] for column in columns]
            
            # Add learned column if it doesn't exist
            if 'learned' not in column_names:
                self.cursor.execute('ALTER TABLE words ADD COLUMN learned BOOLEAN DEFAULT 0')
        
        # Create other tables
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS learned_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                word_id INTEGER,
                learned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (word_id) REFERENCES words(id)
            )
        ''')
        
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS libraries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS library_words (
                library_id INTEGER,
                word_id INTEGER,
                PRIMARY KEY (library_id, word_id),
                FOREIGN KEY (library_id) REFERENCES libraries(id),
                FOREIGN KEY (word_id) REFERENCES words(id)
            )
        ''')
        
        self.conn.commit()
    
    def load_from_db(self):
        """Load all words from the database into the BST"""
        try:
            # Try to select with learned column
            self.cursor.execute('SELECT word, definition, source, learned FROM words')
        except sqlite3.OperationalError:
            # Fallback to select without learned column
            self.cursor.execute('SELECT word, definition, source FROM words')
            words = self.cursor.fetchall()
            # Add default learned value (False)
            words = [(word, definition, source, False) for word, definition, source in words]
        else:
            words = self.cursor.fetchall()
        
        # Sort words by the word itself
        words.sort(key=lambda x: x[0])
        
        # Build a balanced BST
        if words:
            self._build_bst_from_sorted_array(words, 0, len(words) - 1)
        
    def load_csv(self, csv_file: str):
        words = []
        try:
            with open(csv_file, 'r', encoding='utf-8') as file:
                # First, try to detect the delimiter
                sample = file.read(1024)
                file.seek(0)
                
                if '\t' in sample:
                    delimiter = '\t'
                else:
                    delimiter = ','
                
                reader = csv.reader(file, delimiter=delimiter)
                for row in reader:
                    if len(row) >= 3:
                        words.append((row[0].strip(), row[1].strip(), row[2].strip(), False))
                    elif len(row) == 2:
                        words.append((row[0].strip(), row[1].strip(), None, False))
                    elif len(row) == 1 and row[0].strip():
                        words.append((row[0].strip(), None, None, False))
        except Exception as e:
            print(f"Error reading CSV: {e}")
            # Try alternative approach for tab-delimited files
            try:
                words = []
                with open(csv_file, 'r', encoding='utf-8') as file:
                    for line in file:
                        parts = line.strip().split('\t')
                        if len(parts) >= 3:
                            words.append((parts[0].strip(), parts[1].strip(), parts[2].strip(), False))
                        elif len(parts) == 2:
                            words.append((parts[0].strip(), parts[1].strip(), None, False))
                        elif len(parts) == 1 and parts[0].strip():
                            words.append((parts[0].strip(), None, None, False))
            except Exception as e:
                print(f"Alternative parsing also failed: {e}")
                return
        
        # Sort words by the word itself
        words.sort(key=lambda x: x[0])
        
        # Use iterative approach to build a balanced BST
        self._build_balanced_bst(words)
            
    def _build_balanced_bst(self, sorted_words):
        """Build a balanced BST from sorted words using iterative approach"""
        # First, clear any existing data
        self.cursor.execute('DELETE FROM words')
        self.conn.commit()
        self.bst = BinarySearchTree()
        
        # Insert words into database
        for word, definition, source, learned in sorted_words:
            try:
                self.cursor.execute(
                    'INSERT INTO words (word, definition, source, learned) VALUES (?, ?, ?, ?)', 
                    (word, definition, source, learned)
                )
            except sqlite3.IntegrityError:
                pass  # Skip duplicates
        
        self.conn.commit()
        
        # Build a balanced BST
        self._build_bst_from_sorted_array(sorted_words, 0, len(sorted_words) - 1)
    
    def _build_bst_from_sorted_array(self, sorted_words, start, end):
        """Build a balanced BST from a sorted array using divide and conquer"""
        if start > end:
            return
        
        # Get the middle element and make it root
        mid = (start + end) // 2
        word, definition, source, learned = sorted_words[mid]
        
        # Insert the middle element
        self.bst.insert(word, definition, source, learned)
        
        # Recursively construct left and right subtrees
        self._build_bst_from_sorted_array(sorted_words, start, mid - 1)
        self._build_bst_from_sorted_array(sorted_words, mid + 1, end)
            
    def insert_word(self, word: str, definition: str = None, source: str = None, learned: bool = False):
        try:
            self.cursor.execute(
                'INSERT INTO words (word, definition, source, learned) VALUES (?, ?, ?, ?)', 
                (word, definition, source, learned)
            )
            self.conn.commit()
            self.bst.insert(word, definition, source, learned)
        except sqlite3.IntegrityError:
            # Update if word already exists
            self.cursor.execute(
                'UPDATE words SET definition = ?, source = ?, learned = ? WHERE word = ?',
                (definition, source, learned, word)
            )
            self.conn.commit()
            # BST insert handles updates automatically
            self.bst.insert(word, definition, source, learned)
            
    def search_word(self, word: str) -> Tuple[str, str, str, bool]:
        """Search for a word and return (word, definition, source, learned) if found"""
        node = self.bst.search(word)
        if node:
            return (node.data, node.definition, node.source, node.learned)
        return None
    
    def binary_search_db(self, word: str) -> Tuple[str, str, str, bool]:
        """Perform a binary search on the database (for demonstration)"""
        self.cursor.execute('SELECT word, definition, source, learned FROM words ORDER BY word')
        all_words = self.cursor.fetchall()
        
        left, right = 0, len(all_words) - 1
        
        while left <= right:
            mid = (left + right) // 2
            if all_words[mid][0] == word:
                return all_words[mid]
            elif all_words[mid][0] < word:
                left = mid + 1
            else:
                right = mid - 1
                
        return None
    
    def delete_word(self, word: str):
        self.cursor.execute('DELETE FROM words WHERE word = ?', (word,))
        self.conn.commit()
        self.bst.delete(word)
    
    def get_all_words(self) -> List[Tuple[str, str, str, bool]]:
        """Get all words in sorted order using BST traversal"""
        return self.bst.inorder_traversal()
    
    def get_learned_words(self) -> List[Tuple[str, str, str, bool]]:
        """Get all learned words"""
        self.cursor.execute('''
            SELECT word, definition, source, learned 
            FROM words 
            WHERE learned = 1
            ORDER BY word
        ''')
        return self.cursor.fetchall()
    
    def get_remaining_words(self) -> List[Tuple[str, str, str, bool]]:
        """Get all unlearned words"""
        self.cursor.execute('''
            SELECT word, definition, source, learned 
            FROM words 
            WHERE learned = 0
            ORDER BY word
        ''')
        return self.cursor.fetchall()
    
    def mark_word_as_learned(self, word: str):
        """Mark a word as learned and record in history"""
        # First get the word ID
        self.cursor.execute('SELECT id FROM words WHERE word = ?', (word,))
        result = self.cursor.fetchone()
        if result:
            word_id = result[0]
            
            # Update the learned status
            self.cursor.execute('UPDATE words SET learned = 1 WHERE id = ?', (word_id,))
            
            # Add to learned history
            self.cursor.execute('INSERT INTO learned_history (word_id) VALUES (?)', (word_id,))
            
            self.conn.commit()
            
            # Update the BST
            node = self.bst.search(word)
            if node:
                node.learned = True
                
            return True
        return False
    
    def mark_word_as_unlearned(self, word: str):
        """Mark a word as unlearned"""
        # First get the word ID
        self.cursor.execute('SELECT id FROM words WHERE word = ?', (word,))
        result = self.cursor.fetchone()
        if result:
            word_id = result[0]
            
            # Update the learned status
            self.cursor.execute('UPDATE words SET learned = 0 WHERE id = ?', (word_id,))
            
            # Remove from learned history if exists
            self.cursor.execute('DELETE FROM learned_history WHERE word_id = ?', (word_id,))
            
            self.conn.commit()
            
            # Update the BST
            node = self.bst.search(word)
            if node:
                node.learned = False
                
            return True
        return False
    
    def get_learning_words(self, count: int = 10) -> List[Tuple[str, str, str, bool]]:
        """Get a random selection of unlearned words for learning"""
        # Select a random letter
        letter = random.choice('abcdefghijklmnopqrstuvwxyz')
        
        # Get words starting with that letter
        words = self.bst.get_words_by_letter(letter, count, True)
        
        # If not enough words with that letter, get more from other letters
        if len(words) < count:
            self.cursor.execute('''
                SELECT word, definition, source, learned 
                FROM words 
                WHERE learned = 0 AND word NOT LIKE ?
                ORDER BY RANDOM()
                LIMIT ?
            ''', (f"{letter}%", count - len(words)))
            
            additional_words = self.cursor.fetchall()
            words.extend(additional_words)
        
        return words[:count]
    
    def create_library(self, name: str, description: str = None) -> int:
        """Create a new library and return its ID"""
        try:
            self.cursor.execute(
                'INSERT INTO libraries (name, description) VALUES (?, ?)', 
                (name, description)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.IntegrityError:
            print(f"Library with name '{name}' already exists")
            return None
    
    def delete_library(self, library_id: int) -> bool:
        """Delete a library and its word associations"""
        try:
            # Delete word associations
            self.cursor.execute('DELETE FROM library_words WHERE library_id = ?', (library_id,))
            
            # Delete the library
            self.cursor.execute('DELETE FROM libraries WHERE id = ?', (library_id,))
            
            self.conn.commit()
            return True
        except Exception as e:
            print(f"Error deleting library: {e}")
            return False
    
    def add_word_to_library(self, library_id: int, word: str) -> bool:
        """Add a word to a library"""
        try:
            # Get word ID
            self.cursor.execute('SELECT id FROM words WHERE word = ?', (word,))
            result = self.cursor.fetchone()
            if not result:
                print(f"Word '{word}' not found in database")
                return False
            
            word_id = result[0]
            
            # Add to library
            self.cursor.execute(
                'INSERT INTO library_words (library_id, word_id) VALUES (?, ?)', 
                (library_id, word_id)
            )
            self.conn.commit()
            return True
        except sqlite3.IntegrityError:
            # Word already in library
            return True
        except Exception as e:
            print(f"Error adding word to library: {e}")
            return False
    
    def remove_word_from_library(self, library_id: int, word: str) -> bool:
        """Remove a word from a library"""
        try:
            # Get word ID
            self.cursor.execute('SELECT id FROM words WHERE word = ?', (word,))
            result = self.cursor.fetchone()
            if not result:
                print(f"Word '{word}' not found in database")
                return False
            
            word_id = result[0]
            
            # Remove from library
            self.cursor.execute(
                'DELETE FROM library_words WHERE library_id = ? AND word_id = ?', 
                (library_id, word_id)
            )
            self.conn.commit()
            return True
        except Exception as e:
            print(f"Error removing word from library: {e}")
            return False
    
    def get_libraries(self) -> List[Tuple[int, str, str, str]]:
        """Get all libraries"""
        self.cursor.execute('SELECT id, name, description, created_date FROM libraries ORDER BY name')
        return self.cursor.fetchall()
    
    def get_library_words(self, library_id: int) -> List[Tuple[str, str, str, bool]]:
        """Get all words in a library"""
        self.cursor.execute('''
            SELECT w.word, w.definition, w.source, w.learned
            FROM words w
            JOIN library_words lw ON w.id = lw.word_id
            WHERE lw.library_id = ?
            ORDER BY w.word
        ''', (library_id,))
        return self.cursor.fetchall()
    
    def export_library(self, library_id: int, filename: str) -> bool:
        """Export a library to a CSV file"""
        try:
            words = self.get_library_words(library_id)
            
            with open(filename, 'w', newline='', encoding='utf-8') as file:
                writer = csv.writer(file, delimiter='\t')
                for word, definition, source, _ in words:
                    if definition and source:
                        writer.writerow([word, definition, source])
                    elif definition:
                        writer.writerow([word, definition])
                    else:
                        writer.writerow([word])
            
            return True
        except Exception as e:
            print(f"Error exporting library: {e}")
            return False
    
    def import_library(self, name: str, description: str, filename: str) -> int:
        """Import a CSV file as a new library"""
        try:
            # Create new library
            library_id = self.create_library(name, description)
            if not library_id:
                return None
            
            # Load words from CSV
            words = []
            with open(filename, 'r', encoding='utf-8') as file:
                # Detect delimiter
                sample = file.read(1024)
                file.seek(0)
                
                if '\t' in sample:
                    delimiter = '\t'
                else:
                    delimiter = ','
                
                reader = csv.reader(file, delimiter=delimiter)
                for row in reader:
                    if len(row) >= 3:
                        words.append((row[0].strip(), row[1].strip(), row[2].strip()))
                    elif len(row) == 2:
                        words.append((row[0].strip(), row[1].strip(), None))
                    elif len(row) == 1 and row[0].strip():
                        words.append((row[0].strip(), None, None))
            
            # Add words to database and library
            for word, definition, source in words:
                # Add to database if not exists
                self.insert_word(word, definition, source)
                
                # Add to library
                self.add_word_to_library(library_id, word)
            
            return library_id
        except Exception as e:
            print(f"Error importing library: {e}")
            return None
    
    def get_word_details_from_api(self, word: str) -> Dict:
        """Get detailed information about a word from the dictionary API"""
        try:
            response = requests.get(f"{self.dictionary_api_url}{word}")
            if response.status_code == 200:
                return response.json()
            else:
                print(f"API error: {response.status_code}")
                return None
        except Exception as e:
            print(f"Error fetching word details: {e}")
            return None
    
    def get_word_examples(self, word: str) -> List[str]:
        """Get example sentences for a word from the API"""
        details = self.get_word_details_from_api(word)
        if not details:
            return []
        
        examples = []
        try:
            for entry in details:
                for meaning in entry.get('meanings', []):
                    for definition in meaning.get('definitions', []):
                        if 'example' in definition:
                            examples.append(definition['example'])
        except Exception as e:
            print(f"Error parsing examples: {e}")
        
        return examples
    
    def get_word_phonetics(self, word: str) -> List[Dict]:
        """Get phonetic information for a word from the API"""
        details = self.get_word_details_from_api(word)
        if not details:
            return []
        
        phonetics = []
        try:
            for entry in details:
                for phonetic in entry.get('phonetics', []):
                    if 'text' in phonetic or 'audio' in phonetic:
                        phonetics.append(phonetic)
        except Exception as e:
            print(f"Error parsing phonetics: {e}")
        
        return phonetics
    
    def play_pronunciation(self, word: str) -> bool:
        """Play the pronunciation of a word using text-to-speech or API audio"""
        if not AUDIO_SUPPORT:
            print("Audio support not available. Install gtts and playsound packages.")
            return False
        
        # First try to get audio from the API
        phonetics = self.get_word_phonetics(word)
        audio_url = None
        
        for phonetic in phonetics:
            if 'audio' in phonetic and phonetic['audio']:
                audio_url = phonetic['audio']
                break
        
        if audio_url:
            try:
                # Download and play the audio file
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
                temp_file.close()
                
                response = requests.get(audio_url)
                with open(temp_file.name, 'wb') as f:
                    f.write(response.content)
                
                playsound(temp_file.name)
                
                # Clean up
                os.unlink(temp_file.name)
                return True
            except Exception as e:
                print(f"Error playing audio from API: {e}")
        
        # Fallback to text-to-speech
        try:
            tts = gTTS(text=word, lang='en')
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
            temp_file.close()
            
            tts.save(temp_file.name)
            playsound(temp_file.name)
            
            # Clean up
            os.unlink(temp_file.name)
            return True
        except Exception as e:
            print(f"Error with text-to-speech: {e}")
            return False
    
    def search_google(self, word: str):
        """Open a Google search for the word in a web browser"""
        search_url = f"https://www.google.com/search?q=define+{word}"
        webbrowser.open(search_url)
    
    def close(self):
        """Close the database connection"""
        if self.conn:
            self.conn.close()

    def generate_story_for_words(self, words, theme="adventure", characters="", scenario="", model_preference="auto"):
        """Generate a story using either Phi, Gemini, or LM Studio model to teach vocabulary words
        
        Args:
            words: List of vocabulary words to include
            theme: Story theme (e.g., adventure, mystery)
            characters: Optional characters to include
            scenario: Optional scenario setting
            model_preference: Which model to use - "auto", "phi", "gemini", or "lmstudio"
        """
        try:
            # Determine which model to use
            available_models = []
            phi_available = False
            gemini_available = False
            lmstudio_available = False
            
            # Check if Phi API is available
            try:
                phi_response = requests.get("http://localhost:8000/health", timeout=3)
                if phi_response.status_code == 200 and phi_response.json().get("model_ready", False):
                    phi_available = True
                    available_models.append("phi")
                    print("✓ Phi model is available")
                else:
                    print("✗ Phi model is not ready")
            except requests.exceptions.RequestException:
                print("✗ Phi server is not running (localhost:8000)")
                
            # Check if Gemini API is available
            try:
                gemini_response = requests.get("http://localhost:8001/health", timeout=3)
                if gemini_response.status_code == 200 and gemini_response.json().get("model_ready", False):
                    gemini_available = True
                    available_models.append("gemini")
                    print("✓ Gemini model is available")
                else:
                    print("✗ Gemini model is not ready")
            except requests.exceptions.RequestException:
                print("✗ Gemini server is not running (localhost:8001)")
            
            # Check if LM Studio API is available
            try:
                lmstudio_response = requests.get("http://localhost:9002/health", timeout=3)
                if lmstudio_response.status_code == 200 and lmstudio_response.json().get("model_ready", False):
                    lmstudio_available = True
                    available_models.append("lmstudio")
                    print(f"✓ LM Studio model is available: {lmstudio_response.json().get('model_name', 'Unknown')}")
                else:
                    print("✗ LM Studio model is not ready")
            except requests.exceptions.RequestException:
                print("✗ LM Studio server is not running (localhost:9002)")
            
            # Determine which model to use based on preference and availability
            model_to_use = None
            if model_preference == "auto":
                # In auto mode, prefer models in this order: Gemini, Phi, LM Studio
                if gemini_available:
                    model_to_use = "gemini"
                elif phi_available:
                    model_to_use = "phi"
                elif lmstudio_available:
                    model_to_use = "lmstudio"
            elif model_preference == "phi" and phi_available:
                model_to_use = "phi"
            elif model_preference == "gemini" and gemini_available:
                model_to_use = "gemini"
            elif model_preference == "lmstudio" and lmstudio_available:
                model_to_use = "lmstudio"
            
            if not model_to_use:
                if not available_models:
                    print("No AI models available. Using simple template-based story generator.")
                    return self.generate_simple_story(words, theme)
                else:
                    # Fall back to any available model
                    model_to_use = available_models[0]
                    print(f"⚠ Your preferred model is not available. Using {model_to_use} instead.")
            
            # Get word definitions if available
            words_with_defs = []
            for word in words:
                word_info = self.search_word(word)
                if word_info:
                    words_with_defs.append(f"{word} (definition: {word_info[1]})")
                else:
                    words_with_defs.append(word)
            
            # Prepare the request payload
            payload = {
                "words": words,
                "theme": theme,
                "characters": characters,
                "scenario": scenario,
                "max_new_tokens": 1024,
                "temperature": 0.8
            }
            
            headers = {
                "Content-Type": "application/json"
            }
            
            # Select the appropriate API endpoint
            if model_to_use == "phi":
                url = "http://localhost:8000/generate-story"
                print(f"Using Phi model to generate story...")
            elif model_to_use == "gemini":
                url = "http://localhost:8001/generate-story"
                print(f"Using Gemini model to generate story...")
            else:  # lmstudio
                url = "http://localhost:9002/generate-story"
                print(f"Using LM Studio model to generate story...")
            
            # Make the request to the selected API with a longer timeout
            try:
                response = requests.post(url, data=json.dumps(payload), headers=headers, timeout=60)
                
                if response.status_code == 200:
                    result = response.json()
                    return result["story"]
                else:
                    error_msg = f"Error from {model_to_use.capitalize()} API: {response.status_code}\n{response.text}"
                    print(error_msg)
                    print("Falling back to simple template-based story generator.")
                    return self.generate_simple_story(words, theme)
            except requests.exceptions.ConnectionError:
                server_port = "8000" if model_to_use == "phi" else ("8001" if model_to_use == "gemini" else "9002")
                print(f"Connection failed. The {model_to_use.capitalize()} server is not running on localhost:{server_port}.")
                print("Falling back to simple template-based story generator.")
                return self.generate_simple_story(words, theme)
            except requests.exceptions.Timeout:
                print(f"Request timed out. The {model_to_use.capitalize()} server is taking too long to respond.")
                print("Falling back to simple template-based story generator.")
                return self.generate_simple_story(words, theme)
            except Exception as e:
                print(f"Error communicating with {model_to_use.capitalize()} server: {str(e)}")
                print("Falling back to simple template-based story generator.")
                return self.generate_simple_story(words, theme)
        except Exception as e:
            print(f"Error generating story: {e}")
            print("Falling back to simple template-based story generator.")
            return self.generate_simple_story(words, theme)

    def generate_simple_story(self, words, theme="adventure"):
        """Generate a simple template-based story when AI models are not available
        
        This is a fallback method that doesn't require external AI services.
        """
        # Get definitions for the words
        word_defs = []
        for word in words:
            word_info = self.search_word(word)
            if word_info:
                word_defs.append((word, word_info[1]))
            else:
                # If definition not found, use a placeholder
                word_defs.append((word, "no definition available"))
        
        # Create a simple story template
        if theme.lower() == "adventure":
            story_template = """# The Unexpected Journey

Once upon a time, a group of friends embarked on an exciting adventure. 
{first_sentence}

As they traveled through the dense forest, they had to **{word1}** carefully over fallen logs and streams.
"{word1} means to move with careful steps," explained the guide.

The journey was not easy. Sometimes they felt **{word2}** by the challenges they faced.
"To feel {word2} means {def2}," said one of the friends with a smile.

{middle_sentence}

When they finally reached their destination, they were filled with a sense of **{word3}**.
The guide nodded, "Yes, {word3} refers to {def3}."

{last_sentence}

## Vocabulary Review:
- **{word1}**: {def1}
- **{word2}**: {def2}
- **{word3}**: {def3}
"""
        elif theme.lower() == "mystery":
            story_template = """# The Curious Case

Detective Morgan was known for his ability to solve the most **{word1}** cases in town.
"A {word1} situation is one that {def1}," he would often say.

When a valuable painting disappeared from the museum, Morgan was called to investigate.
The museum curator appeared **{word2}** about the situation.
"To be {word2} means {def2}," Morgan noted in his journal.

{middle_sentence}

As Morgan examined the scene, he found a small **{word3}** near the empty frame.
"This {word3} might be the key to solving this case," he whispered.

{last_sentence}

## Vocabulary Review:
- **{word1}**: {def1}
- **{word2}**: {def2}
- **{word3}**: {def3}
"""
        else:
            # Generic template for any theme
            story_template = """# A {theme} Story

{first_sentence}

The main character encountered something **{word1}** along the way.
This was important because to {word1} means "{def1}."

Later, they felt **{word2}** about the situation they were in.
Being {word2} means {def2}.

{middle_sentence}

In the end, they discovered the power of **{word3}** within themselves.
The concept of {word3} refers to {def3}.

{last_sentence}

## Vocabulary Review:
- **{word1}**: {def1}
- **{word2}**: {def2}
- **{word3}**: {def3}
"""
        
        # Fill in the template with the words and definitions
        import random
        
        # Use up to 3 words or all available words
        used_words = word_defs[:min(3, len(word_defs))]
        
        # If we have fewer than 3 words, duplicate some to fill the template
        while len(used_words) < 3:
            used_words.append(random.choice(used_words))
        
        # Create some simple connecting sentences
        first_sentences = [
            f"Their goal was to discover the meaning of **{used_words[0][0]}**.",
            f"They were on a quest to understand what it means to be **{used_words[0][0]}**.",
            f"The journey began when someone mentioned the word **{used_words[0][0]}**."
        ]
        
        middle_sentences = [
            f"Along the way, they learned about **{random.choice([w for w, _ in used_words])}** and its importance.",
            f"The experience taught them the value of being **{random.choice([w for w, _ in used_words])}**.",
            f"They discussed how **{random.choice([w for w, _ in used_words])}** relates to everyday life."
        ]
        
        last_sentences = [
            f"The adventure helped them understand all these words better, especially **{used_words[-1][0]}**.",
            f"They returned home with a deeper appreciation for language, particularly words like **{used_words[-1][0]}**.",
            f"This experience showed them how words like **{used_words[-1][0]}** can shape our understanding of the world."
        ]
        
        # Fill in the template
        story = story_template.format(
            theme=theme,
            word1=used_words[0][0],
            def1=used_words[0][1],
            word2=used_words[1][0],
            def2=used_words[1][1],
            word3=used_words[2][0],
            def3=used_words[2][1],
            first_sentence=random.choice(first_sentences),
            middle_sentence=random.choice(middle_sentences),
            last_sentence=random.choice(last_sentences)
        )
        
        return story


class LearningSession:
    """Class to manage a vocabulary learning session"""
    
    def __init__(self, search_engine: SearchEngine, word_count: int = 10):
        self.search_engine = search_engine
        self.word_count = word_count
        self.current_words = []
        self.current_index = 0
        self.start_time = None
        self.end_time = None
    
    def start_session(self):
        """Start a new learning session"""
        self.current_words = self.search_engine.get_learning_words(self.word_count)
        self.current_index = 0
        self.start_time = datetime.now()
        return len(self.current_words) > 0
    
    def get_current_word(self):
        """Get the current word in the session"""
        if 0 <= self.current_index < len(self.current_words):
            return self.current_words[self.current_index]
        return None
    
    def mark_current_as_learned(self):
        """Mark the current word as learned"""
        current = self.get_current_word()
        if current:
            self.search_engine.mark_word_as_learned(current[0])
    
    def mark_current_as_unlearned(self):
        """Mark the current word as unlearned"""
        current = self.get_current_word()
        if current:
            self.search_engine.mark_word_as_unlearned(current[0])
    
    def next_word(self):
        """Move to the next word in the session"""
        if self.current_index < len(self.current_words) - 1:
            self.current_index += 1
            return True
        return False
    
    def previous_word(self):
        """Move to the previous word in the session"""
        if self.current_index > 0:
            self.current_index -= 1
            return True
        return False
    
    def end_session(self):
        """End the current learning session"""
        self.end_time = datetime.now()
        duration = self.end_time - self.start_time
        return {
            'words_reviewed': self.current_index + 1,
            'total_words': len(self.current_words),
            'duration_seconds': duration.total_seconds()
        }


# Usage example
if __name__ == "__main__":
    search_engine = SearchEngine("gre_words.db")
    
    print("GRE Vocabulary Learning Tool")
    print("===========================")
    
    # Check if database is empty
    all_words = search_engine.get_all_words()
    if not all_words:
        print("Loading CSV file...")
        search_engine.load_csv("gre_master_wordlist.csv")
        print("CSV file loaded successfully!")
    
    while True:
        print("\nMain Menu:")
        print("1. Search for a word")
        print("2. Start a learning session")
        print("3. View learned words")
        print("4. View remaining words")
        print("5. Library management")
        print("6. Learn by Story")
        print("7. Exit")
        
        choice = input("Enter your choice (1-7): ")
        
        if choice == '1':
            word = input("Enter word to search: ")
            result = search_engine.search_word(word)
            
            if result:
                word, definition, source, learned = result
                print(f"\nWord: {word}")
                print(f"Definition: {definition}")
                print(f"Source: {source}")
                print(f"Learned: {'Yes' if learned else 'No'}")
                
                # Get additional information from API
                print("\nFetching additional information...")
                examples = search_engine.get_word_examples(word)
                if examples:
                    print("\nExample sentences:")
                    for i, example in enumerate(examples[:3], 1):
                        print(f"{i}. {example}")
                
                # Pronunciation options
                if AUDIO_SUPPORT:
                    play_audio = input("\nWould you like to hear the pronunciation? (y/n): ")
                    if play_audio.lower() == 'y':
                        search_engine.play_pronunciation(word)
                
                # Google search option
                google_search = input("\nWould you like to search Google for more information? (y/n): ")
                if google_search.lower() == 'y':
                    search_engine.search_google(word)
                
                # Mark as learned option
                if not learned:
                    mark_learned = input("\nWould you like to mark this word as learned? (y/n): ")
                    if mark_learned.lower() == 'y':
                        search_engine.mark_word_as_learned(word)
                        print("Word marked as learned!")
            else:
                print(f"Word not found: {word}")
                add_word = input("Would you like to add this word to the database? (y/n): ")
                if add_word.lower() == 'y':
                    definition = input("Enter definition: ")
                    source = input("Enter source (optional): ")
                    search_engine.insert_word(word, definition, source if source else None)
                    print(f"Word '{word}' added to the database!")
        
        elif choice == '2':
            word_count = input("How many words would you like to learn? (default: 10): ")
            try:
                word_count = int(word_count)
            except ValueError:
                word_count = 10
            
            session = LearningSession(search_engine, word_count)
            if session.start_session():
                print(f"\nStarting learning session with {len(session.current_words)} words")
                
                while True:
                    current = session.get_current_word()
                    if not current:
                        break
                    
                    word, definition, source, learned = current
                    print(f"\nWord {session.current_index + 1}/{len(session.current_words)}")
                    print(f"Word: {word}")
                    
                    show_def = input("Press Enter to show definition or 'q' to quit: ")
                    if show_def.lower() == 'q':
                        break
                    
                    # Display the definition and source
                    print(f"Definition: {definition}")
                    if source:
                        print(f"Source: {source}")
                    
                    # Add options to mark as learned/unlearned
                    print("\nOptions:")
                    print("  Enter: next word")
                    print("  l: mark as learned and next word")
                    print("  ul: mark as unlearned and next word")
                    print("  q: quit session")
                    
                    action = input("Your choice: ")
                    
                    if action.lower() == 'q':
                        break
                    elif action.lower() == 'l':
                        session.mark_current_as_learned()
                        print("Word marked as learned!")
                        if not session.next_word():
                            print("\nEnd of session!")
                            break
                    elif action.lower() == 'ul':
                        session.mark_current_as_unlearned()
                        print("Word marked as unlearned!")
                        if not session.next_word():
                            print("\nEnd of session!")
                            break
                    else:
                        # Default action: just move to next word
                        if not session.next_word():
                            print("\nEnd of session!")
                            break
        
        elif choice == '3':
            # View learned words
            learned_words = search_engine.get_learned_words()
            if learned_words:
                print(f"\nLearned Words ({len(learned_words)}):")
                print("-" * 50)
                for i, (word, definition, source, _) in enumerate(learned_words, 1):
                    print(f"{i}. {word}: {definition}")
                print("-" * 50)
                
                # Option to view more details or mark as unlearned
                action = input("\nEnter word number to see details, 'u' followed by number to mark as unlearned, or Enter to return: ")
                if action and action.isdigit() and 1 <= int(action) <= len(learned_words):
                    idx = int(action) - 1
                    word = learned_words[idx][0]
                    print(f"\nWord: {word}")
                    print(f"Definition: {learned_words[idx][1]}")
                    if learned_words[idx][2]:
                        print(f"Source: {learned_words[idx][2]}")
                elif action and action.startswith('u') and action[1:].isdigit():
                    idx = int(action[1:]) - 1
                    if 0 <= idx < len(learned_words):
                        word = learned_words[idx][0]
                        search_engine.mark_word_as_unlearned(word)
                        print(f"Word '{word}' marked as unlearned.")
            else:
                print("\nNo learned words yet.")
                
        elif choice == '4':
            # View remaining words
            remaining_words = search_engine.get_remaining_words()
            if remaining_words:
                print(f"\nRemaining Words ({len(remaining_words)}):")
                print("-" * 50)
                for i, (word, definition, source, _) in enumerate(remaining_words[:20], 1):
                    print(f"{i}. {word}: {definition}")
                
                if len(remaining_words) > 20:
                    print(f"... and {len(remaining_words) - 20} more words")
                print("-" * 50)
                
                # Option to view more details or mark as learned
                action = input("\nEnter word number to see details, 'l' followed by number to mark as learned, or Enter to return: ")
                if action and action.isdigit() and 1 <= int(action) <= min(20, len(remaining_words)):
                    idx = int(action) - 1
                    word = remaining_words[idx][0]
                    print(f"\nWord: {word}")
                    print(f"Definition: {remaining_words[idx][1]}")
                    if remaining_words[idx][2]:
                        print(f"Source: {remaining_words[idx][2]}")
                elif action and action.startswith('l') and action[1:].isdigit():
                    idx = int(action[1:]) - 1
                    if 0 <= idx < min(20, len(remaining_words)):
                        word = remaining_words[idx][0]
                        search_engine.mark_word_as_learned(word)
                        print(f"Word '{word}' marked as learned.")
            else:
                print("\nNo remaining words. All words have been learned!")
                
        elif choice == '5':
            # Library management
            while True:
                print("\nLibrary Management:")
                print("1. View all libraries")
                print("2. Create new library")
                print("3. Import library from CSV")
                print("4. Export library to CSV")
                print("5. Return to main menu")
                
                lib_choice = input("Enter your choice (1-5): ")
                
                if lib_choice == '1':
                    libraries = search_engine.get_libraries()
                    if libraries:
                        print("\nAvailable Libraries:")
                        print("-" * 50)
                        for i, (lib_id, name, desc, date) in enumerate(libraries, 1):
                            print(f"{i}. {name} - {desc}")
                        print("-" * 50)
                        
                        lib_action = input("\nEnter library number to view words, or Enter to return: ")
                        if lib_action and lib_action.isdigit() and 1 <= int(lib_action) <= len(libraries):
                            lib_idx = int(lib_action) - 1
                            lib_id = libraries[lib_idx][0]
                            lib_name = libraries[lib_idx][1]
                            
                            lib_words = search_engine.get_library_words(lib_id)
                            print(f"\nWords in '{lib_name}' ({len(lib_words)}):")
                            print("-" * 50)
                            for i, (word, definition, source, learned) in enumerate(lib_words[:20], 1):
                                status = "[Learned]" if learned else "[Not Learned]"
                                print(f"{i}. {word} {status}: {definition}")
                            
                            if len(lib_words) > 20:
                                print(f"... and {len(lib_words) - 20} more words")
                            print("-" * 50)
                    else:
                        print("\nNo libraries available.")
                        
                elif lib_choice == '2':
                    name = input("Enter library name: ")
                    desc = input("Enter library description: ")
                    lib_id = search_engine.create_library(name, desc)
                    if lib_id:
                        print(f"\nLibrary '{name}' created successfully!")
                    else:
                        print("\nFailed to create library.")
                        
                elif lib_choice == '3':
                    name = input("Enter library name: ")
                    desc = input("Enter library description: ")
                    filename = input("Enter CSV file path: ")
                    
                    if os.path.exists(filename):
                        lib_id = search_engine.import_library(name, desc, filename)
                        if lib_id:
                            print(f"\nLibrary '{name}' imported successfully!")
                        else:
                            print("\nFailed to import library.")
                    else:
                        print(f"\nFile '{filename}' not found.")
                        
                elif lib_choice == '4':
                    libraries = search_engine.get_libraries()
                    if libraries:
                        print("\nAvailable Libraries:")
                        print("-" * 50)
                        for i, (lib_id, name, desc, date) in enumerate(libraries, 1):
                            print(f"{i}. {name} - {desc}")
                        print("-" * 50)
                        
                        lib_action = input("\nEnter library number to export, or Enter to return: ")
                        if lib_action and lib_action.isdigit() and 1 <= int(lib_action) <= len(libraries):
                            lib_idx = int(lib_action) - 1
                            lib_id = libraries[lib_idx][0]
                            lib_name = libraries[lib_idx][1]
                            
                            filename = input(f"Enter export filename for '{lib_name}' (default: {lib_name}.csv): ")
                            if not filename:
                                filename = f"{lib_name}.csv"
                                
                            if search_engine.export_library(lib_id, filename):
                                print(f"\nLibrary exported to '{filename}' successfully!")
                            else:
                                print("\nFailed to export library.")
                    else:
                        print("\nNo libraries available.")
                        
                elif lib_choice == '5':
                    break
                    
        elif choice == '6':
            # Learn by Story
            print("\nLearn by Story")
            print("===============")
            
            # Get words for the story
            word_source = input("Select words from: (1) Unlearned words, (2) Library, (3) Custom list: ")
            
            words_to_use = []
            if word_source == '1':
                # Get unlearned words
                count = input("How many unlearned words to include? (default: 5): ")
                try:
                    count = int(count)
                except ValueError:
                    count = 5
                
                remaining = search_engine.get_remaining_words()
                if remaining:
                    # Select random words
                    import random
                    if len(remaining) > count:
                        selected = random.sample(remaining, count)
                    else:
                        selected = remaining
                    
                    words_to_use = [word[0] for word in selected]
                    print(f"Selected words: {', '.join(words_to_use)}")
                else:
                    print("No unlearned words available.")
                    continue
                    
            elif word_source == '2':
                # Get words from a library
                libraries = search_engine.get_libraries()
                if libraries:
                    print("\nAvailable Libraries:")
                    for i, (lib_id, name, desc, _) in enumerate(libraries, 1):
                        print(f"{i}. {name} - {desc}")
                    
                    lib_choice = input("Select library number: ")
                    if lib_choice.isdigit() and 1 <= int(lib_choice) <= len(libraries):
                        lib_idx = int(lib_choice) - 1
                        lib_id = libraries[lib_idx][0]
                        
                        lib_words = search_engine.get_library_words(lib_id)
                        if lib_words:
                            count = input("How many words to include? (default: 5): ")
                            try:
                                count = int(count)
                            except ValueError:
                                count = 5
                            
                            # Select random words
                            import random
                            if len(lib_words) > count:
                                selected = random.sample(lib_words, count)
                            else:
                                selected = lib_words
                            
                            words_to_use = [word[0] for word in selected]
                            print(f"Selected words: {', '.join(words_to_use)}")
                        else:
                            print("No words in this library.")
                            continue
                    else:
                        print("Invalid library selection.")
                        continue
                else:
                    print("No libraries available.")
                    continue
                    
            elif word_source == '3':
                # Custom list of words
                custom_words = input("Enter words separated by commas: ")
                words_to_use = [word.strip() for word in custom_words.split(',') if word.strip()]
                if not words_to_use:
                    print("No valid words entered.")
                    continue
            else:
                print("Invalid selection.")
                continue
            
            # Get story parameters
            theme = input("Enter story theme (e.g., adventure, mystery, sci-fi): ")
            if not theme:
                theme = "adventure"
                
            characters = input("Enter characters (optional): ")
            scenario = input("Enter scenario (optional): ")
            
            # Ask which model to use
            model_choice = input("Select AI model: (1) Auto-select, (2) Phi, (3) Gemini, (4) LM Studio: ")
            model_preference = "auto"
            if model_choice == "2":
                model_preference = "phi"
            elif model_choice == "3":
                model_preference = "gemini"
            elif model_choice == "4":
                model_preference = "lmstudio"
            
            print("\nGenerating story... This may take a moment.")
            story = search_engine.generate_story_for_words(words_to_use, theme, characters, scenario, model_preference)
            
            print("\n" + "="*50)
            print("YOUR VOCABULARY STORY")
            print("="*50)
            print(story)
            print("="*50)
            
            # Option to save the story
            save_option = input("\nWould you like to save this story? (y/n): ")
            if save_option.lower() == 'y':
                filename = input("Enter filename (default: vocabulary_story.txt): ")
                if not filename:
                    filename = "vocabulary_story.txt"
                
                try:
                    with open(filename, 'w', encoding='utf-8') as file:
                        file.write("VOCABULARY STORY\n")
                        file.write(f"Theme: {theme}\n")
                        file.write(f"Words: {', '.join(words_to_use)}\n\n")
                        file.write(story)
                    print(f"Story saved to {filename}")
                except Exception as e:
                    print(f"Error saving story: {e}")
            
            # Option to mark words as learned
            learn_option = input("\nWould you like to mark these words as learned? (y/n): ")
            if learn_option.lower() == 'y':
                for word in words_to_use:
                    search_engine.mark_word_as_learned(word)
                print(f"Marked {len(words_to_use)} words as learned.")
        
        elif choice == '7':
            print("\nThank you for using the GRE Vocabulary Learning Tool!")
            break
            
        else:
            print("\nInvalid choice. Please enter a number between 1 and 7.")
