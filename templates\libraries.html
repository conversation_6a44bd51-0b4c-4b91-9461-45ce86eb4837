{% extends "base.html" %}

{% block title %}My Libraries{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>My Libraries</h1>
                <div class="btn-group" role="group">
                    <a href="{{ url_for('create_library') }}" class="btn btn-primary">Create New Library</a>
                    <a href="{{ url_for('import_library') }}" class="btn btn-outline-primary">Import from CSV</a>
                </div>
            </div>

            {% if libraries %}
                <div class="row">
                    {% for library in libraries %}
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        {{ library.name if library.name else library[1] if library is iterable and library|length > 1 else 'Unnamed Library' }}
                                    </h5>
                                    <p class="card-text">
                                        {{ library.description if library.description else library[2] if library is iterable and library|length > 2 else 'No description' }}
                                    </p>
                                    
                                    {% set lib_id = library.id if library.id else library[0] if library is iterable and library|length > 0 else 0 %}
                                    {% set stats = get_library_stats(lib_id) %}
                                    
                                    <div class="mb-3">
                                        <small class="text-muted">
                                            {{ stats.total }} words • {{ stats.learned }} learned • {{ stats.percent_complete }}% complete
                                        </small>
                                        <div class="progress mt-1" style="height: 5px;">
                                            <div class="progress-bar" role="progressbar" style="width: {{ stats.percent_complete }}%"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="btn-group w-100" role="group">
                                        <a href="{{ url_for('view_library', library_id=lib_id) }}" class="btn btn-outline-primary btn-sm">View</a>
                                        <a href="{{ url_for('learning_session', library_id=lib_id) }}" class="btn btn-primary btn-sm">Study</a>
                                        {% if current_library_id != lib_id %}
                                            <form method="POST" action="{{ url_for('set_current_library', library_id=lib_id) }}" class="d-inline">
                                                <button type="submit" class="btn btn-outline-success btn-sm">Set Current</button>
                                            </form>
                                        {% else %}
                                            <span class="btn btn-success btn-sm disabled">Current</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-5">
                    <h3 class="text-muted">No Libraries Yet</h3>
                    <p class="text-muted">Create your first library to start organizing your vocabulary words.</p>
                    <div class="mt-3">
                        <a href="{{ url_for('create_library') }}" class="btn btn-primary me-2">Create Library</a>
                        <a href="{{ url_for('import_library') }}" class="btn btn-outline-primary">Import from CSV</a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
