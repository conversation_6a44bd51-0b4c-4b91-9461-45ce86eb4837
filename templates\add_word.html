{% extends 'base.html' %}

{% block title %}Add New Word{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h2 class="mb-0">Add New Word</h2>
    </div>
    <div class="card-body">
        <form method="post" action="{{ url_for('add_word') }}">
            <div class="mb-3">
                <label for="word" class="form-label">Word</label>
                <input type="text" class="form-control" id="word" name="word" value="{{ request.args.get('word', '') }}" required>
            </div>
            <div class="mb-3">
                <label for="definition" class="form-label">Definition</label>
                <textarea class="form-control" id="definition" name="definition" rows="3" required></textarea>
            </div>
            <div class="mb-3">
                <label for="source" class="form-label">Source (Optional)</label>
                <input type="text" class="form-control" id="source" name="source">
                <div class="form-text">Where did you find this word? (e.g., GRE prep book, novel)</div>
            </div>
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{{ url_for('search') }}" class="btn btn-outline-secondary">Cancel</a>
                <button type="submit" class="btn btn-primary">Add Word</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}