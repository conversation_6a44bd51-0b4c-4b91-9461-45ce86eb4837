{% extends "base.html" %}

{% block title %}Register - GRE Vocabulary Learner{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">Create an Account</h3>
                </div>
                <div class="card-body">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category }}">{{ message }}</div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    
                    <form method="post" action="{{ url_for('auth.register') }}">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control {% if errors.get('username') %}is-invalid{% endif %}" 
                                       id="username" name="username" value="{{ form.username }}" required>
                                {% if errors.get('username') %}
                                    <div class="invalid-feedback">{{ errors.get('username') }}</div>
                                {% endif %}
                                <div class="form-text">Choose a unique username (3-20 characters)</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control {% if errors.get('email') %}is-invalid{% endif %}" 
                                       id="email" name="email" value="{{ form.email }}" required>
                                {% if errors.get('email') %}
                                    <div class="invalid-feedback">{{ errors.get('email') }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name</label>
                                <input type="text" class="form-control {% if errors.get('first_name') %}is-invalid{% endif %}" 
                                       id="first_name" name="first_name" value="{{ form.first_name }}">
                                {% if errors.get('first_name') %}
                                    <div class="invalid-feedback">{{ errors.get('first_name') }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name</label>
                                <input type="text" class="form-control {% if errors.get('last_name') %}is-invalid{% endif %}" 
                                       id="last_name" name="last_name" value="{{ form.last_name }}">
                                {% if errors.get('last_name') %}
                                    <div class="invalid-feedback">{{ errors.get('last_name') }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control {% if errors.get('password') %}is-invalid{% endif %}" 
                                       id="password" name="password" required>
                                {% if errors.get('password') %}
                                    <div class="invalid-feedback">{{ errors.get('password') }}</div>
                                {% endif %}
                                <div class="form-text">Minimum 6 characters</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">Confirm Password</label>
                                <input type="password" class="form-control {% if errors.get('confirm_password') %}is-invalid{% endif %}" 
                                       id="confirm_password" name="confirm_password" required>
                                {% if errors.get('confirm_password') %}
                                    <div class="invalid-feedback">{{ errors.get('confirm_password') }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input {% if errors.get('accept_terms') %}is-invalid{% endif %}" 
                                   id="accept_terms" name="accept_terms" required>
                            <label class="form-check-label" for="accept_terms">
                                I agree to the <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">Terms and Conditions</a>
                            </label>
                            {% if errors.get('accept_terms') %}
                                <div class="invalid-feedback">{{ errors.get('accept_terms') }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Register</button>
                        </div>
                    </form>
                    
                    <div class="mt-3 text-center">
                        <p>Already have an account? <a href="{{ url_for('auth.login') }}">Login here</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Terms and Conditions Modal -->
<div class="modal fade" id="termsModal" tabindex="-1" aria-labelledby="termsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="termsModalLabel">Terms and Conditions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h6>1. Acceptance of Terms</h6>
                <p>By registering for an account, you agree to these Terms and Conditions.</p>
                
                <h6>2. User Accounts</h6>
                <p>You are responsible for maintaining the confidentiality of your account information.</p>
                
                <h6>3. Privacy Policy</h6>
                <p>Your use of this service is also governed by our Privacy Policy.</p>
                
                <h6>4. Content</h6>
                <p>You are responsible for any content you add to the platform.</p>
                
                <h6>5. Termination</h6>
                <p>We reserve the right to terminate accounts that violate these terms.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
