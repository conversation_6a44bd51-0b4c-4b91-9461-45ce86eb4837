{% extends 'base.html' %}

{% block title %}Learning Session{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h2 class="mb-0">Learning Session</h2>
        <div class="progress mt-2">
            <div class="progress-bar" role="progressbar" style="width: {{ (progress.current / progress.total * 100)|int }}%;" 
                 aria-valuenow="{{ progress.current }}" aria-valuemin="0" aria-valuemax="{{ progress.total }}">
                {{ progress.current }} / {{ progress.total }}
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if word %}
        <div class="word-card">
            <h1 class="display-4 text-center mb-4">{{ word[0] }}</h1>
            
            <div class="definition-section" id="definitionSection" style="display: none;">
                <h5>Definition:</h5>
                <p class="lead">{{ word[1] }}</p>
                
                {% if word[2] %}
                <h5>Source:</h5>
                <p>{{ word[2] }}</p>
                {% endif %}
            </div>
            
            <div class="d-grid gap-2 mb-4">
                <button id="showDefinition" class="btn btn-outline-primary btn-lg">
                    <i class="bi bi-eye"></i> Show Definition
                </button>
            </div>
            
            <form method="post" action="{{ url_for('session_view', session_id=session_id) }}">
                <div class="d-flex justify-content-between">
                    <button type="submit" name="action" value="prev" class="btn btn-outline-secondary" 
                            {% if progress.current == 1 %}disabled{% endif %}>
                        <i class="bi bi-arrow-left"></i> Previous
                    </button>
                    
                    <div class="btn-group" role="group">
                        <button type="submit" name="action" value="learned" class="btn btn-success">
                            <i class="bi bi-check-circle"></i> Mark as Learned
                        </button>
                        <button type="submit" name="action" value="unlearned" class="btn btn-warning">
                            <i class="bi bi-x-circle"></i> Mark as Unlearned
                        </button>
                    </div>
                    
                    <button type="submit" name="action" value="next" class="btn btn-primary">
                        Next <i class="bi bi-arrow-right"></i>
                    </button>
                </div>
                
                <div class="d-grid gap-2 mt-4">
                    <button type="submit" name="action" value="end" class="btn btn-outline-danger">
                        <i class="bi bi-box-arrow-right"></i> End Session
                    </button>
                </div>
            </form>
        </div>
        {% else %}
        <div class="alert alert-warning">
            No words available for this session.
        </div>
        <div class="d-grid gap-2">
            <a href="{{ url_for('learning_session') }}" class="btn btn-primary">
                <i class="bi bi-arrow-left"></i> Back to Session Setup
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const showDefinitionBtn = document.getElementById('showDefinition');
        const definitionSection = document.getElementById('definitionSection');
        
        if (showDefinitionBtn && definitionSection) {
            showDefinitionBtn.addEventListener('click', function() {
                definitionSection.style.display = 'block';
                showDefinitionBtn.style.display = 'none';
            });
        }
    });
</script>
{% endblock %}