{% extends "base.html" %}

{% block title %}{{ library.name }} - Library{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <!-- Library Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1>{{ library.name }}</h1>
                    {% if library.description %}
                        <p class="text-muted">{{ library.description }}</p>
                    {% endif %}
                </div>
                <div class="btn-group" role="group">
                    {% if current_user.is_authenticated %}
                        {% if current_library_id != library.id %}
                            <a href="{{ url_for('set_current_library', library_id=library.id) }}" class="btn btn-outline-primary">
                                Set as Current Library
                            </a>
                        {% else %}
                            <span class="btn btn-success disabled">Current Library</span>
                        {% endif %}
                    {% endif %}
                    <a href="{{ url_for('edit_library', library_id=library.id) }}" class="btn btn-outline-secondary">
                        Edit Library
                    </a>
                    <a href="{{ url_for('add_to_library', library_id=library.id) }}" class="btn btn-outline-info">
                        Add Words
                    </a>
                </div>
            </div>

            <!-- Library Statistics -->
            {% if current_user.is_authenticated %}
                {% set stats = get_library_stats(library.id) %}
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">{{ stats.total }}</h5>
                                <p class="card-text">Total Words</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">{{ stats.learned }}</h5>
                                <p class="card-text">Learned</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">{{ stats.remaining }}</h5>
                                <p class="card-text">Remaining</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">{{ stats.percent_complete }}%</h5>
                                <p class="card-text">Complete</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="mb-4">
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: {{ stats.percent_complete }}%" 
                             aria-valuenow="{{ stats.percent_complete }}" aria-valuemin="0" aria-valuemax="100">
                            {{ stats.percent_complete }}%
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="mb-3">
                <div class="btn-group" role="group">
                    <a href="{{ url_for('learning_session', library_id=library.id) }}" class="btn btn-primary">
                        Start Learning Session
                    </a>
                    <a href="{{ url_for('export_words', list_type='library', library_id=library.id) }}" class="btn btn-outline-secondary">
                        Export Words
                    </a>
                    {% if current_user.is_authenticated %}
                        <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteLibraryModal">
                            Delete Library
                        </button>
                    {% endif %}
                </div>
            </div>

            <!-- Words List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Words in this Library ({{ words|length }})</h5>
                </div>
                <div class="card-body">
                    {% if words %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Word</th>
                                        <th>Definition</th>
                                        <th>Source</th>
                                        {% if current_user.is_authenticated %}
                                            <th>Status</th>
                                            <th>Actions</th>
                                        {% endif %}
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for word in words %}
                                        <tr>
                                            <td>
                                                <strong>{{ word.word }}</strong>
                                            </td>
                                            <td>{{ word.definition }}</td>
                                            <td>
                                                {% if word.source %}
                                                    <small class="text-muted">{{ word.source }}</small>
                                                {% endif %}
                                            </td>
                                            {% if current_user.is_authenticated %}
                                                <td>
                                                    {% if word.learned %}
                                                        <span class="badge bg-success">Learned</span>
                                                    {% else %}
                                                        <span class="badge bg-warning">Learning</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <form method="POST" action="{{ url_for('mark_word') }}" class="d-inline">
                                                        <input type="hidden" name="word" value="{{ word.word }}">
                                                        <input type="hidden" name="redirect_url" value="{{ request.url }}">
                                                        {% if word.learned %}
                                                            <button type="submit" name="action" value="unlearned" class="btn btn-sm btn-outline-warning">
                                                                Mark as Learning
                                                            </button>
                                                        {% else %}
                                                            <button type="submit" name="action" value="learned" class="btn btn-sm btn-outline-success">
                                                                Mark as Learned
                                                            </button>
                                                        {% endif %}
                                                    </form>
                                                </td>
                                            {% endif %}
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <p class="text-muted">No words in this library yet.</p>
                            <a href="{{ url_for('add_to_library', library_id=library.id) }}" class="btn btn-primary">
                                Add Words to Library
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Library Modal -->
{% if current_user.is_authenticated %}
<div class="modal fade" id="deleteLibraryModal" tabindex="-1" aria-labelledby="deleteLibraryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteLibraryModalLabel">Delete Library</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the library "{{ library.name }}"?</p>
                <p class="text-danger"><strong>This action cannot be undone.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" action="{{ url_for('delete_library', library_id=library.id) }}" class="d-inline">
                    <button type="submit" class="btn btn-danger">Delete Library</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}