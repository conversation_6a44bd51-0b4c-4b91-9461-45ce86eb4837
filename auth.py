from flask import Blueprint, render_template, request, redirect, url_for, flash, session, g, jsonify
from werkzeug.security import generate_password_hash, check_password_hash
from models import db, User
from functools import wraps
import os
import re
from datetime import datetime
from werkzeug.utils import secure_filename
from security import (SecurityManager, TwoFactorAuth, csrf_required, rate_limited,
                     account_active_required, get_client_ip)

# Create a blueprint for authentication routes
auth = Blueprint('auth', __name__)

# Enhanced login required decorator
def login_required(view):
    @wraps(view)
    def wrapped_view(**kwargs):
        if g.user is None:
            flash('You need to login to access this page.', 'warning')
            return redirect(url_for('auth.login', next=request.path))
        return account_active_required(view)(**kwargs)
    return wrapped_view

# Enhanced login route with security features
@auth.route('/login', methods=['GET', 'POST'])
@rate_limited(limit=10, window=300)  # 10 attempts per 5 minutes
# @csrf_required  # Temporarily disabled
def login():
    errors = {}

    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        remember_me = request.form.get('remember_me') == 'on'

        # Basic validation
        if not username:
            errors['username'] = 'Username is required'
        if not password:
            errors['password'] = 'Password is required'

        if not errors:
            # Check if user exists
            user = User.query.filter_by(username=username).first()

            if user:
                # Check if account is locked
                if user.is_account_locked():
                    flash('Account is temporarily locked due to failed login attempts. Please try again later.', 'warning')
                    return render_template('auth/login.html', errors=errors)

                # Check password - use new method if available, fallback to old
                password_valid = False
                if hasattr(user, 'check_password'):
                    password_valid = user.check_password(password)
                else:
                    password_valid = check_password_hash(user.password_hash or user.password, password)

                if password_valid:
                    # Reset failed login attempts if user has the method
                    if hasattr(user, 'failed_login_attempts'):
                        user.failed_login_attempts = 0
                        user.locked_until = None

                    # Update last login time
                    user.last_login = datetime.utcnow()
                    db.session.commit()

                    # Check if 2FA is enabled
                    if hasattr(user, 'two_factor_enabled') and user.two_factor_enabled:
                        # Store user ID in session for 2FA verification
                        session['pending_user_id'] = user.id
                        session['pending_login_time'] = datetime.utcnow().isoformat()
                        return redirect(url_for('auth.verify_2fa'))
                    else:
                        # Complete login
                        session.clear()
                        session['user_id'] = user.id
                        session['2fa_verified'] = True  # Mark as verified for non-2FA users

                        if remember_me:
                            session.permanent = True

                        flash('Login successful!', 'success')

                        # Redirect to next page if specified, otherwise to home
                        next_page = request.args.get('next')
                        if next_page and next_page.startswith('/'):  # Security: only allow relative URLs
                            return redirect(next_page)
                        return redirect(url_for('index'))
                else:
                    # Increment failed login attempts if user has the method
                    if hasattr(user, 'increment_failed_login'):
                        user.increment_failed_login()
                    flash('Invalid username or password', 'danger')
            else:
                flash('Invalid username or password', 'danger')

    return render_template('auth/login.html', errors=errors)

# Register route
@auth.route('/register', methods=['GET', 'POST'])
def register():
    errors = {}
    form = {}

    if request.method == 'POST':
        # Get form data
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        accept_terms = 'accept_terms' in request.form

        # Save form data for redisplay if there are errors
        form = {
            'username': username,
            'email': email,
            'first_name': first_name,
            'last_name': last_name
        }

        # Validate input
        if not username or len(username) < 3 or len(username) > 20:
            errors['username'] = 'Username must be between 3 and 20 characters'
        elif User.query.filter_by(username=username).first():
            errors['username'] = 'Username already exists'

        if not email or '@' not in email:
            errors['email'] = 'Valid email is required'
        elif User.query.filter_by(email=email).first():
            errors['email'] = 'Email already registered'

        if not password or len(password) < 6:
            errors['password'] = 'Password must be at least 6 characters'

        if password != confirm_password:
            errors['confirm_password'] = 'Passwords do not match'

        if not accept_terms:
            errors['accept_terms'] = 'You must accept the terms and conditions'

        # If no errors, create user
        if not errors:
            new_user = User(
                username=username,
                email=email,
                first_name=first_name,
                last_name=last_name,
                created_at=datetime.utcnow()
            )

            # Use enhanced password setting if available
            if hasattr(new_user, 'set_password'):
                new_user.set_password(password)
            else:
                new_user.password_hash = generate_password_hash(password)

            db.session.add(new_user)
            db.session.commit()

            flash('Registration successful! You can now login.', 'success')
            return redirect(url_for('auth.login'))

    return render_template('auth/register.html', errors=errors, form=form)

# Logout route
@auth.route('/logout')
def logout():
    session.clear()
    flash('You have been logged out.', 'info')
    return redirect(url_for('index'))

# Profile route
@auth.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    errors = {}
    user = g.user

    if request.method == 'POST':
        # Get form data
        email = request.form.get('email')
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        profile_picture = request.files.get('profile_picture')

        # Validate input
        if not email or '@' not in email:
            errors['email'] = 'Valid email is required'
        elif email != user.email and User.query.filter_by(email=email).first():
            errors['email'] = 'Email already registered'

        # If no errors, update user
        if not errors:
            user.email = email
            user.first_name = first_name
            user.last_name = last_name

            # Handle profile picture upload
            if profile_picture and profile_picture.filename:
                filename = secure_filename(profile_picture.filename)
                # Create a unique filename
                unique_filename = f"{user.id}_{datetime.now().strftime('%Y%m%d%H%M%S')}_{filename}"

                # Ensure the upload directory exists
                upload_dir = os.path.join('static', 'uploads', 'profile_pictures')
                os.makedirs(upload_dir, exist_ok=True)

                # Save the file
                file_path = os.path.join(upload_dir, unique_filename)
                profile_picture.save(file_path)

                # Update user's profile picture path
                user.profile_picture = os.path.join('uploads', 'profile_pictures', unique_filename)

            db.session.commit()
            flash('Profile updated successfully!', 'success')
            return redirect(url_for('auth.profile'))

    # Get user statistics
    stats = {
        'total_words': 0,  # Replace with actual stats
        'learned_words': 0,  # Replace with actual stats
        'sessions': 0  # Replace with actual stats
    }

    return render_template('auth/profile.html', user=user, stats=stats, errors=errors)

# Change password route
@auth.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    errors = {}

    if request.method == 'POST':
        current_password = request.form.get('current_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')

        # Validate input
        if not current_password:
            errors['current_password'] = 'Current password is required'
        elif not check_password_hash(g.user.password_hash, current_password):
            errors['current_password'] = 'Current password is incorrect'

        if not new_password or len(new_password) < 6:
            errors['new_password'] = 'New password must be at least 6 characters'

        if new_password != confirm_password:
            errors['confirm_password'] = 'Passwords do not match'

        # If no errors, update password
        if not errors:
            g.user.password_hash = generate_password_hash(new_password)
            db.session.commit()

            flash('Password changed successfully!', 'success')
            return redirect(url_for('auth.profile'))

    return render_template('auth/change_password.html', errors=errors)

# Two-Factor Authentication routes
@auth.route('/verify-2fa', methods=['GET', 'POST'])
def verify_2fa():
    """Verify 2FA token during login"""
    if 'pending_user_id' not in session:
        flash('No pending login found.', 'warning')
        return redirect(url_for('auth.login'))

    user = User.query.get(session['pending_user_id'])
    if not user or not user.two_factor_enabled:
        session.pop('pending_user_id', None)
        flash('Invalid 2FA setup.', 'danger')
        return redirect(url_for('auth.login'))

    errors = {}

    if request.method == 'POST':
        token = request.form.get('token', '').strip()
        backup_code = request.form.get('backup_code', '').strip()

        verified = False

        if token:
            # Verify TOTP token
            if user.verify_totp(token):
                verified = True
        elif backup_code:
            # Verify backup code
            if user.verify_backup_code(backup_code):
                verified = True
                flash('Backup code used. Please generate new backup codes.', 'warning')

        if verified:
            # Complete login
            session.clear()
            session['user_id'] = user.id
            session['2fa_verified'] = True

            # Update last login
            user.last_login = datetime.utcnow()
            db.session.commit()

            flash('Login successful!', 'success')
            return redirect(url_for('index'))
        else:
            errors['token'] = 'Invalid verification code'

    return render_template('auth/verify_2fa.html', errors=errors)

@auth.route('/setup-2fa', methods=['GET', 'POST'])
@login_required
def setup_2fa():
    """Setup Two-Factor Authentication"""
    user = g.user

    if request.method == 'POST':
        action = request.form.get('action')

        if action == 'enable':
            token = request.form.get('token', '').strip()

            if not token:
                flash('Please enter the verification code.', 'danger')
                return render_template('auth/setup_2fa.html', user=user)

            # Generate secret if not exists
            if not user.totp_secret:
                user.generate_totp_secret()

            # Verify token
            if user.verify_totp(token):
                user.two_factor_enabled = True
                backup_codes = user.generate_backup_codes()
                db.session.commit()

                flash('Two-Factor Authentication enabled successfully!', 'success')
                return render_template('auth/backup_codes.html', backup_codes=backup_codes)
            else:
                flash('Invalid verification code. Please try again.', 'danger')

        elif action == 'disable':
            password = request.form.get('password', '')

            if not password:
                flash('Password is required to disable 2FA.', 'danger')
            elif not user.check_password(password):
                flash('Incorrect password.', 'danger')
            else:
                user.two_factor_enabled = False
                user.totp_secret = None
                user.backup_codes = None
                db.session.commit()

                flash('Two-Factor Authentication disabled.', 'info')
                return redirect(url_for('auth.profile'))

    # Generate QR code for setup
    qr_code_url = None
    if not user.two_factor_enabled:
        if not user.totp_secret:
            user.generate_totp_secret()
        qr_code_url = TwoFactorAuth.generate_qr_code(user)

    return render_template('auth/setup_2fa.html', user=user, qr_code_url=qr_code_url)

@auth.route('/backup-codes')
@login_required
def backup_codes():
    """View backup codes"""
    user = g.user

    if not user.two_factor_enabled:
        flash('Two-Factor Authentication is not enabled.', 'warning')
        return redirect(url_for('auth.profile'))

    # Generate new backup codes
    codes = user.generate_backup_codes()

    return render_template('auth/backup_codes.html', backup_codes=codes)


