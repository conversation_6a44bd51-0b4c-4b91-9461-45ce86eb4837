#!/usr/bin/env python3
"""
Setup script for GRE Vocabulary Learning App
This script helps set up the application with all necessary dependencies and configurations.
"""

import os
import sys
import subprocess
import secrets
from pathlib import Path

def print_header():
    """Print setup header"""
    print("=" * 60)
    print("GRE Vocabulary Learning App - Enhanced Setup")
    print("=" * 60)
    print()

def check_python_version():
    """Check if Python version is compatible"""
    print("Checking Python version...")
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required!")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("\nInstalling dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    print("\nCreating directories...")
    directories = [
        "instance",
        "uploads",
        "logs",
        "static/uploads"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")

def generate_env_file():
    """Generate .env file with secure defaults"""
    print("\nGenerating environment configuration...")
    
    env_file = Path(".env")
    if env_file.exists():
        response = input("⚠️  .env file already exists. Overwrite? (y/N): ")
        if response.lower() != 'y':
            print("Skipping .env file generation")
            return True
    
    # Generate secure secret key
    secret_key = secrets.token_urlsafe(32)
    
    env_content = f"""# GRE Vocabulary App Configuration
# Generated on {os.popen('date').read().strip()}

# Flask Configuration
FLASK_ENV=development
FLASK_APP=app.py
SECRET_KEY={secret_key}

# Database Configuration
DATABASE_URL=sqlite:///instance/users.db

# Upload Configuration
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216

# Security Configuration
SECURITY_PASSWORD_SALT={secrets.token_urlsafe(16)}
SECURITY_CSRF_TIME_LIMIT=3600
SECURITY_LOGIN_ATTEMPTS_LIMIT=5
SECURITY_LOGIN_LOCKOUT_DURATION=30

# Session Configuration
SESSION_COOKIE_SECURE=False
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax
PERMANENT_SESSION_LIFETIME=3600

# Development Settings (change for production)
DEBUG=True
TESTING=False

# Email Configuration (optional - for future features)
# MAIL_SERVER=smtp.gmail.com
# MAIL_PORT=587
# MAIL_USE_TLS=True
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-app-password

# External API Keys (optional)
# YOUTUBE_API_KEY=your-youtube-api-key
"""
    
    try:
        with open(env_file, 'w') as f:
            f.write(env_content)
        print("✅ Environment file created: .env")
        print("🔑 Secure secret key generated")
        return True
    except Exception as e:
        print(f"❌ Failed to create .env file: {e}")
        return False

def setup_database():
    """Set up the database"""
    print("\nSetting up database...")
    
    try:
        # Run migration script
        result = subprocess.run([sys.executable, "migrate_database.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Database setup completed")
            return True
        else:
            print(f"❌ Database setup failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Database setup error: {e}")
        return False

def create_admin_user():
    """Create initial admin user"""
    print("\nCreating admin user...")
    
    try:
        from app import app, db
        from models import User
        
        with app.app_context():
            # Check if admin user already exists
            admin = User.query.filter_by(username='admin').first()
            if admin:
                print("ℹ️  Admin user already exists")
                return True
            
            # Get admin details
            print("Please enter admin user details:")
            username = input("Username (default: admin): ").strip() or "admin"
            email = input("Email: ").strip()
            
            while not email:
                email = input("Email is required: ").strip()
            
            password = input("Password: ").strip()
            while len(password) < 8:
                password = input("Password must be at least 8 characters: ").strip()
            
            first_name = input("First Name (optional): ").strip()
            last_name = input("Last Name (optional): ").strip()
            
            # Create admin user
            admin_user = User(
                username=username,
                email=email,
                first_name=first_name or "Admin",
                last_name=last_name or "User",
                role='admin',
                is_active=True,
                email_verified=True
            )
            
            if hasattr(admin_user, 'set_password'):
                admin_user.set_password(password)
            else:
                from werkzeug.security import generate_password_hash
                admin_user.password_hash = generate_password_hash(password)
            
            db.session.add(admin_user)
            db.session.commit()
            
            print(f"✅ Admin user '{username}' created successfully")
            return True
            
    except Exception as e:
        print(f"❌ Failed to create admin user: {e}")
        return False

def print_next_steps():
    """Print next steps for the user"""
    print("\n" + "=" * 60)
    print("🎉 Setup completed successfully!")
    print("=" * 60)
    print()
    print("Next steps:")
    print("1. Review the .env file and adjust settings as needed")
    print("2. For production deployment:")
    print("   - Set FLASK_ENV=production")
    print("   - Set SESSION_COOKIE_SECURE=True")
    print("   - Use a proper database (PostgreSQL/MySQL)")
    print("   - Set up proper logging")
    print()
    print("3. Start the application:")
    print("   python app.py")
    print()
    print("4. Access the application:")
    print("   http://localhost:5000")
    print()
    print("5. Login with your admin account and explore the new features:")
    print("   - Enhanced security with 2FA")
    print("   - Study room with timer")
    print("   - Improved CSV upload")
    print("   - Task management")
    print()
    print("📚 For detailed information, see IMPROVEMENTS.md")
    print()

def main():
    """Main setup function"""
    print_header()
    
    # Check prerequisites
    if not check_python_version():
        return 1
    
    # Install dependencies
    if not install_dependencies():
        return 1
    
    # Create directories
    create_directories()
    
    # Generate environment file
    if not generate_env_file():
        return 1
    
    # Setup database
    if not setup_database():
        return 1
    
    # Create admin user
    create_admin = input("\nWould you like to create an admin user now? (Y/n): ")
    if create_admin.lower() != 'n':
        create_admin_user()
    
    # Print next steps
    print_next_steps()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
