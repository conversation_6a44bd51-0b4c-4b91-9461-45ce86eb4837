{% extends "base.html" %}

{% block title %}Create Library{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h2>Create New Library</h2>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('create_library') }}">
                        <div class="mb-3">
                            <label for="name" class="form-label">Library Name</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                            <div class="form-text">Choose a descriptive name for your library.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description (Optional)</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                            <div class="form-text">Add a brief description of this library's purpose or content.</div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('libraries') }}" class="btn btn-outline-secondary me-md-2">Cancel</a>
                            <button type="submit" class="btn btn-primary">Create Library</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
