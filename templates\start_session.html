{% extends 'base.html' %}

{% block title %}Start Learning Session{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h1 class="mb-0">
                <i class="bi bi-play-circle"></i> Start Learning Session
            </h1>
        </div>
        <div class="card-body">
            <p class="lead">
                Configure your learning session to focus on the words you want to learn.
            </p>
            
            <form method="post" action="{{ url_for('learning_session') }}">
                <div class="mb-4">
                    <h4>1. Select Words</h4>
                    <div class="card">
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Word Source</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="word_source" id="sourceRandom" value="random" {% if not library_id %}checked{% endif %}>
                                    <label class="form-check-label" for="sourceRandom">
                                        Random words
                                    </label>
                                </div>
                                
                                {% if libraries %}
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="word_source" id="sourceLibrary" value="library" {% if library_id %}checked{% endif %}>
                                        <label class="form-check-label" for="sourceLibrary">
                                            Words from a library
                                        </label>
                                    </div>
                                    
                                    <div class="ms-4 mb-3" id="librarySelectContainer" style="display: {% if library_id %}block{% else %}none{% endif %};">
                                        <select class="form-select" name="library_id" id="librarySelect">
                                            <option value="" selected disabled>Select a library</option>
                                            {% for library in libraries %}
                                                <option value="{{ library.id }}" {% if library.id == library_id|int %}selected{% endif %}>
                                                    {{ library.name }} ({{ library.word_count }} words)
                                                </option>
                                            {% endfor %}
                                        </select>
                                        
                                        <div class="form-check mt-2">
                                            <input class="form-check-input" type="checkbox" id="unlearned_only" name="unlearned_only" value="1" checked>
                                            <label class="form-check-label" for="unlearned_only">
                                                Only include words I haven't learned yet
                                            </label>
                                        </div>
                                    </div>
                                {% endif %}
                                
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="word_source" id="sourceCustom" value="custom">
                                    <label class="form-check-label" for="sourceCustom">
                                        Specific words
                                    </label>
                                </div>
                                
                                <div class="ms-4 mb-3" id="customWordsContainer" style="display: none;">
                                    <textarea class="form-control" name="word_list" id="customWordList" rows="3" placeholder="Enter words separated by commas">{% if word %}{{ word }}{% endif %}</textarea>
                                    <div class="form-text">Enter words separated by commas</div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="wordCount" class="form-label">Number of Words</label>
                                <input type="range" class="form-range" min="5" max="50" value="10" id="wordCount" name="word_count" oninput="updateWordCountLabel()">
                                <div class="text-center" id="wordCountLabel">10 words</div>
                                <div class="form-text">For specific words, all entered words will be used regardless of this setting</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mb-4">
                    <h4>2. Session Settings</h4>
                    <div class="card">
                        <div class="card-body">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="showDefinition" name="show_definition" value="1" checked>
                                <label class="form-check-label" for="showDefinition">
                                    Show definitions during session
                                </label>
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="showExamples" name="show_examples" value="1" checked>
                                <label class="form-check-label" for="showExamples">
                                    Show examples during session
                                </label>
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="randomOrder" name="random_order" value="1" checked>
                                <label class="form-check-label" for="randomOrder">
                                    Randomize word order
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="bi bi-play-circle"></i> Start Session
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Update word count label
    function updateWordCountLabel() {
        const count = document.getElementById('wordCount').value;
        document.getElementById('wordCountLabel').textContent = count + ' words';
    }
    
    // Show/hide containers based on radio selection
    document.querySelectorAll('input[name="word_source"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const libraryContainer = document.getElementById('librarySelectContainer');
            const customContainer = document.getElementById('customWordsContainer');
            
            if (this.value === 'library') {
                libraryContainer.style.display = 'block';
                customContainer.style.display = 'none';
            } else if (this.value === 'custom') {
                libraryContainer.style.display = 'none';
                customContainer.style.display = 'block';
            } else {
                libraryContainer.style.display = 'none';
                customContainer.style.display = 'none';
            }
        });
    });
    
    // Initialize with URL parameters if present
    document.addEventListener('DOMContentLoaded', function() {
        const customWordList = document.getElementById('customWordList');
        if (customWordList.value.trim()) {
            document.getElementById('sourceCustom').checked = true;
            document.getElementById('customWordsContainer').style.display = 'block';
        }
        
        updateWordCountLabel();
    });
</script>
{% endblock %}
