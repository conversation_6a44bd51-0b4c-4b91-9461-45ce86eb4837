{% extends 'base.html' %}

{% block title %}{{ word.word }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header bg-primary text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="mb-0">{{ word.word }}</h1>
                <div>
                    <button class="btn btn-light" onclick="playPronunciation()">
                        <i class="bi bi-volume-up"></i> Pronounce
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <h5>Definition</h5>
                    <p>{{ word.definition }}</p>
                    
                    {% if word.example %}
                        <h5>Example</h5>
                        <p><em>{{ word.example }}</em></p>
                    {% endif %}
                    
                    <div class="row mt-4">
                        {% if word.synonyms %}
                            <div class="col-md-6">
                                <h5>Synonyms</h5>
                                <ul class="list-group">
                                    {% for synonym in word.synonyms.split(',') %}
                                        <li class="list-group-item">{{ synonym.strip() }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        {% endif %}
                        
                        {% if word.antonyms %}
                            <div class="col-md-6">
                                <h5>Antonyms</h5>
                                <ul class="list-group">
                                    {% for antonym in word.antonyms.split(',') %}
                                        <li class="list-group-item">{{ antonym.strip() }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card mb-3">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Learning Status</h5>
                        </div>
                        <div class="card-body">
                            {% if progress %}
                                <p>Status: 
                                    {% if progress.status == 'learned' %}
                                        <span class="badge bg-success">Learned</span>
                                    {% elif progress.status == 'learning' %}
                                        <span class="badge bg-warning">Learning</span>
                                    {% else %}
                                        <span class="badge bg-secondary">New</span>
                                    {% endif %}
                                </p>
                                <p>Last reviewed: {{ progress.last_reviewed|datetime }}</p>
                                <p>Review count: {{ progress.review_count }}</p>
                            {% else %}
                                <p>You haven't studied this word yet.</p>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Add to Library</h5>
                        </div>
                        <div class="card-body">
                            {% if libraries %}
                                <form method="post" action="{{ url_for('add_word_to_library', library_id=0, word_id=word.id) }}" id="addToLibraryForm">
                                    <div class="mb-3">
                                        <select class="form-select" id="library_id" name="library_id" required>
                                            <option value="" selected disabled>Select a library</option>
                                            {% for library in libraries %}
                                                <option value="{{ library.id }}">{{ library.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <button type="button" class="btn btn-primary w-100" onclick="addToLibrary()">
                                        <i class="bi bi-plus-circle"></i> Add to Library
                                    </button>
                                </form>
                            {% else %}
                                <p>You don't have any libraries yet.</p>
                                <a href="{{ url_for('create_library') }}" class="btn btn-outline-primary w-100">
                                    <i class="bi bi-plus-circle"></i> Create a Library
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-4">
                <h5>External Resources</h5>
                <div class="d-flex flex-wrap gap-2">
                    <a href="https://www.merriam-webster.com/dictionary/{{ word.word }}" target="_blank" class="btn btn-outline-primary">
                        <i class="bi bi-book"></i> Merriam-Webster
                    </a>
                    <a href="https://www.vocabulary.com/dictionary/{{ word.word }}" target="_blank" class="btn btn-outline-primary">
                        <i class="bi bi-book"></i> Vocabulary.com
                    </a>
                    <a href="https://www.google.com/search?q=define+{{ word.word }}" target="_blank" class="btn btn-outline-primary">
                        <i class="bi bi-google"></i> Google
                    </a>
                    <a href="https://www.thesaurus.com/browse/{{ word.word }}" target="_blank" class="btn btn-outline-primary">
                        <i class="bi bi-book"></i> Thesaurus.com
                    </a>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('search') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Back to Search
                </a>
                
                <div>
                    <a href="{{ url_for('story_generator') }}?words={{ word.word }}" class="btn btn-success">
                        <i class="bi bi-book"></i> Generate Story
                    </a>
                    <a href="{{ url_for('learning_session') }}?word={{ word.word }}" class="btn btn-primary">
                        <i class="bi bi-play-circle"></i> Learn This Word
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function playPronunciation() {
        const word = "{{ word.word }}";
        const utterance = new SpeechSynthesisUtterance(word);
        speechSynthesis.speak(utterance);
    }
    
    function addToLibrary() {
        const librarySelect = document.getElementById('library_id');
        const libraryId = librarySelect.value;
        
        if (!libraryId) {
            alert('Please select a library');
            return;
        }
        
        const form = document.getElementById('addToLibraryForm');
        form.action = "{{ url_for('add_word_to_library', library_id=0, word_id=word.id) }}".replace('0', libraryId);
        form.submit();
    }
</script>
{% endblock %}