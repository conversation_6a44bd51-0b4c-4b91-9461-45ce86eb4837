{% extends 'base.html' %}

{% block title %}Session Complete{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-success text-white">
        <h2 class="mb-0">Session Complete!</h2>
    </div>
    <div class="card-body">
        <div class="text-center mb-4">
            <i class="bi bi-trophy display-1 text-warning"></i>
        </div>
        
        <h4 class="text-center mb-4">Great job! You've completed your learning session.</h4>
        
        <div class="row">
            <div class="col-md-4">
                <div class="card text-center mb-3">
                    <div class="card-body">
                        <h5 class="card-title">Words Reviewed</h5>
                        <p class="display-4">{{ stats.words_reviewed }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center mb-3">
                    <div class="card-body">
                        <h5 class="card-title">Total Words</h5>
                        <p class="display-4">{{ stats.total_words }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center mb-3">
                    <div class="card-body">
                        <h5 class="card-title">Time Spent</h5>
                        <p class="display-4">{{ (stats.duration_seconds / 60)|round|int }} min</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="d-grid gap-2 d-md-flex justify-content-md-center mt-4">
            <a href="{{ url_for('learning_session') }}" class="btn btn-primary btn-lg">
                <i class="bi bi-arrow-repeat"></i> Start New Session
            </a>
            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary btn-lg">
                <i class="bi bi-house"></i> Back to Home
            </a>
        </div>
    </div>
</div>
{% endblock %}