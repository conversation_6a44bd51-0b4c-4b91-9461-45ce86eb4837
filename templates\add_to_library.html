{% extends 'base.html' %}

{% block title %}Add to Library{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h2 class="mb-0">Add Words to "{{ library_name }}"</h2>
    </div>
    <div class="card-body">
        <ul class="nav nav-tabs mb-4" id="addToLibraryTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="csv-tab" data-bs-toggle="tab" data-bs-target="#csv" type="button" role="tab" aria-controls="csv" aria-selected="true">
                    <i class="bi bi-file-earmark-text"></i> Import from CSV
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="library-tab" data-bs-toggle="tab" data-bs-target="#library" type="button" role="tab" aria-controls="library" aria-selected="false">
                    <i class="bi bi-collection"></i> Copy from Another Library
                </button>
            </li>
        </ul>

        <div class="tab-content" id="addToLibraryTabContent">
            <!-- CSV Import Tab -->
            <div class="tab-pane fade show active" id="csv" role="tabpanel" aria-labelledby="csv-tab">
                <p class="card-text">
                    Import words from a CSV file. The file should have the following format:
                </p>
                <pre class="bg-light p-3 mb-4">word,definition,source</pre>

                <form method="post" action="{{ url_for('add_to_library', library_id=library_id) }}" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="file" class="form-label">CSV File</label>
                        <input class="form-control" type="file" id="file" name="file" accept=".csv,.txt" required>
                    </div>
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('view_library', library_id=library_id) }}" class="btn btn-outline-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary">Import Words</button>
                    </div>
                </form>
            </div>

            <!-- Library Copy Tab -->
            <div class="tab-pane fade" id="library" role="tabpanel" aria-labelledby="library-tab">
                <p class="card-text">
                    Copy words from another library to "{{ library_name }}".
                </p>

                <form method="post" action="{{ url_for('add_to_library', library_id=library_id) }}">
                    <div class="mb-3">
                        <label for="source_library_id" class="form-label">Select Source Library</label>

                        {% if libraries %}
                        <select class="form-select" id="source_library_id" name="source_library_id" required>
                            <option value="" selected disabled>Choose a library...</option>
                            {% for library in libraries %}
                            <option value="{{ library['id'] }}">{{ library['name'] }}</option>
                            {% endfor %}
                        </select>
                        {% else %}
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i> No other libraries available.
                        </div>
                        {% endif %}
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('view_library', library_id=library_id) }}" class="btn btn-outline-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary" {% if not libraries %}disabled{% endif %}>Copy Words</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}