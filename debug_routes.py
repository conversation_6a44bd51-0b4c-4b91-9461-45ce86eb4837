#!/usr/bin/env python3
"""
Debug script to check Flask routes registration
"""

from app import app

def list_routes():
    """List all registered routes in the Flask application"""
    print("=== REGISTERED FLASK ROUTES ===")
    print(f"{'Endpoint':<30} {'Methods':<15} {'Rule'}")
    print("-" * 80)
    
    for rule in app.url_map.iter_rules():
        methods = ','.join(sorted(rule.methods - {'HEAD', 'OPTIONS'}))
        print(f"{rule.endpoint:<30} {methods:<15} {rule.rule}")
    
    print("\n=== SEARCHING FOR add_words_to_library ===")
    found = False
    for rule in app.url_map.iter_rules():
        if 'add_words' in rule.endpoint:
            print(f"Found: {rule.endpoint} -> {rule.rule} [{','.join(rule.methods)}]")
            found = True
    
    if not found:
        print("❌ add_words_to_library route NOT FOUND!")
        print("\n=== SIMILAR ROUTES ===")
        for rule in app.url_map.iter_rules():
            if 'add_word' in rule.endpoint or 'library' in rule.endpoint:
                print(f"Similar: {rule.endpoint} -> {rule.rule}")
    else:
        print("✅ add_words_to_library route found!")

if __name__ == "__main__":
    list_routes()
