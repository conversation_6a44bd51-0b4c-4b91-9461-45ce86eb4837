#!/usr/bin/env python3
"""
Test script to verify that all routes are properly registered and working
"""

import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_routes():
    """Test that all routes are properly registered"""
    try:
        from app import app
        
        print("=== TESTING ROUTE REGISTRATION ===")
        print(f"{'Endpoint':<40} {'Methods':<15} {'Rule'}")
        print("-" * 80)
        
        routes = []
        for rule in app.url_map.iter_rules():
            methods = ','.join(sorted(rule.methods - {'HEAD', 'OPTIONS'}))
            routes.append((rule.endpoint, methods, rule.rule))
            print(f"{rule.endpoint:<40} {methods:<15} {rule.rule}")
        
        print(f"\nTotal routes registered: {len(routes)}")
        
        # Check for specific routes that were added/fixed
        required_routes = [
            'story_generator',
            'view_library',
            'set_current_library',
            'add_words_to_library',
            'add_to_library'
        ]
        
        print("\n=== CHECKING REQUIRED ROUTES ===")
        registered_endpoints = [route[0] for route in routes]
        
        for route in required_routes:
            if route in registered_endpoints:
                print(f"✅ {route} - FOUND")
            else:
                print(f"❌ {route} - MISSING")
        
        # Test app creation
        print("\n=== TESTING APP CREATION ===")
        with app.app_context():
            print("✅ App context created successfully")
            
        print("\n=== TESTING IMPORTS ===")
        try:
            from search import SearchEngine
            print("✅ SearchEngine imported successfully")
        except Exception as e:
            print(f"❌ SearchEngine import failed: {e}")
            
        try:
            from models import db, User
            print("✅ Models imported successfully")
        except Exception as e:
            print(f"❌ Models import failed: {e}")
            
        try:
            from auth import auth, login_required
            print("✅ Auth imported successfully")
        except Exception as e:
            print(f"❌ Auth import failed: {e}")
            
        print("\n=== TEST COMPLETED ===")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_routes()
    sys.exit(0 if success else 1)
