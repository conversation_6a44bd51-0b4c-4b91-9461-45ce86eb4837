{% extends 'base.html' %}

{% block title %}Learning Session - {{ word.word }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <!-- Progress Bar -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">Learning Progress</h6>
                        <span class="text-muted">{{ index + 1 }} of {{ total }}</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-primary" role="progressbar" 
                             style="width: {{ ((index + 1) / total * 100)|round(1) }}%"
                             aria-valuenow="{{ index + 1 }}" aria-valuemin="0" aria-valuemax="{{ total }}">
                            {{ ((index + 1) / total * 100)|round(1) }}%
                        </div>
                    </div>
                </div>
            </div>

            <!-- Word Card -->
            <div class="card">
                <div class="card-header bg-primary text-white text-center">
                    <h2 class="mb-0">{{ word.word }}</h2>
                </div>
                <div class="card-body">
                    <!-- Definition Section -->
                    <div class="mb-4">
                        <h5 class="text-primary">Definition</h5>
                        <p class="lead">{{ word.definition }}</p>
                    </div>

                    <!-- Example Section -->
                    {% if word.example %}
                    <div class="mb-4">
                        <h5 class="text-primary">Example</h5>
                        <p class="text-muted"><em>{{ word.example }}</em></p>
                    </div>
                    {% endif %}

                    <!-- Source Section -->
                    {% if word.source %}
                    <div class="mb-4">
                        <h5 class="text-primary">Source</h5>
                        <p class="text-muted">{{ word.source }}</p>
                    </div>
                    {% endif %}

                    <!-- Action Buttons -->
                    <div class="text-center">
                        <form method="post" action="{{ url_for('next_word') }}" class="d-inline">
                            <input type="hidden" name="learned" value="1">
                            <button type="submit" class="btn btn-success btn-lg me-3">
                                <i class="bi bi-check-circle"></i> I Know This Word
                            </button>
                        </form>
                        
                        <form method="post" action="{{ url_for('next_word') }}" class="d-inline">
                            <input type="hidden" name="learned" value="0">
                            <button type="submit" class="btn btn-warning btn-lg">
                                <i class="bi bi-x-circle"></i> Need to Study More
                            </button>
                        </form>
                    </div>

                    <!-- Additional Actions -->
                    <div class="text-center mt-4">
                        <button class="btn btn-outline-primary" onclick="playPronunciation('{{ word.word }}')">
                            <i class="bi bi-volume-up"></i> Pronounce
                        </button>
                        
                        <a href="{{ url_for('google_search', word=word.word) }}" 
                           class="btn btn-outline-info" target="_blank">
                            <i class="bi bi-search"></i> Google Search
                        </a>
                        
                        <a href="{{ url_for('end_session') }}" class="btn btn-outline-danger">
                            <i class="bi bi-stop-circle"></i> End Session
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function playPronunciation(word) {
    fetch(`/play-pronunciation/${encodeURIComponent(word)}`)
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                // Fallback to speech synthesis
                if ('speechSynthesis' in window) {
                    const utterance = new SpeechSynthesisUtterance(word);
                    utterance.rate = 0.8;
                    speechSynthesis.speak(utterance);
                } else {
                    alert('Pronunciation not available');
                }
            }
        })
        .catch(error => {
            console.error('Error playing pronunciation:', error);
            // Fallback to speech synthesis
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(word);
                utterance.rate = 0.8;
                speechSynthesis.speak(utterance);
            }
        });
}

// Keyboard shortcuts
document.addEventListener('keydown', function(event) {
    if (event.key === 'ArrowRight' || event.key === 'Enter') {
        // Mark as known and go to next
        document.querySelector('input[value="1"]').parentElement.submit();
    } else if (event.key === 'ArrowLeft' || event.key === 'Space') {
        // Mark as unknown and go to next
        document.querySelector('input[value="0"]').parentElement.submit();
    } else if (event.key === 'p' || event.key === 'P') {
        // Play pronunciation
        playPronunciation('{{ word.word }}');
    }
});
</script>
{% endblock %}
