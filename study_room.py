"""
Study Room and Timer functionality for the GRE Vocabulary App
"""
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session, g
from models import db, StudySession, Task, User
from auth import login_required
import requests
from urllib.parse import urlparse, parse_qs

study_room = Blueprint('study_room', __name__, url_prefix='/study')

class TimerManager:
    """Manages study timers and sessions"""
    
    @staticmethod
    def create_study_session(user_id: int, title: str, planned_duration: int, 
                           description: str = None, background_audio: str = None,
                           background_theme: str = 'default') -> StudySession:
        """Create a new study session"""
        session = StudySession(
            user_id=user_id,
            title=title,
            description=description,
            planned_duration=planned_duration,
            background_audio=background_audio,
            background_theme=background_theme,
            status='active'
        )
        
        db.session.add(session)
        db.session.commit()
        return session
    
    @staticmethod
    def pause_session(session_id: int) -> bool:
        """Pause a study session"""
        session = StudySession.query.get(session_id)
        if session and session.status == 'active':
            session.status = 'paused'
            db.session.commit()
            return True
        return False
    
    @staticmethod
    def resume_session(session_id: int) -> bool:
        """Resume a paused study session"""
        session = StudySession.query.get(session_id)
        if session and session.status == 'paused':
            session.status = 'active'
            db.session.commit()
            return True
        return False
    
    @staticmethod
    def complete_session(session_id: int, actual_duration: int) -> bool:
        """Complete a study session"""
        session = StudySession.query.get(session_id)
        if session:
            session.status = 'completed'
            session.end_time = datetime.utcnow()
            session.actual_duration = actual_duration
            db.session.commit()
            return True
        return False

class YouTubeAudioManager:
    """Manages YouTube audio integration for background sounds"""
    
    @staticmethod
    def validate_youtube_url(url: str) -> bool:
        """Validate YouTube URL"""
        if not url:
            return False
        
        youtube_patterns = [
            r'(?:https?://)?(?:www\.)?youtube\.com/watch\?v=([a-zA-Z0-9_-]+)',
            r'(?:https?://)?(?:www\.)?youtu\.be/([a-zA-Z0-9_-]+)',
            r'(?:https?://)?(?:www\.)?youtube\.com/embed/([a-zA-Z0-9_-]+)'
        ]
        
        for pattern in youtube_patterns:
            if re.match(pattern, url):
                return True
        return False
    
    @staticmethod
    def extract_video_id(url: str) -> Optional[str]:
        """Extract YouTube video ID from URL"""
        patterns = [
            r'(?:https?://)?(?:www\.)?youtube\.com/watch\?v=([a-zA-Z0-9_-]+)',
            r'(?:https?://)?(?:www\.)?youtu\.be/([a-zA-Z0-9_-]+)',
            r'(?:https?://)?(?:www\.)?youtube\.com/embed/([a-zA-Z0-9_-]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        return None
    
    @staticmethod
    def get_embed_url(video_id: str) -> str:
        """Get YouTube embed URL"""
        return f"https://www.youtube.com/embed/{video_id}?autoplay=1&loop=1&playlist={video_id}"

class TaskManager:
    """Manages study tasks and to-do lists"""
    
    @staticmethod
    def create_task(user_id: int, title: str, description: str = None,
                   estimated_time: int = None, priority: str = 'medium',
                   due_date: datetime = None, study_session_id: int = None) -> Task:
        """Create a new task"""
        task = Task(
            user_id=user_id,
            study_session_id=study_session_id,
            title=title,
            description=description,
            estimated_time=estimated_time,
            priority=priority,
            due_date=due_date,
            status='pending'
        )
        
        db.session.add(task)
        db.session.commit()
        return task
    
    @staticmethod
    def update_task_status(task_id: int, status: str, actual_time: int = None) -> bool:
        """Update task status"""
        task = Task.query.get(task_id)
        if task:
            task.status = status
            if actual_time:
                task.actual_time = actual_time
            if status == 'completed':
                task.completed_at = datetime.utcnow()
            db.session.commit()
            return True
        return False
    
    @staticmethod
    def get_user_tasks(user_id: int, status: str = None) -> List[Task]:
        """Get user tasks, optionally filtered by status"""
        query = Task.query.filter_by(user_id=user_id)
        if status:
            query = query.filter_by(status=status)
        return query.order_by(Task.created_at.desc()).all()

# Routes
@study_room.route('/')
@login_required
def index():
    """Study room main page"""
    # Get active study session
    active_session = StudySession.query.filter_by(
        user_id=g.user.id, 
        status='active'
    ).first()
    
    # Get pending tasks
    pending_tasks = TaskManager.get_user_tasks(g.user.id, 'pending')
    
    # Get recent sessions
    recent_sessions = StudySession.query.filter_by(
        user_id=g.user.id
    ).order_by(StudySession.start_time.desc()).limit(5).all()
    
    return render_template('study_room/index.html',
                         active_session=active_session,
                         pending_tasks=pending_tasks,
                         recent_sessions=recent_sessions)

@study_room.route('/start', methods=['GET', 'POST'])
@login_required
def start_session():
    """Start a new study session"""
    if request.method == 'POST':
        title = request.form.get('title', '').strip()
        description = request.form.get('description', '').strip()
        planned_duration = request.form.get('planned_duration', type=int)
        background_audio = request.form.get('background_audio', '').strip()
        background_theme = request.form.get('background_theme', 'default')
        
        # Validation
        if not title:
            flash('Session title is required.', 'danger')
            return render_template('study_room/start_session.html')
        
        if not planned_duration or planned_duration <= 0:
            flash('Please enter a valid duration.', 'danger')
            return render_template('study_room/start_session.html')
        
        # Validate YouTube URL if provided
        if background_audio and not YouTubeAudioManager.validate_youtube_url(background_audio):
            flash('Please enter a valid YouTube URL.', 'warning')
            background_audio = None
        
        # Check for existing active session
        existing_session = StudySession.query.filter_by(
            user_id=g.user.id,
            status='active'
        ).first()
        
        if existing_session:
            flash('You already have an active study session. Please complete it first.', 'warning')
            return redirect(url_for('study_room.session_view', session_id=existing_session.id))
        
        # Create new session
        session = TimerManager.create_study_session(
            user_id=g.user.id,
            title=title,
            planned_duration=planned_duration,
            description=description,
            background_audio=background_audio,
            background_theme=background_theme
        )
        
        flash('Study session started successfully!', 'success')
        return redirect(url_for('study_room.session_view', session_id=session.id))
    
    return render_template('study_room/start_session.html')

@study_room.route('/session/<int:session_id>')
@login_required
def session_view(session_id):
    """View active study session"""
    session = StudySession.query.get_or_404(session_id)
    
    # Check ownership
    if session.user_id != g.user.id:
        flash('Access denied.', 'danger')
        return redirect(url_for('study_room.index'))
    
    # Get session tasks
    session_tasks = Task.query.filter_by(study_session_id=session_id).all()
    
    # Prepare YouTube embed URL if audio is provided
    youtube_embed_url = None
    if session.background_audio:
        video_id = YouTubeAudioManager.extract_video_id(session.background_audio)
        if video_id:
            youtube_embed_url = YouTubeAudioManager.get_embed_url(video_id)
    
    return render_template('study_room/session.html',
                         session=session,
                         tasks=session_tasks,
                         youtube_embed_url=youtube_embed_url)

@study_room.route('/session/<int:session_id>/pause', methods=['POST'])
@login_required
def pause_session(session_id):
    """Pause study session"""
    session = StudySession.query.get_or_404(session_id)
    
    if session.user_id != g.user.id:
        return jsonify({'success': False, 'message': 'Access denied'})
    
    success = TimerManager.pause_session(session_id)
    return jsonify({'success': success})

@study_room.route('/session/<int:session_id>/resume', methods=['POST'])
@login_required
def resume_session(session_id):
    """Resume study session"""
    session = StudySession.query.get_or_404(session_id)
    
    if session.user_id != g.user.id:
        return jsonify({'success': False, 'message': 'Access denied'})
    
    success = TimerManager.resume_session(session_id)
    return jsonify({'success': success})

@study_room.route('/session/<int:session_id>/complete', methods=['POST'])
@login_required
def complete_session(session_id):
    """Complete study session"""
    session = StudySession.query.get_or_404(session_id)
    
    if session.user_id != g.user.id:
        return jsonify({'success': False, 'message': 'Access denied'})
    
    actual_duration = request.json.get('actual_duration', 0)
    success = TimerManager.complete_session(session_id, actual_duration)
    
    if success:
        flash('Study session completed successfully!', 'success')
    
    return jsonify({'success': success})

@study_room.route('/tasks')
@login_required
def tasks():
    """Task management page"""
    status_filter = request.args.get('status')
    user_tasks = TaskManager.get_user_tasks(g.user.id, status_filter)
    
    return render_template('study_room/tasks.html', 
                         tasks=user_tasks, 
                         status_filter=status_filter)

@study_room.route('/tasks/create', methods=['POST'])
@login_required
def create_task():
    """Create a new task"""
    title = request.form.get('title', '').strip()
    description = request.form.get('description', '').strip()
    estimated_time = request.form.get('estimated_time', type=int)
    priority = request.form.get('priority', 'medium')
    due_date_str = request.form.get('due_date')
    
    if not title:
        flash('Task title is required.', 'danger')
        return redirect(url_for('study_room.tasks'))
    
    due_date = None
    if due_date_str:
        try:
            due_date = datetime.strptime(due_date_str, '%Y-%m-%d')
        except ValueError:
            flash('Invalid due date format.', 'warning')
    
    task = TaskManager.create_task(
        user_id=g.user.id,
        title=title,
        description=description,
        estimated_time=estimated_time,
        priority=priority,
        due_date=due_date
    )
    
    flash('Task created successfully!', 'success')
    return redirect(url_for('study_room.tasks'))

@study_room.route('/tasks/<int:task_id>/update', methods=['POST'])
@login_required
def update_task(task_id):
    """Update task status"""
    task = Task.query.get_or_404(task_id)
    
    if task.user_id != g.user.id:
        return jsonify({'success': False, 'message': 'Access denied'})
    
    status = request.json.get('status')
    actual_time = request.json.get('actual_time')
    
    success = TaskManager.update_task_status(task_id, status, actual_time)
    return jsonify({'success': success})
