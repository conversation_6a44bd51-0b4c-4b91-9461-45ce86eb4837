{% extends 'base.html' %}

{% block title %}Session Complete{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header bg-success text-white">
            <h1 class="mb-0">Learning Session Complete!</h1>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-3">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Session Summary</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Words Reviewed
                                    <span class="badge bg-primary rounded-pill">{{ stats.words_reviewed }}</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Words Learned
                                    <span class="badge bg-success rounded-pill">{{ stats.words_learned }}</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Completion Rate
                                    <span class="badge bg-info rounded-pill">{{ stats.completion_rate }}%</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Learning Rate
                                    <span class="badge bg-warning rounded-pill">{{ stats.learning_rate }}%</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Your Progress</h5>
                        </div>
                        <div class="card-body">
                            <div class="progress mb-3" style="height: 25px;">
                                <div class="progress-bar bg-success" role="progressbar" 
                                     style="width: {{ stats.learning_rate }}%;" 
                                     aria-valuenow="{{ stats.learning_rate }}" 
                                     aria-valuemin="0" 
                                     aria-valuemax="100">
                                    {{ stats.learning_rate }}% Words Learned
                                </div>
                            </div>
                            <div class="progress" style="height: 25px;">
                                <div class="progress-bar bg-info" role="progressbar" 
                                     style="width: {{ stats.completion_rate }}%;" 
                                     aria-valuenow="{{ stats.completion_rate }}" 
                                     aria-valuemin="0" 
                                     aria-valuemax="100">
                                    {{ stats.completion_rate }}% Completed
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-4">
                <h4>What's Next?</h4>
                <div class="row mt-3">
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-play-circle fs-1 text-primary mb-3"></i>
                                <h5>Continue Learning</h5>
                                <p>Start another session to learn more words.</p>
                                <a href="{{ url_for('learning_session') }}" class="btn btn-primary">
                                    New Session
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-book fs-1 text-success mb-3"></i>
                                <h5>Review Learned Words</h5>
                                <p>See all the words you've mastered.</p>
                                <a href="{{ url_for('learned_words') }}" class="btn btn-success">
                                    My Learned Words
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-lightbulb fs-1 text-warning mb-3"></i>
                                <h5>Generate a Story</h5>
                                <p>Create a story with your vocabulary words.</p>
                                <a href="{{ url_for('story_generator') }}" class="btn btn-warning">
                                    Story Generator
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <a href="{{ url_for('index') }}" class="btn btn-secondary">
                <i class="bi bi-house"></i> Back to Home
            </a>
            <a href="{{ url_for('dashboard') }}" class="btn btn-info float-end">
                <i class="bi bi-graph-up"></i> View Dashboard
            </a>
        </div>
    </div>
</div>
{% endblock %}