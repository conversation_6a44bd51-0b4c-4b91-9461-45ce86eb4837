:root {
    /* Light Theme Colors */
    --primary-color: #ff7722;
    --primary-light: #ff9955;
    --primary-dark: #e65100;
    --secondary-color: #2e7d32;
    --secondary-light: #60ad5e;
    --secondary-dark: #005005;
    --accent-color: #673ab7;
    --success-color: #2e7d32;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --gray-color: #6c757d;
    --gray-light: #e9ecef;
    --dark-surface: #2d2d2d;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    
    /* Typography */
    --font-family-body: 'Inter', sans-serif;
    --font-family-heading: 'Poppins', sans-serif;
    
    /* Shadows */
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --card-shadow-hover: 0 8px 15px rgba(0, 0, 0, 0.15);
    
    /* Border radius */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 1rem;
    
    /* Transitions */
    --transition-speed: 0.3s ease;
}

[data-bs-theme="dark"] {
    /* Dark Theme Colors */
    --primary-color: #ff7722;
    --primary-light: #ff9955;
    --primary-dark: #e65100;
    --secondary-color: #4caf50;
    --secondary-light: #80e27e;
    --secondary-dark: #087f23;
    --accent-color: #bb86fc;
    --success-color: #4caf50;
    --warning-color: #ffab40;
    --danger-color: #ff5252;
    --gray-color: #9e9e9e;
    --gray-light: #424242;
    --dark-surface: #1e1e1e;
    --light-color: #e0e0e0;
    --dark-color: #121212;
    
    /* Shadows for dark theme */
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    --card-shadow-hover: 0 8px 15px rgba(0, 0, 0, 0.4);
}

/*--------------------------------------------------------------
# Base Styles
--------------------------------------------------------------*/
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-body);
    background-color: var(--light-color);
    color: var(--dark-color);
    transition: background-color var(--transition-speed), color var(--transition-speed);
    line-height: 1.6;
    overflow-x: hidden;
}

[data-bs-theme="dark"] body {
    background-color: #121212;
    color: rgba(255, 255, 255, 0.9);
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-heading);
    font-weight: 600;
}

.text-gradient {
    background: linear-gradient(to right, var(--primary-color), var(--primary-light));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    display: inline-block;
}

[data-bs-theme="dark"] .text-gradient {
    background: linear-gradient(to right, var(--primary-color), var(--primary-light));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.main-content {
    min-height: calc(100vh - 300px);
}

/*--------------------------------------------------------------
# Typography
--------------------------------------------------------------*/
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-heading);
    font-weight: 600;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--gray-color);
    max-width: 700px;
    margin: 0 auto;
}

/*--------------------------------------------------------------
# Navbar
--------------------------------------------------------------*/
.navbar {
    background: linear-gradient(135deg, #1a1a1a, #000000);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    padding: 0.8rem 1rem;
    border-radius: 0 0 var(--border-radius-sm) var(--border-radius-sm);
    border-bottom: 1px solid rgba(255, 119, 34, 0.3);
}

.navbar-brand {
    font-weight: 700;
    color: var(--primary-color) !important;
    font-size: 1.4rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.navbar-brand i {
    font-size: 1.6rem;
}

.nav-link {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-speed);
    margin: 0 0.2rem;
}

.nav-link:hover, .nav-link:focus {
    color: var(--primary-color) !important;
    background-color: rgba(0, 0, 0, 0.3);
    transform: translateY(-2px);
}

.nav-link.active {
    background-color: rgba(255, 119, 34, 0.2);
    color: var(--primary-color) !important;
}

.navbar .dropdown-menu {
    background-color: #121212;
    border: 1px solid rgba(255, 119, 34, 0.2);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.5);
    border-radius: var(--border-radius-sm);
    padding: 0.5rem;
    margin-top: 0.5rem;
}

.navbar .dropdown-item {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.6rem 1rem;
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-speed);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.navbar .dropdown-item:hover {
    background-color: rgba(255, 119, 34, 0.1);
    color: var(--primary-color);
    transform: translateX(5px);
}

.navbar-toggler {
    border: none;
    color: var(--primary-color);
    font-size: 1.5rem;
}

.navbar-toggler:focus {
    box-shadow: none;
}

.btn-add {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
    color: white;
    border: none;
    font-weight: 500;
    padding: 0.6rem 1.2rem;
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-speed);
}

.btn-add:hover {
    background: linear-gradient(135deg, var(--secondary-light), var(--secondary-color));
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-theme-toggle {
    background-color: rgba(255, 255, 255, 0.15);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-speed);
}

.btn-theme-toggle:hover {
    background-color: rgba(255, 255, 255, 0.25);
    transform: rotate(15deg);
}

/* Hero Section */
.hero-section {
    padding: 4rem 0 2rem;
    border-radius: var(--border-radius-lg);
    margin-bottom: 3rem;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(30, 30, 30, 0.8));
    border: 1px solid rgba(255, 119, 34, 0.1);
}

.hero-section::before {
    content: '';
    position: absolute;
    top: -50px;
    right: -50px;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 119, 34, 0.3), transparent 70%);
    z-index: 0;
}

.hero-section::after {
    content: '';
    position: absolute;
    bottom: -50px;
    left: -50px;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(46, 125, 50, 0.2), transparent 70%);
    z-index: 0;
}

.hero-section h1 {
    color: white;
    font-weight: 700;
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 1;
}

.hero-section h1::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, var(--primary-color), var(--primary-light));
    border-radius: 2px;
}

.hero-section .lead {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
    position: relative;
    z-index: 1;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius-sm);
    padding: 0.6rem 1.2rem;
    font-weight: 500;
    transition: all var(--transition-speed);
}

.btn-lg {
    padding: 0.8rem 1.8rem;
    font-size: 1.1rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border: none;
}

.btn-primary:hover, .btn-primary:focus {
    background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(230, 92, 0, 0.3);
}

.btn-outline-success {
    color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-outline-success:hover, .btn-outline-success:focus {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(46, 125, 50, 0.2);
}

.btn-success {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
    border: none;
}

.btn-success:hover, .btn-success:focus {
    background: linear-gradient(135deg, var(--secondary-light), var(--secondary-color));
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(46, 125, 50, 0.3);
}

/* Stats Cards */
.stats-wrapper {
    padding: 1rem;
    border-radius: var(--border-radius-lg);
    background-color: rgba(255, 255, 255, 0.8);
    box-shadow: var(--card-shadow);
}

[data-bs-theme="dark"] .stats-wrapper {
    background-color: rgba(30, 30, 30, 0.8);
}

.stats-container {
    padding: 1rem;
}

.stat-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem 1rem;
    border-radius: var(--border-radius-md);
    background-color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all var(--transition-speed);
    height: 100%;
    text-align: center;
}

[data-bs-theme="dark"] .stat-card {
    background-color: var(--dark-surface);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.total-words .stat-icon {
    color: var(--primary-color);
    background-color: rgba(255, 119, 34, 0.1);
}

.learned-words .stat-icon {
    color: var(--success-color);
    background-color: rgba(46, 125, 50, 0.1);
}

.remaining-words .stat-icon {
    color: var(--accent-color);
    background-color: rgba(103, 58, 183, 0.1);
}

.stat-content {
    width: 100%;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    line-height: 1;
}

.total-words .stat-number {
    color: var(--primary-color);
}

.learned-words .stat-number {
    color: var(--success-color);
}

.remaining-words .stat-number {
    color: var(--accent-color);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--gray-color);
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Progress bar */
.progress {
    height: 0.6rem;
    border-radius: 1rem;
    background-color: var(--gray-light);
    overflow: hidden;
    margin-top: 0.5rem;
    position: relative;
}

.progress-bar {
    border-radius: 1rem;
    background: linear-gradient(to right, var(--secondary-dark), var(--secondary-light));
}

.learned-words .progress-bar {
    background: linear-gradient(to right, var(--success-color), #81c784);
}

.remaining-words .progress-bar {
    background: linear-gradient(to right, var(--accent-color), #9575CD);
}

.progress-text {
    display: block;
    font-size: 0.8rem;
    color: var(--gray-color);
    margin-top: 0.25rem;
    text-align: right;
}

/* Responsive adjustments for stats cards */
@media (max-width: 992px) {
    .stat-card {
        padding: 1.25rem 0.75rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .stat-icon {
        font-size: 1.75rem;
        width: 50px;
        height: 50px;
    }
}

@media (max-width: 768px) {
    .stats-container .row {
        flex-direction: row;
    }
    
    .stats-container .col-md-4 {
        width: 33.333%;
        padding: 0 0.5rem;
    }
    
    .stat-card {
        padding: 1rem 0.5rem;
    }
    
    .stat-number {
        font-size: 1.75rem;
    }
    
    .stat-label {
        font-size: 0.8rem;
    }
    
    .stat-icon {
        font-size: 1.5rem;
        width: 45px;
        height: 45px;
        margin-bottom: 0.75rem;
    }
}

@media (max-width: 576px) {
    .stats-container .col-md-4 {
        width: 100%;
        margin-bottom: 1rem;
    }
    
    .stat-card {
        flex-direction: row;
        text-align: left;
        padding: 1rem;
    }
    
    .stat-icon {
        margin-bottom: 0;
        margin-right: 1rem;
    }
    
    .stat-content {
        flex: 1;
    }
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius-md);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    transition: all var(--transition-speed);
    overflow: hidden;
    background-color: white;
}

[data-bs-theme="dark"] .card {
    background-color: #1a1a1a;
    border: 1px solid rgba(255, 119, 34, 0.1);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.4);
}

.card-header {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.8), rgba(30, 30, 30, 0.6));
    border-bottom: 1px solid rgba(255, 119, 34, 0.2);
    padding: 1.2rem 1.5rem;
    display: flex;
    align-items: center;
}

[data-bs-theme="dark"] .card-header {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.8), rgba(30, 30, 30, 0.6));
    border-bottom: 1px solid rgba(255, 119, 34, 0.2);
}

.card-header h5 {
    margin-bottom: 0;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
}

.card-header i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: rgba(0, 0, 0, 0.05);
    border-top: 1px solid rgba(255, 119, 34, 0.1);
    padding: 1rem 1.5rem;
}

[data-bs-theme="dark"] .card-footer {
    background-color: rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(255, 119, 34, 0.1);
}

/* Word Cards */
.word-card {
    border-radius: var(--border-radius-sm);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    transition: all var(--transition-speed);
    background-color: white;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

[data-bs-theme="dark"] .word-card {
    background-color: var(--dark-surface);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.word-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.word-card-body {
    padding: 1.5rem;
    flex-grow: 1;
}

.word-title {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.8rem;
    font-size: 1.2rem;
    position: relative;
    display: inline-block;
}

.word-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(to right, var(--primary-color), var(--primary-light));
    border-radius: 1.5px;
}

.word-definition {
    color: var(--gray-color);
    font-size: 0.95rem;
    margin-bottom: 0;
    line-height: 1.6;
}

.word-card-footer {
    padding: 1rem 1.5rem;
    background-color: rgba(0, 0, 0, 0.02);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Footer */
.footer {
    margin-top: 3rem;
    padding: 2rem 0;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    transition: background-color var(--transition-speed), border-color var(--transition-speed);
}

[data-bs-theme="dark"] .footer {
    background-color: #1a1a1a;
    border-top: 1px solid #333;
    color: var(--light-color);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
    font-size: 1.2rem;
}

.footer-logo svg {
    width: 24px;
    height: 24px;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: var(--gray-color);
    text-decoration: none;
    transition: color var(--transition-speed);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-social {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(255, 119, 34, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    transition: all var(--transition-speed);
}

.social-icon:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-3px);
}

.footer-bottom {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    text-align: center;
    color: var(--gray-color);
    font-size: 0.9rem;
}

[data-bs-theme="dark"] .footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.05);
}

/* Story content */
.story-content {
    font-size: 1.1rem;
    line-height: 1.7;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    white-space: pre-line;
}

.story-content h1, .story-content h2, .story-content h3 {
    color: #0d6efd;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
}

/* Print styles */
@media print {
    nav, footer, .btn, form {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: none;
    }
    
    .card-header {
        background-color: white;
        border-bottom: 1px solid #000;
    }
    
    .story-content {
        background-color: white;
        padding: 0;
    }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-header {
        flex-direction: column;
        align-items: flex-start !important;
    }
    
    .card-header > div {
        margin-top: 0.5rem;
    }
}

/* Library card stats */
.library-stats .stat-card {
    padding: 0.5rem;
    height: auto;
    min-height: 80px;
}

.library-stats .stat-icon {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    width: 30px;
    height: 30px;
}

.library-stats .stat-number {
    font-size: 1.5rem;
    margin-bottom: 0;
}

.library-stats .stat-label {
    font-size: 0.7rem;
    margin-bottom: 0;
}

/* Improve responsiveness of library cards */
@media (max-width: 767px) {
    .library-stats .row {
        margin: 0 -5px;
    }
    
    .library-stats .col-4 {
        padding: 0 5px;
    }
    
    .library-stats .stat-card {
        padding: 0.25rem;
    }
    
    .library-stats .stat-icon {
        display: none;
    }
    
    .library-stats .stat-number {
        font-size: 1.2rem;
    }
    
    .library-stats .stat-label {
        font-size: 0.6rem;
    }
}
