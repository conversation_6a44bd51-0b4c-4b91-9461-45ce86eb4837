{% extends "base.html" %}

{% block title %}Backup Codes - G<PERSON> Vocabulary Learner{% endblock %}

{% block extra_css %}
<style>
    .backup-codes-card {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        border-radius: 20px;
        color: white;
    }
    
    .code-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
        margin: 2rem 0;
    }
    
    .backup-code {
        background: rgba(255, 255, 255, 0.9);
        color: #333;
        padding: 1rem;
        border-radius: 10px;
        text-align: center;
        font-family: 'Courier New', monospace;
        font-size: 1.1rem;
        font-weight: bold;
        letter-spacing: 2px;
        border: 2px solid transparent;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .backup-code:hover {
        border-color: var(--primary-color);
        transform: translateY(-2px);
    }
    
    .backup-code.copied {
        background: rgba(40, 167, 69, 0.9);
        color: white;
        border-color: #28a745;
    }
    
    .security-warning {
        background: rgba(255, 193, 7, 0.1);
        border: 1px solid rgba(255, 193, 7, 0.3);
        border-radius: 10px;
        padding: 1.5rem;
        margin: 2rem 0;
    }
    
    .download-section {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        padding: 1.5rem;
        margin: 2rem 0;
    }
    
    .print-only {
        display: none;
    }
    
    @media print {
        .no-print {
            display: none !important;
        }
        
        .print-only {
            display: block !important;
        }
        
        .backup-codes-card {
            background: white !important;
            color: black !important;
        }
        
        .backup-code {
            background: #f8f9fa !important;
            color: black !important;
            border: 1px solid #dee2e6 !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card backup-codes-card">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="bi bi-shield-check" style="font-size: 4rem; color: rgba(255, 255, 255, 0.8);"></i>
                        <h2 class="mt-3">Two-Factor Authentication Backup Codes</h2>
                        <p class="mb-0">Save these codes in a secure location</p>
                    </div>
                    
                    <div class="security-warning">
                        <h5><i class="bi bi-exclamation-triangle"></i> Important Security Information</h5>
                        <ul class="mb-0">
                            <li><strong>Each code can only be used once</strong> - they will be disabled after use</li>
                            <li><strong>Keep these codes secure</strong> - treat them like passwords</li>
                            <li><strong>Don't share these codes</strong> with anyone</li>
                            <li><strong>Store them safely</strong> - in a password manager or printed in a secure location</li>
                            <li><strong>Generate new codes</strong> if you suspect they've been compromised</li>
                        </ul>
                    </div>
                    
                    <!-- Print Header (only visible when printing) -->
                    <div class="print-only text-center mb-4">
                        <h3>GRE Vocabulary Learner - 2FA Backup Codes</h3>
                        <p>Generated on: {{ moment().format('MMMM Do YYYY, h:mm:ss a') }}</p>
                        <p>Account: {{ current_user.email }}</p>
                        <hr>
                    </div>
                    
                    <!-- Backup Codes Grid -->
                    <div class="code-grid">
                        {% for code in backup_codes %}
                        <div class="backup-code" onclick="copyCode(this)" title="Click to copy">
                            {{ code }}
                        </div>
                        {% endfor %}
                    </div>
                    
                    <!-- Download and Print Section -->
                    <div class="download-section no-print">
                        <h5><i class="bi bi-download"></i> Save Your Codes</h5>
                        <p class="mb-3">Choose how you'd like to save these backup codes:</p>
                        
                        <div class="d-flex flex-wrap gap-3 justify-content-center">
                            <button class="btn btn-outline-light" onclick="downloadCodes()">
                                <i class="bi bi-file-text"></i> Download as Text File
                            </button>
                            <button class="btn btn-outline-light" onclick="printCodes()">
                                <i class="bi bi-printer"></i> Print Codes
                            </button>
                            <button class="btn btn-outline-light" onclick="copyAllCodes()">
                                <i class="bi bi-clipboard"></i> Copy All Codes
                            </button>
                        </div>
                    </div>
                    
                    <!-- Usage Instructions -->
                    <div class="mt-4 no-print">
                        <h5><i class="bi bi-info-circle"></i> How to Use Backup Codes</h5>
                        <ol>
                            <li>When prompted for a 2FA code, click "Use Backup Code" instead</li>
                            <li>Enter one of these backup codes exactly as shown</li>
                            <li>The code will be consumed and cannot be used again</li>
                            <li>Generate new backup codes when you're running low</li>
                        </ol>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-between mt-4 no-print">
                        <a href="{{ url_for('auth.setup_2fa') }}" class="btn btn-outline-light">
                            <i class="bi bi-arrow-left"></i> Back to 2FA Settings
                        </a>
                        <div class="d-flex gap-2">
                            <button class="btn btn-warning" onclick="generateNewCodes()">
                                <i class="bi bi-arrow-clockwise"></i> Generate New Codes
                            </button>
                            <a href="{{ url_for('auth.profile') }}" class="btn btn-success">
                                <i class="bi bi-check-circle"></i> Done
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modal for New Codes -->
<div class="modal fade" id="newCodesModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-arrow-clockwise"></i> Generate New Backup Codes
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Warning:</strong> Generating new backup codes will invalidate all existing codes.
                </div>
                <p>Are you sure you want to generate new backup codes? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="{{ url_for('auth.backup_codes') }}" class="btn btn-warning">Generate New Codes</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Copy individual code to clipboard
function copyCode(element) {
    const code = element.textContent.trim();
    navigator.clipboard.writeText(code).then(() => {
        // Visual feedback
        element.classList.add('copied');
        element.innerHTML = '<i class="bi bi-check"></i> Copied!';
        
        setTimeout(() => {
            element.classList.remove('copied');
            element.textContent = code;
        }, 2000);
    }).catch(err => {
        console.error('Failed to copy: ', err);
        // Fallback for older browsers
        fallbackCopyTextToClipboard(code);
    });
}

// Copy all codes to clipboard
function copyAllCodes() {
    const codes = Array.from(document.querySelectorAll('.backup-code'))
        .map(el => el.textContent.trim())
        .join('\n');
    
    const fullText = `GRE Vocabulary Learner - 2FA Backup Codes
Generated on: ${new Date().toLocaleString()}
Account: {{ current_user.email }}

${codes}

Important: Each code can only be used once. Keep these codes secure.`;
    
    navigator.clipboard.writeText(fullText).then(() => {
        showToast('All backup codes copied to clipboard!', 'success');
    }).catch(err => {
        console.error('Failed to copy: ', err);
        fallbackCopyTextToClipboard(fullText);
    });
}

// Download codes as text file
function downloadCodes() {
    const codes = Array.from(document.querySelectorAll('.backup-code'))
        .map(el => el.textContent.trim())
        .join('\n');
    
    const content = `GRE Vocabulary Learner - 2FA Backup Codes
Generated on: ${new Date().toLocaleString()}
Account: {{ current_user.email }}

Backup Codes:
${codes}

Important Security Information:
- Each code can only be used once
- Keep these codes secure and private
- Don't share these codes with anyone
- Store them in a safe location
- Generate new codes if compromised

How to use:
1. When prompted for 2FA, click "Use Backup Code"
2. Enter one of these codes exactly as shown
3. The code will be consumed after use
`;
    
    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `gre-vocab-2fa-backup-codes-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    showToast('Backup codes downloaded successfully!', 'success');
}

// Print codes
function printCodes() {
    window.print();
}

// Generate new codes confirmation
function generateNewCodes() {
    const modal = new bootstrap.Modal(document.getElementById('newCodesModal'));
    modal.show();
}

// Fallback copy function for older browsers
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showToast('Copied to clipboard!', 'success');
        } else {
            showToast('Failed to copy to clipboard', 'error');
        }
    } catch (err) {
        showToast('Failed to copy to clipboard', 'error');
    }
    
    document.body.removeChild(textArea);
}

// Show toast notification
function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

// Add moment.js-like functionality for date formatting
function moment() {
    return {
        format: function(format) {
            const now = new Date();
            const options = { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                hour: 'numeric',
                minute: 'numeric',
                second: 'numeric',
                hour12: true
            };
            return now.toLocaleDateString('en-US', options);
        }
    };
}
</script>
{% endblock %}
