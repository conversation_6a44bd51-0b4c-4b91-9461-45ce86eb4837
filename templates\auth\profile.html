{% extends "base.html" %}

{% block title %}My Profile - GRE Vocabulary Learner{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Profile Information</h4>
                </div>
                <div class="card-body text-center">
                    {% if user.profile_picture %}
                        <img src="{{ url_for('static', filename=user.profile_picture) }}" alt="Profile Picture" 
                             class="rounded-circle img-thumbnail mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                    {% else %}
                        <div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center mx-auto mb-3" 
                             style="width: 150px; height: 150px;">
                            <span class="display-4 text-white">{{ user.username[0].upper() }}</span>
                        </div>
                    {% endif %}
                    
                    <h3>{{ user.get_full_name() }}</h3>
                    <p class="text-muted">@{{ user.username }}</p>
                    <p><i class="bi bi-envelope"></i> {{ user.email }}</p>
                    <p><i class="bi bi-calendar"></i> Member since {{ user.created_at.strftime('%b %d, %Y') }}</p>
                    
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editProfileModal">
                            <i class="bi bi-pencil"></i> Edit Profile
                        </button>
                        <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-key"></i> Change Password
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Learning Statistics</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 text-center mb-3">
                            <div class="p-3 rounded bg-light">
                                <h2 class="text-primary">{{ stats.total_words }}</h2>
                                <p class="mb-0">Total Words</p>
                            </div>
                        </div>
                        <div class="col-md-4 text-center mb-3">
                            <div class="p-3 rounded bg-light">
                                <h2 class="text-success">{{ stats.learned_words }}</h2>
                                <p class="mb-0">Words Learned</p>
                            </div>
                        </div>
                        <div class="col-md-4 text-center mb-3">
                            <div class="p-3 rounded bg-light">
                                <h2 class="text-info">{{ stats.sessions }}</h2>
                                <p class="mb-0">Learning Sessions</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h5>Recent Activity</h5>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i> Activity tracking will be available in a future update.
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">My Libraries</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> Library management will be available in a future update.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Profile Modal -->
<div class="modal fade" id="editProfileModal" tabindex="-1" aria-labelledby="editProfileModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="{{ url_for('auth.profile') }}" enctype="multipart/form-data">
                <div class="modal-header">
                    <h5 class="modal-title" id="editProfileModalLabel">Edit Profile</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="first_name" class="form-label">First Name</label>
                        <input type="text" class="form-control {% if errors.get('first_name') %}is-invalid{% endif %}" 
                               id="first_name" name="first_name" value="{{ user.first_name or '' }}">
                        {% if errors.get('first_name') %}
                            <div class="invalid-feedback">{{ errors.get('first_name') }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="last_name" class="form-label">Last Name</label>
                        <input type="text" class="form-control {% if errors.get('last_name') %}is-invalid{% endif %}" 
                               id="last_name" name="last_name" value="{{ user.last_name or '' }}">
                        {% if errors.get('last_name') %}
                            <div class="invalid-feedback">{{ errors.get('last_name') }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control {% if errors.get('email') %}is-invalid{% endif %}" 
                               id="email" name="email" value="{{ user.email }}" required>
                        {% if errors.get('email') %}
                            <div class="invalid-feedback">{{ errors.get('email') }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="profile_picture" class="form-label">Profile Picture</label>
                        <input type="file" class="form-control" id="profile_picture" name="profile_picture" accept="image/*">
                        <div class="form-text">Upload a new profile picture (optional)</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
