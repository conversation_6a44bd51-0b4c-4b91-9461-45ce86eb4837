{% extends 'base.html' %}

{% block title %}Search Words{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h2 class="mb-0">Search Words</h2>
    </div>
    <div class="card-body">
        <form method="get" action="{{ url_for('search') }}" class="mb-4">
            <div class="input-group">
                <input type="text" name="word" class="form-control" placeholder="Enter a word to search" value="{{ word or '' }}" required autofocus>
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-search"></i> Search
                </button>
            </div>
            <div class="form-text mt-2">
                <small>Press Alt+S or Ctrl+/ anywhere on the site to quickly access search.</small>
            </div>
        </form>

        <div class="card-body">
            {% if result %}
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="mb-0">{{ result[0] }}</h3>
                    <div>
                        {% if result[3] %}
                        <span class="badge bg-success">Learned</span>
                        <form method="post" action="{{ url_for('mark_word') }}" class="d-inline">
                            <input type="hidden" name="word" value="{{ result[0] }}">
                            <input type="hidden" name="action" value="unlearned">
                            <input type="hidden" name="redirect_url" value="{{ url_for('search', word=result[0]) }}">
                            <button type="submit" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-x-circle"></i> Mark as Unlearned
                            </button>
                        </form>
                        {% else %}
                        <span class="badge bg-warning">Not Learned</span>
                        <form method="post" action="{{ url_for('mark_word') }}" class="d-inline">
                            <input type="hidden" name="word" value="{{ result[0] }}">
                            <input type="hidden" name="action" value="learned">
                            <input type="hidden" name="redirect_url" value="{{ url_for('search', word=result[0]) }}">
                            <button type="submit" class="btn btn-sm btn-outline-success">
                                <i class="bi bi-check-circle"></i> Mark as Learned
                            </button>
                        </form>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <h5>Definition</h5>
                    <p>{{ result[1] }}</p>

                    {% if result[2] %}
                    <h5>Source</h5>
                    <p>{{ result[2] }}</p>
                    {% endif %}

                    {% if phonetics %}
                    <h5>Pronunciation</h5>
                    <div class="mb-3">
                        {% for phonetic in phonetics %}
                        {% if phonetic.text %}
                        <span class="badge bg-light text-dark me-2">{{ phonetic.text }}</span>
                        {% endif %}
                        {% if phonetic.audio %}
                        <button class="btn btn-sm btn-outline-primary play-audio" data-audio="{{ phonetic.audio }}">
                            <i class="bi bi-volume-up"></i> Listen
                        </button>
                        {% endif %}
                        {% endfor %}
                        
                        <!-- Add direct pronunciation button if no phonetics available -->
                        {% if not phonetics or not phonetics[0].audio %}
                        <button class="btn btn-sm btn-outline-primary play-pronunciation" data-word="{{ result[0] }}">
                            <i class="bi bi-volume-up"></i> Listen
                        </button>
                        {% endif %}
                    </div>
                    {% endif %}

                    {% if examples %}
                    <h5>Examples</h5>
                    <ul class="list-group">
                        {% for example in examples %}
                        <li class="list-group-item">{{ example }}</li>
                        {% endfor %}
                    </ul>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <a href="https://www.google.com/search?q=define+{{ result[0] }}" target="_blank" class="btn btn-outline-primary">
                        <i class="bi bi-google"></i> Search on Google
                    </a>
                </div>
            </div>
            {% elif word %}
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle"></i> Word "{{ word }}" not found.
                <a href="{{ url_for('add_word') }}?word={{ word }}" class="alert-link">Add it to the database?</a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const audioButtons = document.querySelectorAll('.play-audio');
        audioButtons.forEach(button => {
            button.addEventListener('click', function() {
                const audioUrl = this.getAttribute('data-audio');
                if (audioUrl) {
                    const audio = new Audio(audioUrl);
                    audio.play();
                }
            });
        });
    });
</script>
{% endblock %}
